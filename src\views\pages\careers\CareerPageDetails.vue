<template>
  <div>
    <v-row>
      <div class="d-flex align-center justify-center">
        <v-icon
          color="primary"
          class="fas fa-angle-left ml-7 cursor-pointer"
          size="large"
          @click="navigateToCareersPage"
        ></v-icon>
      </div>
      <p class="headingsText font-weight-bold text-h6 text-primary">
        {{ orgName }}
      </p>
    </v-row>
    <v-row class="bg-primary d-flex justify-space-around">
      <v-card color="primary" class="pa-4 text-center">
        <div class="text-h5 text-center">{{ jobDetails.Job_Post_Name }}</div>
        <div class="d-flex mt-2 justify-center align-center text-white">
          {{ jobLocation }}
          <div
            v-if="jobDetails.Experience_Level"
            style="
              background: white;
              border-radius: 50%;
              margin: 0px 5px;
              height: 10px;
              width: 10px;
            "
          ></div>
          {{ jobDetails.Experience_Level }}
        </div>
      </v-card>
    </v-row>
    <v-row>
      <v-col cols="12" style="padding: 5px 20% 5px 20%">
        <div class="d-flex justify-end align-center">
          <v-btn
            variant="elevated"
            rounded="lg"
            size="large"
            color="primary"
            class="pa-3 mt-2 text-button"
            @click="navigateToUrl(jobDetails.Job_Post_Id)"
          >
            Apply for this job
          </v-btn>
        </div>
        <div style="font-size: 20px" class="mt-10 text-body-1">
          <div
            v-if="!sanitizedJobDescription"
            class="pa-5 d-flex bg-hover justify-center align-center"
            style="margin: 2% 0 10px"
          >
            No job description for this job post
          </div>
          <div v-else ref="editorView" class="quill-editorView"></div>
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import DOMPurify from "dompurify";
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";
export default {
  name: "CareerPageDetails",
  emits: ["close-details-form"],
  data() {
    return {};
  },
  computed: {
    sanitizedJobDescription() {
      let jobDescription = this.jobDetails?.Job_Description
        ? this.convertEmojiCodepointsToEmojis(this.jobDetails.Job_Description)
        : "";
      return DOMPurify.sanitize(jobDescription);
    },
    jobLocation() {
      const { City_Name, State_Name, Country_Name } = this.jobDetails || {};

      return [City_Name, State_Name, Country_Name].filter(Boolean).join(", ");
    },
  },
  props: {
    jobDetails: {
      type: Object,
      required: true,
    },
    orgName: {
      type: String,
      required: true,
    },
  },
  mounted() {
    window.scrollTo(0, 0);
    this.initQuillEditor();
  },
  watch() {},
  methods: {
    initQuillEditor() {
      this.quill = new Quill(this.$refs.editorView, {
        theme: "snow",
      });
      // Set initial font size
      this.setEditorFontSize("16px");
      this.quill.root.innerHTML = this.jobDetails?.Job_Description
        ? this.convertEmojiCodepointsToEmojis(this.jobDetails.Job_Description)
        : "";
      this.hasContent = !!this.quill.getText().trim();

      // Listen for editor text change events
      this.quill.on("text-change", () => {
        this.hasContent = !!this.quill.getText().trim();
      });
      this.quill.enable(false);
    },
    setEditorFontSize(fontSize) {
      const editorElement = this.$refs.editorView.querySelector(".ql-editor");
      if (editorElement) {
        editorElement.style.fontSize = fontSize;
      }
    },
    navigateToUrl(item) {
      window.open(
        this.$store.getters.baseUrl + `v3/job-candidates?jobPostId=${item}`,
        "_blank"
      );
    },
    navigateToCareersPage() {
      this.$emit("close-details-form");
    },
    getPostingDateDifference(postingDate) {
      const postingDateObj = new Date(postingDate);
      const currentDate = new Date();
      const differenceInTime = currentDate.getTime() - postingDateObj.getTime();
      const differenceInDays = differenceInTime / (1000 * 3600 * 24);
      return Math.floor(differenceInDays);
    },
    convertEmojiCodepointsToEmojis(text) {
      return text.replace(/\[EMOJI:([0-9a-f-]+)\]/gi, (match, codePoints) => {
        // Split by dash if there are multiple code points
        const codePointArray = codePoints.split("-");

        // Convert each hex code point back to a character and join them
        const emoji = codePointArray
          .map((hex) => String.fromCodePoint(parseInt(hex, 16)))
          .join("");

        return emoji;
      });
    },
  },
};
</script>
<style scoped>
.headingsText {
  margin: 2%;
}
.quill-editorView {
  height: auto;
}
::v-deep .ql-toolbar.ql-snow {
  display: none !important;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
  border: none;
}
::v-deep .ql-editor {
  padding: 0px;
}
</style>
