<template>
  <!-- This is a utility component/mixin for fixing email content -->
</template>

<script>
/**
 * Email Content Fixer Mixin for Vue.js
 * Usage: Import and add to mixins array in your component
 */

export default {
  name: 'EmailContentFixer',
  methods: {
    /**
     * Main function to fix email content formatting issues
     * @param {string} htmlContent - The original HTML content
     * @returns {string} - Fixed HTML content
     */
    fixEmailContent(htmlContent) {
      if (!htmlContent || typeof htmlContent !== 'string') {
        return htmlContent;
      }

      let fixedContent = htmlContent;

      // Apply all fixes in sequence
      fixedContent = this.removeRichTextEditorArtifacts(fixedContent);
      fixedContent = this.fixListFormatting(fixedContent);
      fixedContent = this.addEmailStyling(fixedContent);
      fixedContent = this.fixGeneralFormatting(fixedContent);

      return fixedContent;
    },

    /**
     * Remove rich text editor artifacts
     */
    removeRichTextEditorArtifacts(content) {
      return content
        // Remove Quill editor UI elements
        .replace(/<span class="ql-ui"[^>]*><\/span>/g, '')
        .replace(/<span class="ql-ui"[^>]*>.*?<\/span>/g, '')
        
        // Remove contenteditable attributes
        .replace(/contenteditable="[^"]*"/g, '')
        
        // Remove empty spans and divs
        .replace(/<span[^>]*><\/span>/g, '')
        .replace(/<div[^>]*><\/div>/g, '')
        
        // Clean up multiple consecutive spaces
        .replace(/\s+/g, ' ')
        .trim();
    },

    /**
     * Fix list formatting issues
     */
    fixListFormatting(content) {
      // Fix ordered lists with center alignment
      content = content.replace(
        /<ol([^>]*)>/g, 
        (match, attributes) => {
          let cleanAttributes = attributes.replace(/style="[^"]*text-align:\s*center[^"]*"/g, '');
          // Remove any existing style and add proper list styling
          cleanAttributes = cleanAttributes.replace(/style="[^"]*"/g, '');
          return `<ol${cleanAttributes} style="text-align: left; margin: 15px 0; padding-left: 30px; list-style-position: outside;">`;
        }
      );

      // Fix unordered lists with center alignment
      content = content.replace(
        /<ul([^>]*)>/g, 
        (match, attributes) => {
          let cleanAttributes = attributes.replace(/style="[^"]*text-align:\s*center[^"]*"/g, '');
          cleanAttributes = cleanAttributes.replace(/style="[^"]*"/g, '');
          return `<ul${cleanAttributes} style="text-align: left; margin: 15px 0; padding-left: 30px; list-style-position: outside;">`;
        }
      );

      // Fix list items with center alignment
      content = content.replace(
        /<li([^>]*)>/g,
        (match, attributes) => {
          let cleanAttributes = attributes.replace(/style="[^"]*text-align:\s*center[^"]*"/g, '');
          cleanAttributes = cleanAttributes.replace(/style="[^"]*"/g, '');
          return `<li${cleanAttributes} style="margin: 8px 0; padding: 4px 0; text-align: left; line-height: 1.5;">`;
        }
      );

      return content;
    },

    /**
     * Add proper email styling
     */
    addEmailStyling(content) {
      // Email-safe CSS styles
      const emailStyles = `
        <style type="text/css">
          /* Email client compatibility styles */
          .email-wrapper { 
            max-width: 600px; 
            margin: 0 auto; 
            font-family: Arial, Helvetica, sans-serif; 
            line-height: 1.6; 
            color: #333333;
          }
          .email-wrapper p { 
            margin: 12px 0; 
            padding: 0;
          }
          .email-wrapper ol, .email-wrapper ul { 
            text-align: left; 
            margin: 15px 0; 
            padding-left: 30px;
            list-style-position: outside;
          }
          .email-wrapper li { 
            margin: 8px 0; 
            padding: 4px 0; 
            text-align: left; 
            line-height: 1.5;
          }
          .email-wrapper a { 
            color: #0066cc; 
            text-decoration: underline; 
          }
          .email-wrapper strong { 
            font-weight: bold; 
          }
          .email-wrapper img { 
            max-width: 100%; 
            height: auto; 
            display: block;
          }
          
          /* Outlook specific fixes */
          table { 
            border-collapse: collapse; 
            mso-table-lspace: 0pt; 
            mso-table-rspace: 0pt; 
          }
          
          /* Mobile responsive */
          @media only screen and (max-width: 600px) {
            .email-wrapper { 
              width: 100% !important; 
              padding: 10px !important; 
            }
            .email-wrapper ol, .email-wrapper ul { 
              padding-left: 20px !important; 
            }
          }
        </style>
      `;

      // Wrap content in email wrapper if not already wrapped
      const hasWrapper = content.includes('email-wrapper') || content.includes('max-width');
      
      if (!hasWrapper) {
        content = `
          ${emailStyles}
          <div class="email-wrapper">
            ${content}
          </div>
        `;
      } else if (!content.includes('<style>')) {
        content = emailStyles + content;
      }

      return content;
    },

    /**
     * Fix general formatting issues
     */
    fixGeneralFormatting(content) {
      return content
        // Fix multiple consecutive <br> tags
        .replace(/(<br\s*\/?>){3,}/g, '<br><br>')
        
        // Fix empty paragraphs
        .replace(/<p[^>]*>\s*<\/p>/g, '')
        
        // Ensure proper spacing around paragraphs
        .replace(/<p(?![^>]*style=)([^>]*)>/g, '<p$1 style="margin: 12px 0; padding: 0;">')
        
        // Fix images without proper styling
        .replace(/<img([^>]*?)(?!.*style=)([^>]*)>/g, '<img$1 style="max-width: 100%; height: auto; display: block;"$2>')
        
        // Fix strong tags
        .replace(/<strong(?![^>]*style=)([^>]*)>/g, '<strong$1 style="font-weight: bold;">')
        
        // Clean up extra whitespace but preserve intentional spacing
        .replace(/>\s+</g, '><')
        .trim();
    },

    /**
     * Quick fix for your specific use case
     * Call this method before sending any email
     */
    prepareEmailForSending(emailContent) {
      return this.fixEmailContent(emailContent);
    }
  }
};
</script>

<style scoped>
/* Component styles if needed */
</style>
</script>
