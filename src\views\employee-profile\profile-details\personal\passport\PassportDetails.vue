<template>
  <div class="my-4">
    <div class="d-flex">
      <div class="d-flex align-center">
        <v-progress-circular
          model-value="100"
          color="red"
          :size="22"
          class="mr-2"
        ></v-progress-circular>
        <span class="text-h6 text-grey-darken-1 font-weight-bold"
          >Passport Details</span
        >
      </div>

      <span
        v-if="formType === 'add' && enableEdit"
        class="d-flex justify-end ml-auto"
      >
        <v-btn color="primary" variant="text" @click="openEditDialog">
          <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
        >
      </span>
      <div
        v-if="formType === 'edit' && !showEditForm && enableEdit"
        class="d-flex justify-end ml-auto"
      >
        <v-btn @click="openEditDialog" color="primary" variant="text">
          <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
        </v-btn>
      </div>
    </div>
    <div
      v-if="
        !showEditForm && passportDetailsData && passportDetailsData.length === 0
      "
      class="d-flex align-center mt-2 ml-4 text-h6 text-grey"
    >
      No passport details have been added
    </div>

    <v-form ref="passportDetailsForm" v-if="showEditForm">
      <v-row>
        <v-col
          v-if="labelList[123].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <v-text-field
            v-model="editedPassportDetails.Passport_No"
            :rules="[
              required(
                `${labelList[123].Field_Alias}`,
                editedPassportDetails.Passport_No
              ),
              editedPassportDetails.Passport_No
                ? validateWithRulesAndReturnMessages(
                    editedPassportDetails.Passport_No,
                    'passportNo',
                    `${labelList[123].Field_Alias}`
                  )
                : true,
            ]"
            variant="solo"
            @update:model-value="onChangeFields()"
          >
            <template v-slot:label>
              <span>{{ labelList[123].Field_Alias }}</span>
              <span
                v-if="labelList[123].Mandatory_Field == 'Yes'"
                class="ml-1"
                style="color: red"
                >*</span
              >
            </template>
          </v-text-field>
        </v-col>

        <v-col
          v-if="labelList[124].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <v-menu
            v-model="issueDateMenu"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ props }">
              <v-text-field
                ref="Issue Date"
                v-model="formattedIssueDate"
                prepend-inner-icon="fas fa-calendar"
                :rules="[
                  required(labelList['124'].Field_Alias, formattedIssueDate),
                ]"
                readonly
                v-bind="props"
                variant="solo"
              >
                <template v-slot:label>
                  {{ labelList["124"].Field_Alias }}
                  <span
                    v-if="labelList['124'].Mandatory_Field === 'Yes'"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </template>
            <v-date-picker
              v-model="editedPassportDetails.Issue_Date"
              :max="currentDate"
              @update:modelValue="onChangeFields()"
            />
          </v-menu>
        </v-col>

        <v-col
          v-if="labelList[127].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <v-menu
            v-model="expiryDateMenu"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ props }">
              <v-text-field
                ref="Expiry Date"
                v-model="formattedExpiryDate"
                prepend-inner-icon="fas fa-calendar"
                :rules="[
                  required(labelList['127'].Field_Alias, formattedExpiryDate),
                ]"
                readonly
                v-bind="props"
                :disabled="!editedPassportDetails.Issue_Date"
                variant="solo"
              >
                <template v-slot:label>
                  {{ labelList["127"].Field_Alias }}
                  <span
                    v-if="labelList['127'].Mandatory_Field === 'Yes'"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </template>
            <v-date-picker
              v-model="editedPassportDetails.Expiry_Date"
              :min="passportExpiryDateMin"
              @update:modelValue="onChangeFields()"
            />
          </v-menu>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <v-file-input
            prepend-icon=""
            :model-value="fileContent"
            append-inner-icon="fas fa-paperclip"
            variant="solo"
            label="Document"
            accept="image/png, image/jpeg, image/jpg, application/pdf"
            :rules="[required('Document', fileContentRuleValue)]"
            @update:modelValue="onChangeFiles"
            @click:clear="removeFiles"
          >
          </v-file-input>
        </v-col>
        <v-col
          v-if="labelList[126].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <CustomSelect
            :items="countryList"
            :label="labelList[126].Field_Alias"
            :isAutoComplete="true"
            :isLoading="countryListLoading"
            :noDataText="
              countryListLoading ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedPassportDetails.Issuing_Country"
            itemValue="Country_Code"
            itemTitle="Country_Name"
            :isRequired="labelList[126].Mandatory_Field == 'Yes'"
            :rules="[
              required(
                `${labelList[126].Field_Alias}`,
                editedPassportDetails.Issuing_Country
              ),
            ]"
            @selected-item="
              onChangeCustomSelectField($event, 'Issuing_Country')
            "
          ></CustomSelect>
        </v-col>
        <v-col
          v-if="labelList[125].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <v-text-field
            v-model="editedPassportDetails.Issuing_Authority"
            variant="solo"
            :rules="[
              required(
                `${labelList[125].Field_Alias}`,
                editedPassportDetails.Issuing_Authority
              ),
              editedPassportDetails.Issuing_Authority
                ? validateWithRulesAndReturnMessages(
                    editedPassportDetails.Issuing_Authority,
                    'issuingAuthority',
                    `${labelList[125].Field_Alias}`
                  )
                : true,
            ]"
            @update:model-value="onChangeFields()"
          >
            <template v-slot:label>
              <span>{{ labelList[125].Field_Alias }}</span>
              <span
                v-if="labelList[125].Mandatory_Field == 'Yes'"
                class="ml-1"
                style="color: red"
                >*</span
              >
            </template>
          </v-text-field>
        </v-col>
      </v-row>
    </v-form>

    <v-row
      v-if="!showEditForm && formType === 'edit'"
      class="pa-4 ma-2 card-blue-background"
    >
      <v-col
        v-if="labelList[123].Field_Visiblity == 'Yes'"
        cols="12"
        md="4"
        sm="6"
      >
        <p class="text-subtitle-1 text-grey-darken-1">
          {{ labelList[123].Field_Alias }}
        </p>
        <p class="text-subtitle-1 font-weight-regular">
          {{ checkNullValue(passportDetails.Passport_No) }}
        </p>
      </v-col>
      <v-col
        v-if="labelList[124].Field_Visiblity == 'Yes'"
        cols="12"
        md="4"
        sm="6"
      >
        <p class="text-subtitle-1 text-grey-darken-1">
          {{ labelList[124].Field_Alias }}
        </p>
        <p class="text-subtitle-1 font-weight-regular">
          {{ formatDate(passportDetails.Issue_Date) }}
        </p>
      </v-col>
      <v-col
        v-if="labelList[127].Field_Visiblity == 'Yes'"
        cols="12"
        md="4"
        sm="6"
      >
        <p class="text-subtitle-1 text-grey-darken-1">
          {{ labelList[127].Field_Alias }}
        </p>
        <p class="text-subtitle-1 font-weight-regular">
          {{ formatDate(passportDetails.Expiry_Date) }}
        </p>
      </v-col>
      <v-col
        v-if="labelList[126].Field_Visiblity == 'Yes'"
        cols="12"
        md="4"
        sm="6"
      >
        <p class="text-subtitle-1 text-grey-darken-1">
          {{ labelList[126].Field_Alias }}
        </p>
        <p class="text-subtitle-1 font-weight-regular">
          {{ checkNullValue(passportDetails.Country_Name) }}
        </p>
      </v-col>
      <v-col
        v-if="labelList[125].Field_Visiblity == 'Yes'"
        cols="12"
        md="4"
        sm="6"
      >
        <p class="text-subtitle-1 text-grey-darken-1">
          {{ labelList[125].Field_Alias }}
        </p>
        <p class="text-subtitle-1 font-weight-regular">
          {{ checkNullValue(passportDetails.Issuing_Authority) }}
        </p>
      </v-col>
      <v-col
        v-if="
          passportDetailsData &&
          passportDetailsData.length &&
          passportDetailsData[0].File_Name
        "
        cols="12"
        md="4"
        sm="6"
      >
        <div class="mt-2 mr-2 d-flex flex-column justify-start">
          <span class="text-blue-grey-darken-3 font-weight-bold"></span>
          <span class="text-blue-grey-darken-6">
            <span
              style="text-decoration: underline"
              @click="retrieveDocuments(passportDetailsData[0].File_Name)"
              class="text-green cursor-pointer"
            >
              View Document</span
            >
          </span>
        </div>
      </v-col>
    </v-row>
  </div>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            >Cancel</v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1"
            color="primary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateEditForm()"
          >
            Save
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
  <FilePreviewModal
    v-if="openModal"
    :fileName="retrievedFileName"
    folderName="Employees Document Upload"
    fileRetrieveType="documents"
    @close-preview-modal="openModal = false"
  ></FilePreviewModal>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import { defineAsyncComponent } from "vue";
import moment from "moment";
import { checkNullValue } from "@/helper";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { ADD_UPDATE_PASSPORT_DETAILS } from "@/graphql/employee-profile/profileQueries.js";
import mixpanel from "mixpanel-browser";
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);
import Config from "@/config.js";

export default {
  name: "PassportDetails",
  mixins: [validationRules],
  components: {
    CustomSelect,
    FilePreviewModal,
  },
  props: {
    passportDetailsData: {
      type: [Array, Object],
      required: true,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
    selectedEmployeeDob: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
  },
  emits: ["refetch-personal-details", "edit-opened", "edit-closed"],
  data() {
    return {
      showEditForm: false,
      enableForm: false,
      passportDetails: {
        Passport_No: "",
        Expiry_Date: null,
        Issue_Date: null,
        Issuing_Country: "",
        Issuing_Authority: "",
        Country_Name: "",
      },
      editedPassportDetails: {},
      editFormVisible: false,
      isFormDirty: false,
      //Date-picker
      formattedIssueDate: "",
      issueDateMenu: false,
      expiryDateMenu: false,
      formattedExpiryDate: "",
      openWarningModal: false,
      // edit
      formType: "",
      openBottomSheet: false,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      // list
      countryList: [],
      countryListLoading: false,
      retrievedFileName: "",
      openModal: false,
      fileContent: null,
      isFileChanged: false,
    };
  },
  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    fileContentRuleValue() {
      return this.fileContent && this.fileContent.name
        ? this.fileContent.name
        : null;
    },
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    selectedEmpDobDate() {
      if (
        this.selectedEmployeeDob &&
        this.selectedEmployeeDob !== "0000-00-00"
      ) {
        return moment(this.selectedEmployeeDob).format("YYYY-MM-DD");
      } else return null;
    },
    passportExpiryDateMin() {
      if (
        this.editedPassportDetails.Issue_Date &&
        this.editedPassportDetails.Issue_Date !== "0000-00-00"
      ) {
        return moment(this.editedPassportDetails.Issue_Date).format(
          "YYYY-MM-DD"
        );
      }
      return this.selectedEmpDobDate;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    enableEdit() {
      if (this.empFormUpdateAccess) {
        return this.empFormUpdateAccess;
      } else if (this.formAccess && this.formAccess.admin === "admin") {
        let myTeamFormAccess =
          this.formType === "add"
            ? this.formAccess.add
            : this.formAccess.update;
        return this.empFormUpdateAccess || myTeamFormAccess;
      } else return false;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  watch: {
    enableForm(val) {
      if (val) {
        this.openEditDialog();
      } else {
        this.closeEditForm();
      }
    },
    "editedPassportDetails.Issue_Date": function (val) {
      if (val) {
        this.issueDateMenu = false;
        this.formattedIssueDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "editedPassportDetails.Expiry_Date": function (val) {
      if (val) {
        this.expiryDateMenu = false;
        this.formattedExpiryDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    isFormDirty(val) {
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", val);
    },
    showEditForm(val) {
      let editFormOpened =
        this.$store.state.employeeProfile.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.passportDetailsData && this.passportDetailsData.length > 0) {
      if (this.passportDetailsData[0].File_Name) {
        this.fileContent = {
          name: this.formattedFileName(this.passportDetailsData[0].File_Name),
          size: 100,
        };
      } else {
        this.fileContent = null;
      }
      this.formType = "edit";
      this.passportDetails = this.passportDetailsData[0];
    } else {
      this.formType = "add";
    }
  },
  methods: {
    checkNullValue,

    onChangeFields() {
      this.isFormDirty = true;
    },

    onChangeCustomSelectField(value, field) {
      this.onChangeFields();
      this.editedPassportDetails[field] = value;
    },

    editUpdated() {
      this.openBottomSheet = false;
      this.showEditForm = false;
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
      this.$emit("refetch-personal-details");
    },

    openEditDialog() {
      this.showEditForm = true;
      this.openBottomSheet = true;
      this.editedPassportDetails = JSON.parse(
        JSON.stringify(this.passportDetails)
      );
      if (this.editedPassportDetails.Issue_Date) {
        this.formattedIssueDate = this.formatDate(
          this.editedPassportDetails?.Issue_Date
        );
        this.editedPassportDetails.Issue_Date = this.editedPassportDetails
          .Issue_Date
          ? new Date(this.editedPassportDetails.Issue_Date)
          : null;
      }
      if (this.editedPassportDetails.Expiry_Date) {
        this.formattedExpiryDate = this.formatDate(
          this.editedPassportDetails?.Expiry_Date
        );
        this.editedPassportDetails.Expiry_Date = this.editedPassportDetails
          .Expiry_Date
          ? new Date(this.editedPassportDetails.Expiry_Date)
          : null;
      }
      this.retrieveCountries();
      this.$emit("edit-opened");
      mixpanel.track("EmpProfile-passport-edit-opened");
    },

    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    closeEditForm() {
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        this.openBottomSheet = false;
        this.showEditForm = false;
        mixpanel.track("EmpProfile-passport-edit-closed");
        this.$emit("edit-closed");
      }
    },

    async validateEditForm() {
      let isFormValid = await this.$refs.passportDetailsForm.validate();
      mixpanel.track("EmpProfile-passport-submit-btn-clicked");
      if (isFormValid && isFormValid.valid) {
        this.updatePassportDetails();
      }
    },

    updatePassportDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_PASSPORT_DETAILS,
          variables: {
            employeeId: vm.selectedEmpId,
            passportNo: vm.editedPassportDetails.Passport_No,
            passportIssueDate: moment(
              vm.editedPassportDetails.Issue_Date
            ).isValid()
              ? moment(vm.editedPassportDetails.Issue_Date).format("YYYY-MM-DD")
              : null,
            passportExpiryDate: moment(
              vm.editedPassportDetails.Expiry_Date
            ).isValid()
              ? moment(vm.editedPassportDetails.Expiry_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            fileName: vm.editedPassportDetails["File_Name"],
            issuingAuthority: vm.editedPassportDetails.Issuing_Authority,
            issuingCountry: vm.editedPassportDetails.Issuing_Country,
            formName: vm.callingFrom === "profile" ? "My Profile" : "",
          },
          client: "apolloClientAD",
        })
        .then(() => {
          mixpanel.track("EmpProfile-passport-update-success");
          if (vm.editedPassportDetails["File_Name"] && vm.isFileChanged) {
            vm.uploadFileContents();
          } else {
            vm.isLoading = false;
            let snackbarData = {
              isOpen: true,
              message:
                vm.formType === "edit"
                  ? "Passport details updated successfully"
                  : "Passport details added successfully",
              type: "success",
            };
            vm.showAlert(snackbarData);
            vm.editUpdated();
          }
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("EmpProfile-passport-update-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "passport details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    async retrieveCountries() {
      this.countryListLoading = true;
      this.countryList = [];
      await this.$store
        .dispatch("listCountries")
        .then((langList) => {
          this.countryList = langList;
          this.countryListLoading = false;
        })
        .catch(() => {
          this.countryListLoading = false;
        });
    },
    async uploadFileContents() {
      mixpanel.track("Onboarded-candidate-doc-file-upload-start");
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Employees Document Upload/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + this.editedPassportDetails["File_Name"],
          action: "upload",
          type: "documents",
          fileContent: vm.fileContent,
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              vm.formType === "edit"
                ? "Passport details updated successfully"
                : "Passport details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.editUpdated();
        })
        .catch(() => {
          mixpanel.track("Onboarded-candidate-doc-file-upload-error");
          vm.isLoading = false;
          vm.editUpdated();
        });
    },
    onChangeFiles(value) {
      this.fileContent = value;
      if (this.fileContent && this.fileContent.name) {
        mixpanel.track("Onboarded-candidate-doc-file-changed");
        this.editedPassportDetails["File_Name"] =
          this.selectedEmpId +
          "?" +
          this.currentTimeStamp +
          "?1?" +
          this.fileContent.name;
        this.isFileChanged = true;
        this.onChangeFields();
      }
    },
    removeFiles() {
      mixpanel.track("Onboarded-candidate-doc-file-removed");
      this.editedPassportDetails["File_Name"] = "";
      this.fileContent = null;
      this.onChangeFields();
    },
    formattedFileName(fileName) {
      if (fileName) {
        let fileNameChunks = fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "File Name";
      }
      return "";
    },
    retrieveDocuments(fileName) {
      let vm = this;
      vm.retrievedFileName = fileName;
      vm.openModal = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
