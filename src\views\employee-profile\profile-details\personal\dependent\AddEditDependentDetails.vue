<template>
  <v-card class="rounded-lg pa-4">
    <div>
      <v-card-title class="d-flex">
        <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
          >Dependent Details</span
        >
        <v-spacer></v-spacer>
        <v-icon color="grey" size="25" @click="$emit('close-dependent-form')"
          >fas fa-times</v-icon
        >
      </v-card-title>
      <v-card-text>
        <v-alert v-if="showValidationAlert" prominent type="warning">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              class="mt-n5 primary"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </v-alert>
        <v-form ref="addDependentForm">
          <v-row>
            <v-col cols="12" md="4" sm="6">
              <v-text-field
                v-model="editedDependentDetailsData.Dependent_First_Name"
                variant="solo"
                :rules="[
                  required(
                    'First Name',
                    editedDependentDetailsData.Dependent_First_Name
                  ),
                  validateWithRulesAndReturnMessages(
                    editedDependentDetailsData.Dependent_First_Name,
                    'dependentFirstName',
                    'First Name'
                  ),
                ]"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  First Name<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-text-field
                v-model="editedDependentDetailsData.Dependent_Last_Name"
                variant="solo"
                :rules="[
                  required(
                    'Last Name',
                    editedDependentDetailsData.Dependent_Last_Name
                  ),
                  validateWithRulesAndReturnMessages(
                    editedDependentDetailsData.Dependent_Last_Name,
                    'dependentLastName',
                    'Last Name'
                  ),
                ]"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Last Name<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <CustomSelect
                v-model="editedDependentDetailsData.Relationship"
                variant="solo"
                :items="relationShipList"
                label="Relationship"
                :isRequired="true"
                :rules="[
                  required(
                    'Relationship',
                    editedDependentDetailsData.Relationship
                  ),
                ]"
                itemValue="Dependent_Relationship"
                itemTitle="Dependent_Relationship"
                :isLoading="relationShipListLoading"
                :itemSelected="editedDependentDetailsData.Relationship"
                @selected-item="
                  onChangeCustomSelectField($event, 'Relationship')
                "
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-menu
                v-model="dobMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="Date of Birth"
                    v-model="formattedDOB"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('Date of Birth', formattedDOB)]"
                    readonly
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      Date of Birth<span style="color: red">*</span>
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="editedDependentDetailsData.Dependent_DOB"
                  :max="currentDate"
                  @update:modelValue="onChangeFields()"
                />
              </v-menu>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <CustomSelect
                :items="genderList"
                label="Gender"
                :isRequired="true"
                :rules="
                  editedDependentDetailsData.Gender_Id == 0
                    ? [true]
                    : [required('Gender', editedDependentDetailsData.Gender_Id)]
                "
                :itemSelected="editedDependentDetailsData.Gender_Id"
                ref="gender"
                itemValue="genderId"
                itemTitle="gender"
                :isLoading="genderListLoading"
                :noDataText="
                  genderListLoading ? 'Loading...' : 'No data available'
                "
                @selected-item="onChangeCustomSelectField($event, 'Gender_Id')"
              ></CustomSelect>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-end">
                <v-btn
                  @click="$emit('close-dependent-form')"
                  class="ma-2 pa-2"
                  variant="outlined"
                >
                  <span>Cancel</span>
                </v-btn>
                <v-btn
                  :disabled="!isFormDirty"
                  class="ma-2 pa-1 secondary"
                  variant="elevated"
                  @click="validateDependentDetails"
                  color="primary"
                >
                  <span class="primary">Save</span>
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { ADD_UPDATE_DEPENDENT_DETAILS } from "@/graphql/employee-profile/profileQueries.js";
import { LIST_MARITAL_STATUS, GENDER_LIST } from "@/graphql/dropDownQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "AddEditDependentDetails",
  mixins: [validationRules],
  props: {
    dependentDetails: {
      type: Object,
      required: false,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    callingFrom: {
      type: String,
      default: "",
    },
    actionType: {
      type: String,
      default: "",
    },
    selectedEmpMaritalStatus: {
      type: [String, Number],
      required: true,
    },
  },
  components: {
    CustomSelect,
  },
  emits: ["refetch-personal-details", "close-dependent-form"],
  data() {
    return {
      editedDependentDetailsData: {
        Dependent_First_Name: "",
        Dependent_Last_Name: "",
        Relationship: "",
        Dependent_DOB: null,
      },
      //Date-picker
      formattedDOB: "",
      dobMenu: false,
      isFormDirty: false,
      // edit
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      formType: "",
      // list
      relationShipList: [],
      relationShipListLoading: false,
      genderList: [],
      genderListLoading: false,
    };
  },

  computed: {
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
  },

  watch: {
    "editedDependentDetailsData.Dependent_DOB": function (val) {
      // Do something with the new value and/or old value here
      if (val) {
        this.dobMenu = false;
        this.formattedDOB = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    // Set the form data when component is created
    if (
      this.dependentDetails &&
      Object.keys(this.dependentDetails).length > 0
    ) {
      this.editedDependentDetailsData = JSON.parse(
        JSON.stringify(this.dependentDetails)
      );
      if (this.editedDependentDetailsData.Dependent_DOB) {
        this.formattedDOB = this.formatDate(
          this.editedDependentDetailsData?.Dependent_DOB
        );
        this.editedDependentDetailsData.Dependent_DOB = this
          .editedDependentDetailsData.Dependent_DOB
          ? new Date(this.editedDependentDetailsData.Dependent_DOB)
          : null;
      }
      this.formType = "edit";
    } else {
      this.formType = "add";
    }
    this.retrieveRelationShipList();
    this.retrieveGender();
  },
  methods: {
    async validateDependentDetails() {
      const { valid } = await this.$refs.addDependentForm.validate();
      mixpanel.track("EmpProfile-dependent-submit-clicked");
      if (valid) {
        this.updateDependentDetails();
      }
    },
    onChangeFields() {
      this.isFormDirty = true;
    },
    onChangeCustomSelectField(value, field) {
      this.onChangeFields();
      this.editedDependentDetailsData[field] = value;
    },

    retrieveRelationShipList() {
      let vm = this;
      vm.relationShipListLoading = true;
      vm.$apollo
        .query({
          query: LIST_MARITAL_STATUS,
          client: "apolloClientAC",
          variables: {
            retrieveRelationship: 1,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveMaritalStatus &&
            !response.data.retrieveMaritalStatus.errorCode
          ) {
            const { maritalStatus } = response.data.retrieveMaritalStatus;
            vm.relationShipList = maritalStatus
              ? JSON.parse(maritalStatus)
              : [];
          }
          vm.relationShipListLoading = false;
        })
        .catch(() => {
          vm.relationShipListLoading = false;
        });
    },

    updateDependentDetails() {
      let vm = this;
      vm.isLoading = true;
      let genderValue = vm.genderList.filter(
        (el) => el.genderId == vm.editedDependentDetailsData.Gender_Id
      );
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_DEPENDENT_DETAILS,
          variables: {
            employeeId: vm.selectedEmpId,
            dependentId: vm.editedDependentDetailsData.Dependent_Id,
            dependentFirstName:
              vm.editedDependentDetailsData.Dependent_First_Name,
            dependentLastName:
              vm.editedDependentDetailsData.Dependent_Last_Name,
            gender: genderValue[0].gender,
            genderId: vm.editedDependentDetailsData.Gender_Id,
            relationship: vm.editedDependentDetailsData.Relationship,
            dependentDOB: moment(
              vm.editedDependentDetailsData.Dependent_DOB
            ).isValid()
              ? moment(vm.editedDependentDetailsData.Dependent_DOB).format(
                  "YYYY-MM-DD"
                )
              : null,
            formName: vm.callingFrom === "profile" ? "My Profile" : "",
            isUpdate: vm.actionType === "edit" ? 1 : 0,
          },
          client: "apolloClientAD",
        })
        .then(() => {
          mixpanel.track("EmpProfile-dependent-update-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              vm.formType === "edit"
                ? "Dependent details updated successfully"
                : "Dependent details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-personal-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("EmpProfile-dependent-update-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "dependent details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    retrieveGender() {
      let vm = this;
      vm.genderListLoading = true;
      vm.$apollo
        .query({
          query: GENDER_LIST,
          client: "apolloClientX",
          variables: {
            Org_Code: this.orgCode,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveGenderList &&
            !response.data.retrieveGenderList.errorCode
          ) {
            const { genderData } = response.data.retrieveGenderList;
            vm.genderList = genderData;
          }
          vm.genderListLoading = false;
        })
        .catch(() => {
          vm.genderListLoading = false;
        });
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
