<template>
  <v-overlay
    v-if="overlay"
    v-model="overlay"
    persistent="true"
    @click:outside="onCloseOverlay()"
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card"
        :style="windowWidth >= 1264 ? '' : 'width: 100vw'"
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div class="text-h6 text-medium ps-2">
            {{ isEditForm ? "Edit" : "Apply" }}
            Compensatory Off
          </div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseOverlay()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <!-- Scrollable Content -->
        <v-card-text
          class="card mb-3 px-0"
          style="overflow-y: auto; padding-bottom: 50px"
          :style="
            isEditForm
              ? { 'max-height': 'calc(90vh - 50px)' }
              : isMobileView
              ? { 'max-height': 'calc(85vh - 180px)' }
              : { 'max-height': 'calc(90vh - 170px)' }
          "
        >
          <v-form ref="compOffForm">
            <v-row class="px-6 pt-6">
              <v-col cols="12" class="px-md-6 py-0">
                <CustomSelect
                  :items="employeesList"
                  v-model="selectedEmployee"
                  :itemSelected="selectedEmployee"
                  item-title="text"
                  item-value="value"
                  label="Employee"
                  clearable
                  :is-auto-complete="true"
                  :is-required="true"
                  :is-loading="nameListLoading"
                  :disabled="isEditForm"
                  @selected-item="isFormDirty = true"
                  :rules="[required('Employee Name ', selectedEmployee)]"
                  @update:modelValue="updatedEmployee"
                ></CustomSelect>
              </v-col>
              <v-col cols="12" class="px-md-6 py-0">
                <CustomSelect
                  :items="workedDateList"
                  v-model="selectedWorkedDate"
                  label="Worked Dates"
                  item-title="Compensated_Date"
                  item-value="Compensated_Date"
                  :is-required="true"
                  clearable
                  :is-loading="workedDateListLoading"
                  :itemSelected="selectedWorkedDate"
                  @selected-item="isFormDirty = true"
                  :is-auto-complete="true"
                  :rules="[required('Worked Dates', selectedWorkedDate)]"
                  @update:modelValue="updateDropDown"
                ></CustomSelect>
              </v-col>
              <v-col cols="12" class="px-md-6 py-0">
                <span class="v-label mr-2 pl-4 mt-n2">Total Balance</span>:
                <span class="text-body-2">
                  {{ checkNullValue(totalDays) }}
                </span>
              </v-col>
              <v-col cols="12" class="px-md-6 py-0">
                <CustomSelect
                  :items="durationOptions"
                  v-model="selectedDuration"
                  label="Duration"
                  item-title="text"
                  item-value="value"
                  :is-required="true"
                  clearable
                  :itemSelected="selectedDuration"
                  @selected-item="isFormDirty = true"
                  :is-auto-complete="true"
                  :rules="[required('Duration', selectedDuration)]"
                  @update:modelValue="resetCompOffDate()"
                ></CustomSelect>
              </v-col>
              <v-col
                v-if="selectedDuration === 0.5"
                cols="12"
                class="px-md-6 py-0"
              >
                <CustomSelect
                  :items="['First Half', 'Second Half']"
                  label="Period "
                  v-model="selectedPeriod"
                  :isRequired="true"
                  :itemSelected="selectedPeriod"
                  @selected-item="isFormDirty = true"
                  :is-auto-complete="true"
                  clearable
                  :rules="
                    selectedDuration === 0.5
                      ? [required('Period ', selectedPeriod)]
                      : true
                  "
                ></CustomSelect>
              </v-col>
              <!-- For future use -->
              <!-- <v-col cols="12" sm="6" md="6" class="px-md-6 mt-2">
                <v-text-field
                  v-model="status"
                  type="text"
                  variant="solo"
                  label="Status"
                >
                </v-text-field>
              </v-col> -->
              <v-col cols="12" class="px-md-6 py-0">
                <v-menu
                  v-model="compOffDateMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      v-model="formattedCompOffDate"
                      prepend-inner-icon="fas fa-calendar"
                      isRequired="true"
                      :rules="[
                        required('Compensatory Off Date', formattedCompOffDate),
                      ]"
                      readonly
                      v-bind="props"
                      variant="solo"
                      ref="candidateDob"
                    >
                      <template v-slot:label>
                        Compensatory Off Date<span style="color: red">*</span>
                      </template></v-text-field
                    >
                  </template>
                  <v-date-picker
                    v-if="compOffDate"
                    v-model="compOffDate"
                    :max="maximumCompOffDate"
                    :min="minimumCompOffDate"
                    @update:modelValue="onChangeFields()"
                  />
                  <v-date-picker
                    v-else
                    v-model="defaultCompOffDate"
                    @update:modelValue="onChangeDefaultDOB(defaultCompOffDate)"
                    :max="maximumCompOffDate"
                    :min="minimumCompOffDate"
                  />
                </v-menu>
              </v-col>

              <v-col
                v-if="eventSchedules?.length"
                cols="12"
                class="px-md-6 my-2"
              >
                <v-card
                  v-for="(schedule, index) in eventSchedules"
                  :key="index"
                  class="mb-4 pa-4 bg-hover"
                  rounded="xl"
                  elevation="3"
                >
                  <v-row>
                    <v-col cols="6">
                      <v-row>
                        <v-col cols="5">
                          <label class="font-weight-medium">Schedule</label>
                        </v-col>
                        <v-col cols="7">
                          {{ checkNullValue(schedule.fromTime) }} -
                          {{ checkNullValue(schedule.toTime) }}
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="6">
                      <v-row>
                        <v-col cols="5">
                          <label class="font-weight-medium">Subject</label>
                        </v-col>
                        <v-col cols="7">
                          {{ checkNullValue(schedule.subject) }}
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>

                  <v-divider class="my-2"></v-divider>

                  <v-row>
                    <v-col cols="6">
                      <v-row>
                        <v-col cols="5">
                          <label class="font-weight-medium">Class</label>
                        </v-col>
                        <v-col cols="7">
                          {{ checkNullValue(schedule.sectionName) }}
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="6">
                      <v-row>
                        <v-col cols="5">
                          <label class="font-weight-medium">Location</label>
                        </v-col>
                        <v-col cols="7">
                          {{ checkNullValue(schedule.locationName) }}
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
              <v-col cols="12" class="px-md-6 py-0">
                <v-textarea
                  v-model="reason"
                  rows="2"
                  row-height="8"
                  counter="500"
                  color="primary"
                  maxlength="500"
                  hide-details="auto"
                  variant="solo"
                  label="Reason"
                  :rules="[
                    validateWithRulesAndReturnMessages(
                      reason,
                      'description',
                      'Reason'
                    ),
                  ]"
                  @update:model-value="isFormDirty = true"
                ></v-textarea>
              </v-col>
              <v-col
                v-if="labelList[429]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                class="px-md-6 py-0"
              >
                <v-file-input
                  ref="documentUpload"
                  prepend-icon=""
                  clearable
                  multiple
                  chips
                  :model-value="documentUpload"
                  append-inner-icon="fas fa-paperclip"
                  variant="solo"
                  :rules="[
                    labelList[429].Mandatory_Field?.toLowerCase() == 'yes'
                      ? required('Documents', documentUpload?.length)
                      : true,
                    checkSize(documentUpload),
                    checkDuplicateFiles(documentUpload),
                  ]"
                  :persistent-hint="true"
                  hint="Max Size: 3MB. Allowed formats: .jpg, .jpeg, .png, .pdf, .doc, .docx, .txt"
                  accept=".jpg, .jpeg, .png, .pdf, .doc, .docx, .txt"
                  @update:model-value="onUploadFile($event, documentUpload)"
                  @click:clear="documentUpload = []"
                >
                  <template v-slot:label>
                    <span>{{ labelList[429]?.Field_Alias }}</span>
                    <span
                      v-if="
                        labelList[429].Mandatory_Field?.toLowerCase() == 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                  <template v-slot:selection="{}">
                    <v-chip
                      v-for="(file, index) in documentUpload"
                      :key="file?.name + '_' + index"
                      draggable
                      small
                      closable
                      @click:close="documentUpload.splice(index, 1)"
                    >
                      <span class="text-truncate">
                        {{
                          file.name.length > 25
                            ? file.name.slice(0, 22) + "..."
                            : file.name
                        }}
                      </span>
                    </v-chip>
                  </template>
                </v-file-input>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <v-card-text class="px-0">
          <v-col cols="12" class="px-md-6 py-0 mb-8">
            <NotesCard
              v-if="!isEditForm"
              notes="The creation of Compensatory Off (Comp Off) balance is directly linked to attendance data. Attendance records for week-offs and holidays must first be approved by the reporting manager. Once approved, the Comp Off balance is credited based on the configured policy guidelines."
              class="px-2"
            ></NotesCard>
          </v-col>
        </v-card-text>
      </v-card>
      <v-card class="overlay-footer bottom-0 position-sticky w-100">
        <div class="d-flex justify-end pa-4">
          <v-btn
            rounded="lg"
            class="mr-6 primary"
            @click="onCloseOverlay()"
            variant="outlined"
          >
            Cancel
          </v-btn>
          <v-btn
            rounded="lg"
            class="mr-1 primary"
            @click="submitCompOffForm()"
            variant="elevated"
            :disabled="!isFormDirty || disableSubmitButton"
          >
            Submit
          </v-btn>
        </div></v-card
      >
      <AppLoading v-if="listLoading"></AppLoading>
    </template>
  </v-overlay>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="onClosePopup()"
  >
  </AppWarningModal>
</template>
<script>
import { defineAsyncComponent } from "vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import { checkNullValue } from "@/helper";
import { GET_LEAVE_SETTINGS } from "@/graphql/settings/core-hr/leaveSettingsQueries.js";
import { GET_STAFF_SCHEDULES_BY_DATE_RANGE } from "@/graphql/workflow/approvalManagementQueries.js";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import moment from "moment";
export default {
  name: "AddEditCompOff",
  emits: ["on-close-add-form", "refetch-list"],
  mixins: [validationRules],
  props: {
    selectedCompOffData: {
      type: Object,
      default: () => {},
    },
    isEditForm: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      listLoading: false,
      overlay: true,
      openConfirmationPopup: false,
      isFormDirty: false,
      employeesList: [],
      selectedEmployee: null,
      workedDateListLoading: false,
      nameListLoading: false,
      forwardedTo: null,
      workedDateList: [],
      selectedWorkedDate: null,
      durationOptions: [
        { value: 1, text: "Full Day" },
        { value: 0.5, text: "Half Day" },
      ],
      selectedDuration: null,
      selectedPeriod: null,
      status: "New",
      compOffDateMenu: false,
      compOffDate: "",
      formattedCompOffDate: "",
      reason: "",
      compOffBalanceId: null,
      defaultCompOffDate: "",
      compOffMinDate: "",
      compOffMaxDate: "",
      totalDays: null,
      attachmentValidationMessage: [],
      isCAMUSchedulerEnabled: "No",
      eventSchedules: [],
      disableSubmitButton: false,
      documentUpload: [],
    };
  },
  components: {
    CustomSelect,
    NotesCard,
  },
  watch: {
    selectedEmployee(val) {
      if (val) {
        this.listApproverDetails(val);
        this.listworkdedDateDetails(val);
        const record = this.employeesList?.find((item) => item.value === val);
        this.userDefinedEmpId = record ? record.userDefinedEmpId : null;
      }
    },
    compOffDate(val) {
      if (val) {
        this.compOffDateMenu = false;
        this.formattedCompOffDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    selectedWorkedDate(val) {
      if (val) {
        const record = this.workedDateList?.find(
          (item) => item.Compensated_Date === val
        );
        this.compOffBalanceId = record ? record.Comp_Off_Balance_Id : null;
        this.getCompOffHours(this.compOffBalanceId);
      }
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    minimumCompOffDate() {
      if (moment(this.compOffMinDate).isValid()) {
        return moment(this.compOffMinDate).format("YYYY-MM-DD");
      } else return null;
    },
    maximumCompOffDate() {
      if (moment(this.compOffMaxDate).isValid()) {
        return moment(this.compOffMaxDate).format("YYYY-MM-DD");
      } else return null;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
      // return "https://fieldforce.hrapp.co.in/";
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {
    this.listEmployees();
    this.preFillCompOffForm();
  },
  methods: {
    checkNullValue,
    onCloseOverlay() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else {
        this.onClosePopup();
        this.isFormDirty = false;
      }
    },
    onClosePopup() {
      this.openConfirmationPopup = false;
      this.$emit("on-close-add-form");
      this.isFormDirty = false;
    },
    async submitCompOffForm() {
      const { valid } = await this.$refs.compOffForm.validate();
      if (valid) {
        this.uploadDocuments();
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    preFillCompOffForm() {
      if (this.isEditForm) {
        this.selectedDuration = this.selectedCompOffData?.Duration_Id || null;
        this.selectedPeriod = this.selectedCompOffData?.Period || "";
        this.status = this.selectedCompOffData?.Approval_Status || "";
        this.reason = this.selectedCompOffData?.Reason || "";
        let docs = this.selectedCompOffData?.Document_Names
          ? JSON.parse(this.selectedCompOffData?.Document_Names)
          : [];
        docs =
          docs?.map((doc) => {
            // Ensure doc is a string before splitting
            return {
              name: doc.split("-")[3] || "", // Extract filename from string
              formattedName: doc, // Keep the full path
            };
          }) || [];
        this.documentUpload = docs;
        if (this.selectedCompOffData?.Compensatory_Date) {
          this.compOffDate = this.formatToDateObject(
            this.selectedCompOffData.Compensatory_Date
          );
          this.formattedCompOffDate =
            this.selectedCompOffData.Compensatory_Date;
        }
      }
      this.defaultCompOffDate = this.minimumCompOffDate;
    },
    cleanString(inputString) {
      return inputString?.trim().replace(/\s+/g, " ");
    },
    formatToDateObject(date) {
      // Check if the date is in DD/MM/YYYY format
      if (moment(date, "DD/MM/YYYY", true).isValid()) {
        return moment(date, "DD/MM/YYYY").toDate();
      }
      // Otherwise, assume it's in YYYY/MM/DD format
      return moment(date, "YYYY/MM/DD").toDate();
    },
    formatToOriginal(date) {
      // Check if the date is in DD/MM/YYYY format
      if (moment(date, "DD/MM/YYYY", true).isValid()) {
        return moment(date, "DD/MM/YYYY").format("YYYY-MM-DD");
      }
      // Otherwise, assume it's in YYYY/MM/DD format
      return moment(date, "YYYY/MM/DD").format("YYYY-MM-DD");
    },
    updateDropDown() {
      this.selectedDuration = null;
      this.selectedPeriod = null;
      this.formattedCompOffDate = "";
    },
    async uploadDocuments() {
      try {
        // Upload Documents files
        this.listLoading = true;
        let neededFiles = this.documentUpload?.filter((doc) => doc.size) || [];
        if (neededFiles?.length) {
          for (let i = 0; i < neededFiles.length; i++) {
            await this.uploadFileContents(neededFiles[i]);
          }
        }
        this.addUpdateCompOff();
      } catch (error) {
        this.listLoading = false;
        this.showAlert({
          isOpen: true,
          type: "warning",
          message:
            "Something went wrong while uploading documents. Please try again.",
        });
      }
    },

    async addUpdateCompOff() {
      let vm = this;
      vm.listLoading = true;
      const compOffDocuments = this.documentUpload
        .filter((document) => document)
        .map((document) => {
          return document.formattedName;
        });
      const apiObj = {
        url: vm.baseUrl + "employees/compensatory-off/update-compensatory-off",
        type: "POST",
        dataType: "json",
        data: {
          Compensatory_Off_Id: vm.isEditForm
            ? vm.selectedCompOffData?.Compensatory_Off_Id
            : 0,
          Employee_Id: vm.selectedEmployee,
          Forward_To: parseInt(vm.forwardedTo),
          Comp_Off_Balance_Id: parseInt(vm.compOffBalanceId),
          Duration: vm.selectedDuration,
          Period: vm.selectedDuration === 0.5 ? vm.selectedPeriod : null,
          Compensatory_Date: vm.formatToOriginal(vm.formattedCompOffDate),
          Approval_Status: "Applied",
          Comment: "",
          reason: vm.reason,
          documentNames: compOffDocuments,
        },
      };
      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then(async (res) => {
          if (res && res.success) {
            let snackbarData = {
              isOpen: true,
              message: `Compensatory off ${
                vm.isEditForm ? "updated" : "added"
              } successfully`,
              type: "success",
            };
            vm.showAlert(snackbarData);
            vm.$emit("refetch-list");
            this.listLoading = false;
          } else {
            let snackbarData = {
              isOpen: true,
              message: res.msg,
              type: "warning",
            };
            vm.showAlert(snackbarData);
            this.listLoading = false;
          }
        })
        .catch((err) => {
          vm.handleAddUpdateError(err);
        });
    },
    handleAddUpdateError(err = "") {
      this.listLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: this.isEditForm ? "updating" : "adding",
        form: "comp off",
        isListError: false,
      });
    },
    async uploadFileContents(file) {
      let vm = this;
      let fileUploadUrl =
        this.domainName +
        "/" +
        this.orgCode +
        "/Compensatory Off Document Upload/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + file.formattedName,
          action: "upload",
          type: "documents",
          fileContent: file,
        })
        .catch((error) => {
          throw error;
        });
    },
    listEmployees() {
      let vm = this;
      vm.nameListLoading = true;
      const apiObj = {
        url: vm.baseUrl + "default/employee-info/list-employee-details",
        type: "POST",
        dataType: "json",
        data: {
          _fId: "Compensatory Off",
        },
      };

      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res && res.length) {
            vm.employeesList = res.map((emp) => ({
              value: parseInt(emp.value), // Ensure numeric comparison
              text: emp.text,
              userDefinedEmpId: emp.User_Defined_EmpId,
            }));

            if (this.isEditForm && this.selectedCompOffData?.Employee_Id) {
              const empId = parseInt(this.selectedCompOffData.Employee_Id); // Ensure number type

              const matchedEmployee = vm.employeesList?.find(
                (emp) => emp.value === empId
              );

              this.selectedEmployee = matchedEmployee
                ? matchedEmployee.value
                : null;
            }
          } else {
            vm.employeesList = [];
          }
          vm.nameListLoading = false;
        })
        .catch(() => {
          vm.nameListLoading = false;
          vm.employeesList = [];
        });
    },
    listApproverDetails(empId) {
      let vm = this;
      const apiObj = {
        url:
          vm.baseUrl +
          `default/employee-info/list-approver-details/employeeId/${empId}/formName/Compensatory%20Off`,
        type: "POST",
        dataType: "json",
      };

      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res && res.Manager?.length) {
            let managerDetails = res.Manager[0];
            vm.forwardedTo = managerDetails?.Manager_Id || null;
          }
        })
        .catch((error) => {
          vm.handleListApproverDetailsError(error);
        });
    },
    handleListApproverDetailsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "approver details",
        isListError: false,
      });
    },
    getCompOffHours(compOffBalanceId) {
      let vm = this;
      const apiObj = {
        url:
          vm.baseUrl +
          `employees/compensatory-off/get-compoff-hours/Employee_Id/${vm.selectedEmployee}/Comp_Off_Balance_Id/${compOffBalanceId}`,
        type: "POST",
        dataType: "json",
      };

      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res && Object.keys(res).length) {
            vm.compOffMinDate = res?.Compoff_Min_Date;
            vm.compOffMaxDate = res?.compOff_Max_Date;
            vm.totalDays = res?.Comp_Remaining_Days;
            if (
              moment(this.compOffMaxDate).isValid() &&
              moment(this.compOffMinDate).isValid()
            ) {
              if (
                moment(vm.compOffMinDate).isAfter(moment(vm.compOffMaxDate))
              ) {
                vm.disableSubmitButton = true;
                let snackbarData = {
                  isOpen: true,
                  message: `The minimum applicable date ${vm.compOffMinDate} for Compensatory Off cannot be later than the maximum applicable date ${vm.compOffMaxDate}.`,
                  type: "warning",
                };
                vm.showAlert(snackbarData);
              }
            }
          }
        })
        .catch((error) => {
          vm.handleGetCompOffHoursError(error);
        });
    },
    handleGetCompOffHoursError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "comp off hours",
        isListError: false,
      });
    },
    listworkdedDateDetails(empId) {
      let vm = this;
      vm.workedDateListLoading = true;
      const apiObj = {
        url:
          vm.baseUrl +
          `employees/compensatory-off/get-compensated-date/Employee_Id/${empId}`,
        type: "POST",
        dataType: "json",
      };

      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res?.length) {
            vm.workedDateList = res;
            if (vm.isEditForm) {
              this.selectedWorkedDate =
                this.selectedCompOffData.Compensated_Date;
            }
          } else {
            let snackbarData = {
              isOpen: true,
              message:
                "Attendance for the week off or holiday has either not been approved or you are not eligible for comp off entitlement. Please discuss with your HR team.",
              type: "warning",
            };
            vm.showAlert(snackbarData);
            vm.workedDateList = [];
          }
          vm.workedDateListLoading = false;
        })
        .catch(() => {
          vm.workedDateListLoading = false;
          vm.workedDateList = [];
        });
    },
    updatedEmployee() {
      this.selectedWorkedDate = null;
    },
    onChangeDefaultDOB(value) {
      this.compOffDate = value;
      this.isFormDirty = true;
      this.showSchedule();
    },
    onChangeFields() {
      this.isFormDirty = true;
      this.showSchedule();
    },
    async getStaffSchedulesByDateRange() {
      let vm = this;
      vm.listLoading = true;
      await vm.$apollo
        .query({
          query: GET_STAFF_SCHEDULES_BY_DATE_RANGE,
          variables: {
            duration: vm.selectedDuration.toString(),
            fromDate: vm.formatToOriginal(vm.formattedCompOffDate),
            leavePeriod: vm.selectedPeriod,
            staffId: vm.userDefinedEmpId,
            toDate: vm.formatToOriginal(vm.formattedCompOffDate),
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getStaffSchedulesByDateRange &&
            !response.data.getStaffSchedulesByDateRange.errorCode
          ) {
            vm.eventSchedules = [];
            const { allSchedules, processedSchedules } =
              response.data.getStaffSchedulesByDateRange;
            let hasConflict = false;

            processedSchedules.forEach((event) => {
              if (event.isScheduled && vm.selectedDuration === 1) {
                hasConflict = true;
              } else if (
                (vm.selectedDuration === 0.25 || vm.selectedDuration === 0.5) &&
                event.isScheduled &&
                event.isScheduledPartially
              ) {
                hasConflict = true;
              }
            });
            if (hasConflict && allSchedules?.length > 0) {
              vm.eventSchedules = allSchedules[0]["schedules"];
              vm.handleCamuSchedulerEvent();
            } else {
              let snackbarData = {
                isOpen: true,
                message:
                  "You don't have any schedule for the selected date and duration.",
                type: "warning",
              };
              vm.showAlert(snackbarData);
              // enable the submit button
              vm.disableSubmitButton = false;
            }
          } else {
            vm.handleGetStaffSchedulesByDateRangeError(response);
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.disableSubmitButton = true;
          vm.showAlert({
            isOpen: true,
            type: "warning",
            message: err || "Something went wrong. Please try again.",
          });
        });
    },
    handleGetStaffSchedulesByDateRangeError(response = null) {
      let errorMessage = "Something went wrong. Please try again later.";
      if (response?.errors && response.errors.length > 0) {
        let errorCode = response.errors[0]?.extensions?.code;
        switch (errorCode) {
          case "DB0000":
            errorMessage =
              "There seems to be some technical issues. Please try after some time.";
            break;
          case "EI00105":
            this.disableSubmitButton = false;
            return;
          case "EI00111":
            errorMessage =
              "Unable to process the request for staff schedules based on a specific date range";
            break;
          case "EI00106":
            errorMessage = "Please provide a valid staff ID.";
            break;
          case "EI00107":
            errorMessage = "Staff details not found in CAMU.";
            break;
          case "EI00109":
            errorMessage = "Please provide valid date range";
            break;
          case "EI00110":
            errorMessage =
              "Please provide the date in the following format (YYYY-MM-DD). You can configure the same in organization settings.";
            break;
          default:
            errorMessage =
              "Something went wrong. If you continue to see this issue please contact the system administrator.";
        }
      }
      this.disableSubmitButton = true;
      this.showAlert({
        isOpen: true,
        type: "warning",
        message: errorMessage || "Something went wrong. Please try again.",
      });
    },
    async getLeaveSettings() {
      let vm = this;
      vm.listLoading = true;
      await vm.$apollo
        .query({
          query: GET_LEAVE_SETTINGS,
          variables: {
            formId: 332,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveLeaveSettings
          ) {
            const { leaveSettings } = response.data.retrieveLeaveSettings;
            vm.isCAMUSchedulerEnabled = leaveSettings?.Enable_CAMU_Scheduler;
          }
          vm.listLoading = false;
        })
        .catch(() => {
          vm.listLoading = false;
        });
    },
    async showSchedule() {
      await this.getLeaveSettings();
      let partnerid = window.$cookies.get("partnerid")
        ? window.$cookies.get("partnerid")
        : "-";

      if (
        partnerid.toLowerCase() === "camu" &&
        this.isCAMUSchedulerEnabled.toLowerCase() === "yes"
      ) {
        if (
          this.formattedCompOffDate &&
          (this.selectedDuration == "1" ||
            (this.selectedDuration && this.selectedPeriod))
        ) {
          this.getStaffSchedulesByDateRange();
        }
      }
    },
    resetCompOffDate() {
      this.formattedCompOffDate = "";
      this.eventSchedules = [];
    },
    handleCamuSchedulerEvent() {
      let snackbarData = {
        isOpen: true,
        message:
          "You have sessions scheduled during this period and requires to be delegated to others in CAMU before applying the leave",
        type: "warning",
      };
      this.showAlert(snackbarData);
      this.disableSubmitButton = true;
    },
    onUploadFile(files, documets) {
      if (!files || files.length === 0) return;

      this.isFormDirty = true;
      this.documentUpload = [...documets, ...files];
      const timestamp = moment().unix();

      for (let doc of this.documentUpload) {
        if (doc.size) {
          doc.formattedName =
            this.selectedEmployee +
            "-" +
            timestamp +
            "-" +
            `1` +
            "-" +
            doc.name;
        }
      }
    },
    checkDuplicateFiles(documentUpload) {
      return () => {
        const fileNames = documentUpload?.map((doc) => doc.name);
        const uniqueFiles = new Set(fileNames);
        if (fileNames.length !== uniqueFiles.size) {
          return "Duplicate files detected. Please remove duplicates.";
        }
        return true;
      };
    },
    checkSize(documentUpload) {
      return () => {
        if (documentUpload?.length) {
          for (let doc of documentUpload) {
            if (doc.size > 3000000) {
              return "Each file should be less than 3 MB.";
            }
          }
        }
        return true;
      };
    },
  },
};
</script>
<style scoped>
.overlay-card {
  height: 100vh;
  width: 35vw;
  overflow-y: auto;
}
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
</style>
