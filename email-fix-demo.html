<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Content Fixer Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .demo-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .demo-section {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        .before {
            background-color: #fff5f5;
            border-color: #fecaca;
        }
        .after {
            background-color: #f0fff4;
            border-color: #86efac;
        }
        .email-preview {
            border: 1px solid #ccc;
            padding: 15px;
            background: white;
            min-height: 200px;
            border-radius: 4px;
        }
        button {
            background: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background: #0052a3;
        }
        .code-block {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Email Content Fixer Demo</h1>
    <p>This demo shows how the email content fixer resolves formatting issues with lists and other elements.</p>

    <div>
        <h2>Your Original Problematic HTML:</h2>
        <div class="code-block" id="originalHtml"></div>
        
        <button onclick="fixEmailContent()">Fix Email Content</button>
        
        <div class="demo-container">
            <div class="demo-section before">
                <h3>❌ Before (Problematic)</h3>
                <div class="email-preview" id="beforePreview"></div>
            </div>
            
            <div class="demo-section after">
                <h3>✅ After (Fixed)</h3>
                <div class="email-preview" id="afterPreview"></div>
            </div>
        </div>
        
        <h2>Fixed HTML Output:</h2>
        <div class="code-block" id="fixedHtml"></div>
    </div>

    <script>
        // Your original problematic HTML
        const originalHtmlContent = `<p style="text-align: center; margin:0;"><strong>Dear Shanmukh()DuplicateCandidateTest,</strong><img src="https://caprice-dev-stage.s3.ap-south-1.amazonaws.com/hrapp/fieldforce/Email%20Template%20Image%20Upload/1745918710%3FemailTemplate%3F1%3FScreenshot%20%282%29.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&amp;X-Amz-Credential=ASIA2CRIEJOMQV3TQYPW%2F20250429%2Fap-south-1%2Fs3%2Faws4_request&amp;X-Amz-Date=20250429T092513Z&amp;X-Amz-Expires=3600&amp;X-Amz-Security-Token=IQoJb3JpZ2luX2VjEPH%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCmFwLXNvdXRoLTEiSDBGAiEAlGdTKWF53r%2FvglTLwvYI%2FbB0T6RfbI4FSRCgf8EQKP0CIQDqyvMsv%2FWkzoWB8ncN1gxDpxbDY3kajRSAJFDZsSnbTyr8AgiK%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F8BEAQaDDY5MjY0NzY0NDA1NyIM%2Fi3d5%2FmysyAGG%2FDQKtACHTMBvrYy3yPwlXHzvdZhbTZ%2BHFjJwvYNq%2B7kkuhElgAR8ofaUfUAUkxHhFMsqzyNNCK5%2FyCxkuWE6qZw880nbWH1uQi0ge65m4b896YIbLLMySFJF9GgQEvMZliUZDZMPzp73jxeLuHzrFhe7i%2BHG%2FhwDMM1J%2FRpjB%2FXkuL3WDgOexcF%2B7OtEkH0yOnlPk2r%2BpaqsftJsR4QQ1f03OxR2q7r8%2BXFsPbKkArBO91eulU2S0dN6WOoEq%2BkCkpCuPg8TNrop1MU3wNLXKQONqboJiBcmWXkVOLSxIdCnqZnozc6umXAKFcO6igo5%2F7hvDCjpKsZ3%2F0rR93IqF%2FQkX2cKBlRxRcfLsYIQmiRPw%2Ft90ecbVV115HfX0Y6BtUkp80NzwaljxZJ1N0bmyfBihN03HNrG7p7zZrefCIpKyNmHguKyFaze0on%2BkSt5nCYzx0IMP%2BuwsAGOp0BCaCqH3qumAoVVn6Me9YOo%2Fr7AHTMLaOVQfcGn8au%2BJGW6EV5%2B6QK1GRCVCYiX2GqZz5cBmOPfZ3gpH%2B%2BHuPgsaYCicJoLeyOTdeEZmwtiUQB4Jo693CLbL3kdfSKESHRryKiE%2FU%2BNZ7K1JHcz7YEQVANroKWZahqX2lrLyhINYQpGEwvcp1fARY3JYU5J%2BHk8jYm6GwCvZ9s6mS2sw%3D%3D&amp;X-Amz-Signature=1f3e0ef006d867de1857e2d4c695623948fb0f970df11aa59e5738bbb1703f88&amp;X-Amz-SignedHeaders=host"></p><p style="text-align: center; margin:0;"><br></p><p style="text-align: center; margin:0;">Thank you for your application and interest in the Business Quality Management Division Head position at rticle. We are pleased to invite you for a face-to-face interview as part of our selection process.</p><p style="text-align: center; margin:0;"><strong>Interview Details:</strong></p><ol><li style="text-align: center;"><span class="ql-ui" contenteditable="false"></span><strong>Date: June 7, 2025</strong></li><li style="text-align: center;"><span class="ql-ui" contenteditable="false"></span><strong>Time: June 7, 2025 01:30  to 02:00 (24hr Format)</strong></li><li style="text-align: center;"><span class="ql-ui" contenteditable="false"></span><strong>Location: <a href="https://www.google.com/maps?q=16.2418994,80.6572551">Adsumalli Vari Street, E T Colony, Ithanagar, Tenali, Andhra Pradesh, India, 522201, </a></strong></li></ol><p><br></p><ol><li style="text-align: center;"><span class="ql-ui" contenteditable="false"></span><strong>Point of Contact:</strong> PremKumar RP</li><li style="text-align: center;"><span class="ql-ui" contenteditable="false"></span><strong><a href="http://localhost:8080/v3/v3/recruitment/job-candidates?candidateId=530">Candidate Profile</a> : </strong></li></ol><p style="text-align: center; margin:0;">Please confirm your availability for the proposed schedule or let us know if adjustments are needed. Additionally, don't hesitate to reach out if you have any questions or require further details.</p><p style="text-align: center; margin:0;">We look forward to meeting you in person and discussing your qualifications further.</p><p style="text-align: center; margin:0;"><strong>Best regards,</strong></p><p style="text-align: center; margin:0;"><strong>PremKumar RP</strong></p><p style="text-align: center; margin:0;"><strong>Business Quality Management Division Head</strong></p><p style="text-align: center; margin:0;"><strong>rticle</strong></p><p><br></p><p><br></p><p><strong>Attachments:</strong></p><p><a href="https://caprice-dev-stage.s3.ap-south-1.amazonaws.com/hrapp/fieldforce/Email%20Template%20Document%20Upload/1738669918%3FemailTemplate%3F1%3FABCD.pdf" target="_blank">ABCD.pdf</a></p><p><a href="https://caprice-dev-stage.s3.ap-south-1.amazonaws.com/hrapp/fieldforce/Email%20Template%20Document%20Upload/1738669951%3FemailTemplate%3F1%3FWTC.pdf" target="_blank">WTC.pdf</a></p><p><br></p>`;

        // Email Content Fixer Function (simplified version for demo)
        function fixEmailContentDemo(htmlContent) {
            if (!htmlContent || typeof htmlContent !== 'string') {
                return htmlContent;
            }

            let fixedContent = htmlContent;

            // Remove rich text editor artifacts
            fixedContent = fixedContent
                .replace(/<span class="ql-ui"[^>]*><\/span>/g, '')
                .replace(/<span class="ql-ui"[^>]*>.*?<\/span>/g, '')
                .replace(/contenteditable="[^"]*"/g, '')
                .replace(/<span[^>]*><\/span>/g, '')
                .replace(/\s+/g, ' ')
                .trim();

            // Fix list formatting issues
            fixedContent = fixedContent.replace(
                /<ol([^>]*)>/g, 
                (match, attributes) => {
                    let cleanAttributes = attributes.replace(/style="[^"]*text-align:\s*center[^"]*"/g, '');
                    cleanAttributes = cleanAttributes.replace(/style="[^"]*"/g, '');
                    return `<ol${cleanAttributes} style="text-align: left; margin: 15px 0; padding-left: 30px; list-style-position: outside;">`;
                }
            );

            fixedContent = fixedContent.replace(
                /<li([^>]*)>/g,
                (match, attributes) => {
                    let cleanAttributes = attributes.replace(/style="[^"]*text-align:\s*center[^"]*"/g, '');
                    cleanAttributes = cleanAttributes.replace(/style="[^"]*"/g, '');
                    return `<li${cleanAttributes} style="margin: 8px 0; padding: 4px 0; text-align: left; line-height: 1.5;">`;
                }
            );

            // Add email wrapper and styles
            const emailStyles = `
                <style type="text/css">
                    .email-wrapper { 
                        max-width: 600px; 
                        margin: 0 auto; 
                        font-family: Arial, Helvetica, sans-serif; 
                        line-height: 1.6; 
                        color: #333333;
                        padding: 20px;
                    }
                    .email-wrapper p { 
                        margin: 12px 0; 
                        padding: 0;
                    }
                    .email-wrapper ol, .email-wrapper ul { 
                        text-align: left !important; 
                        margin: 15px 0; 
                        padding-left: 30px !important;
                        list-style-position: outside;
                    }
                    .email-wrapper li { 
                        margin: 8px 0; 
                        padding: 4px 0; 
                        text-align: left !important; 
                        line-height: 1.5;
                    }
                    .email-wrapper a { 
                        color: #0066cc; 
                        text-decoration: underline; 
                    }
                    .email-wrapper strong { 
                        font-weight: bold; 
                    }
                    .email-wrapper img { 
                        max-width: 100%; 
                        height: auto; 
                        display: block;
                    }
                </style>
            `;

            fixedContent = `
                ${emailStyles}
                <div class="email-wrapper">
                    ${fixedContent}
                </div>
            `;

            return fixedContent;
        }

        // Initialize the demo
        function initDemo() {
            document.getElementById('originalHtml').textContent = originalHtmlContent;
            document.getElementById('beforePreview').innerHTML = originalHtmlContent;
        }

        function fixEmailContent() {
            const fixedContent = fixEmailContentDemo(originalHtmlContent);
            document.getElementById('afterPreview').innerHTML = fixedContent;
            document.getElementById('fixedHtml').textContent = fixedContent;
        }

        // Initialize when page loads
        window.onload = initDemo;
    </script>
</body>
</html>
