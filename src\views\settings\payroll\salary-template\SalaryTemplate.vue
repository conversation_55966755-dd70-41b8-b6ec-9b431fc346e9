<template>
  <div>
    <div>
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="d-flex justify-end"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem" class="mt-3 mx-4">
          <div v-if="listLoading" class="mt-3">
            <!-- Skeleton loaders -->
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="itemList.length === 0"
            key="no-results-screen"
            :main-title="
              originalList.length === 0
                ? ''
                : 'There are no salary templates for the selected filters/searches.'
            "
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      :notes="$t('coreHr.documentSubtypeHelp1')"
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess.add"
                      variant="elevated"
                      class="ml-4 mt-1 primary"
                      rounded="lg"
                      prepend-icon="fas fa-plus"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onActions('add')"
                    >
                      <span>Add Template</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter()"
                    >
                      Reset Filter/Search
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      rounded="lg"
                      class="mt-1"
                      color="transparent"
                      variant="flat"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <div v-else>
            <div
              style="width: 100%"
              :class="{
                'py-1 mb-2': true,
                'd-flex justify-end align-center': !isMobileView,
                'd-flex justify-center align-center flex-wrap': isMobileView,
              }"
            >
              <v-btn
                v-if="formAccess?.add"
                color="primary"
                variant="elevated"
                class="mr-1"
                rounded="lg"
                prepend-icon="fas fa-plus"
                :size="isMobileView ? 'small' : 'default'"
                @click="onActions('add')"
              >
                Add Template
              </v-btn>
              <v-btn
                rounded="lg"
                color="transparent"
                variant="flat"
                :size="isMobileView ? 'small' : 'default'"
                @click="refetchList('Refetch List')"
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
              <v-menu v-model="openMoreMenu" transition="scale-transition">
                <template v-slot:activator="{ props }">
                  <v-btn variant="plain" v-bind="props">
                    <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                    <v-icon v-else>fas fa-caret-up</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in ['Export']"
                    :key="action"
                    @click="onActions(action)"
                  >
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            'bg-hover': isHovering,
                          }"
                          >{{ action }}</v-list-item-title
                        >
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
            <v-row>
              <v-col cols="12" :class="windowWidth < 1280 ? 'mb-12' : ''">
                <v-data-table
                  :headers="tableHeaders"
                  :items="itemList"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      windowWidth < 1280 ? 350 : 290,
                      itemList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    {
                      value: -1,
                      title: '$vuetify.dataFooter.itemsPerPageAll',
                    },
                  ]"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      @click="onActions('view', item)"
                      class="data-table-tr bg-white cursor-pointer"
                      :class="
                        isMobileView ? ' v-data-table__mobile-table-row' : ''
                      "
                    >
                      <td
                        class="bg-white"
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                        style="position: sticky; left: 0"
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Template Name
                        </div>
                        <section class="text-primary font-weight-medium">
                          <v-tooltip
                            :text="item.Template_Name"
                            location="bottom"
                          >
                            <template v-slot:activator="{ props }">
                              <div v-bind="props">
                                {{ checkNullValue(item.Template_Name) }}
                              </div>
                            </template>
                          </v-tooltip>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Description
                        </div>
                        <section>
                          {{ checkNullValue(item.Description) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Status
                        </div>
                        <section :class="getStatusColor(item.Template_Status)">
                          {{ checkNullValue(item.Template_Status) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="d-flex justify-center align-center"
                          style="width: 100%"
                        >
                          Actions
                        </div>
                        <section class="d-flex justify-end align-center">
                          <ActionMenu
                            v-if="itemActions.length"
                            :accessRights="formAccess"
                            @selected-action="onActions($event, item)"
                            :actions="itemActions"
                            iconColor="grey"
                          />
                          <div v-else>
                            <p>-</p>
                          </div>
                        </section>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
            <ViewForm
              v-if="showViewForm"
              :form-name="'Salary Template'"
              :form-id="206"
              :show-form="showViewForm"
              :selected-item="selectedItem"
              :payrollCurrency="payrollCurrency"
              @close-form="closeAllForms()"
            />
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import { RETRIEVE_SALARY_LIST } from "@/graphql/corehr/salaryQueries.js";

import moment from "moment";
import FileExportMixin from "@/mixins/FileExportMixin";
import { checkNullValue } from "@/helper";
import ViewForm from "@/views/coreHr/payroll-data-management/salary-info/ViewForm.vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard.vue")
);
export default {
  name: "SalaryInfo",
  components: {
    EmployeeDefaultFilterMenu,
    ActionMenu,
    ViewForm,
    NotesCard,
  },
  mixins: [FileExportMixin],
  data: () => ({
    currentTabItem: "",
    landedFormName: "Salary Template",
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
    itemList: [],
    originalList: [],
    payrollCurrency: "",
    openMoreMenu: false,
    selectedItem: null,
    showViewForm: false,
    showAddEditForm: false,
  }),
  computed: {
    tableHeaders() {
      let headers = [];
      headers.push(
        { title: "Template Name", key: "Template_Name" },
        { title: "Description", key: "Description" },
        { title: "Status", key: "Template_Status" },
        {
          title: "Actions",
          key: "action",
          align: "end",
          sortable: false,
          width: "10%",
        }
      );
      return headers;
    },
    getStatusColor() {
      return (status) => {
        if (status?.toLowerCase() === "active") {
          return "text-green";
        } else if (status?.toLowerCase() === "inactive") {
          return "text-red";
        }
        return "";
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    itemActions() {
      let actions = [];
      if (this.formAccess.view) {
        actions.push("View");
      }
      if (this.formAccess.update) {
        actions.push("Edit");
      }
      if (this.formAccess.delete) {
        actions.push("Delete");
      }
      return actions;
    },
    formAccess() {
      let formAccess = this.$store.getters.formIdBasedAccessRights(206);
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else return false;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    payrollGeneralAccess() {
      let payrollGenralConfigAccess = this.accessRights(261);
      if (
        payrollGenralConfigAccess &&
        payrollGenralConfigAccess.accessRights &&
        payrollGenralConfigAccess.accessRights["view"]
      ) {
        return payrollGenralConfigAccess.accessRights;
      } else return false;
    },
    mainTabs() {
      let tabs = [];
      if (this.payrollGeneralAccess && this.payrollGeneralAccess.view) {
        tabs.push("General");
      }
      tabs.push("Salary Template");
      return tabs;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat =
            this.$store.state.orgDetails.orgDateFormat + "HH:mm";
          return moment(date).format(orgDateFormat);
        } else return "";
      };
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },
  methods: {
    checkNullValue,
    onTabChange(tabName) {
      if (tabName.toLowerCase() === "general") {
        this.$router.push("/settings/payroll/general");
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    onActions(action, item) {
      if (action?.toLowerCase() === "export") {
        this.exportData();
      } else if (action?.toLowerCase() === "view") {
        this.selectedItem = item;
        this.showViewForm = true;
        this.showAddEditForm = false;
      } else if (action?.toLowerCase() === "edit") {
        this.selectedItem = item;
        this.isEdit = true;
        this.showAddEditForm = true;
      } else if (action?.toLowerCase() === "delete") {
        this.deleteItem(item);
      } else if (action?.toLowerCase() === "add") {
        this.selectedItem = null;
        this.isEdit = false;
        this.showAddEditForm = true;
      }
    },
    exportData() {
      const exportHeaders = [
        {
          header: "Template Name",
          key: "Template_Name",
        },
        {
          header: "Description",
          key: "Description",
        },
        {
          header: "Status",
          key: "Template_Status",
        },
        {
          header: "Added By",
          key: "AddedByName",
        },
        {
          header: "Added On",
          key: "Added_On",
        },
        {
          header: "Updated By",
          key: "UpdatedByName",
        },
        {
          header: "Updated On",
          key: "Updated_On",
        },
      ];
      this.itemList.forEach((element) => {
        element.Added_On = this.formatDate(element.Added_On);
        element.Updated_On = this.formatDate(element.Updated_On);
      });
      const exportOptions = {
        fileExportData: this.itemList,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
    },
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_SALARY_LIST,
          client: "apolloClientF",
          variables: {
            formId: 206,
          },
          fetchPolicy: "no-cache",
        })
        .then(({ data }) => {
          if (data?.listSalaryTemplateDetails?.templateDetails) {
            let salaryList = JSON.parse(
              data.listSalaryTemplateDetails.templateDetails
            );
            vm.payrollCurrency = data.listSalaryTemplateDetails.currencySymbol;
            vm.originalList = salaryList;
            vm.itemList = salaryList;
          } else {
            vm.originalList = [];
            vm.itemList = [];
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.originalList = [];
          vm.itemList = [];
          this.$store
            .dispatch("handleApiErrors", {
              error: err,
              action: "retrieve",
              form: "salary template",
              isListError: true,
            })
            .then((errorMessages) => {
              this.errorContent = errorMessages;
              this.isErrorInList = true;
            });
        });
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.fetchList();
    },
  },
};
</script>
<style scoped>
.container {
  padding: 58px 0px 0px 0px;
}
</style>
