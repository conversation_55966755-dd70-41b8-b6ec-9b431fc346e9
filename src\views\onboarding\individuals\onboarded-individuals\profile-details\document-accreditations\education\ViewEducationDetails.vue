<template>
  <div
    v-if="educationDetails && educationDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey py-4"
  >
    No education details have been added
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in educationDetails"
    :key="index"
    class="card-item d-flex rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width:250px; max-width:650px; border-left: 7px solid ${generateRandomColor()}; height:auto;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="mr-2 d-flex flex-column justify-start">
                <v-tooltip :text="data.Course_Name" location="bottom">
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="data.Course_Name ? props : ''"
                    >
                      {{ checkNullValue(data.Course_Name) }}
                      <span v-if="data.Year_Of_Passing">
                        - {{ data.Year_Of_Passing }}</span
                      >
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>
      <div class="card-columns w-100 mt-n8">
        <span
          class="d-flex align-start flex-column ml-3 my-3"
          :style="!isMobileView ? 'width:50%' : 'width:100%'"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div
              class="mr-2 d-flex flex-column justify-start"
              v-if="labelList[129]?.Field_Visiblity?.toLowerCase() == 'yes'"
            >
              <b class="mb-1 text-grey justify-start">
                {{ labelList[129].Field_Alias }}
              </b>
              <v-tooltip
                :text="
                  data?.Specialization_Id
                    ? data?.Specialization_Name
                    : data?.Specialisation
                "
                location="right"
              >
                <template v-slot:activator="{ props }">
                  <span
                    class="py-2 text-truncate"
                    :style="{ maxWidth: '225px' }"
                    v-bind="
                      (data?.Specialization_Id
                        ? data?.Specialization_Name
                        : data?.Specialisation
                      )?.length > 20
                        ? props
                        : ''
                    "
                  >
                    {{
                      data.Specialization_Id
                        ? checkNullValue(data.Specialization_Name)
                        : checkNullValue(data.Specialisation)
                    }}</span
                  >
                </template>
              </v-tooltip>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">University </b>
              <span
                :style="!isMobileView ? 'width:50%' : 'width:100%'"
                class="py-2"
              >
                {{ checkNullValue(data.University) }}</span
              >
            </div>
            <div
              class="d-flex flex-column justify-start"
              v-if="labelList[133]?.Field_Visiblity?.toLowerCase() == 'yes'"
            >
              <b class="mt-1 mr-2 text-grey justify-start">
                {{ labelList[133].Field_Alias }}
              </b>
              <span class="py-2"> {{ checkNullValue(data.Grade) }}</span>
            </div>
            <div
              class="d-flex flex-column justify-start"
              v-if="labelList[292]?.Field_Visiblity?.toLowerCase() == 'yes'"
            >
              <b class="mt-1 mr-2 text-grey justify-start">
                {{ labelList[292].Field_Alias }}
              </b>
              <span class="py-2">
                {{ checkNullValue(data.Year_Of_Start) }}</span
              >
            </div>
          </v-card-text>
        </span>
        <span
          class="d-flex align-start flex-column ml-3 my-3"
          :style="
            !isMobileView
              ? 'width:50%'
              : 'width:100% ; margin-top:-28px !important;margin-bottom: 10px !important;'
          "
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div
              class="d-flex flex-column justify-start"
              v-if="labelList[130]?.Field_Visiblity?.toLowerCase() == 'yes'"
            >
              <b class="mt-2 text-grey justify-start">
                {{ labelList[130].Field_Alias }}
              </b>
              <v-tooltip
                :text="
                  data?.Institution_Id
                    ? data?.Institution_Name_Table
                    : data?.Institute_Name
                "
                location="right"
              >
                <template v-slot:activator="{ props }">
                  <span
                    class="py-2 text-truncate"
                    :style="{ maxWidth: '225px' }"
                    v-bind="
                      (data?.Institution_Id
                        ? data?.Institution_Name_Table
                        : data?.Institute_Name
                      )?.length > 20
                        ? props
                        : ''
                    "
                  >
                    {{
                      data.Institution_Id
                        ? checkNullValue(data.Institution_Name_Table)
                        : checkNullValue(data.Institute_Name)
                    }}</span
                  >
                </template>
              </v-tooltip>
            </div>
            <div
              class="d-flex flex-column justify-start"
              v-if="labelList[132]?.Field_Visiblity?.toLowerCase() == 'yes'"
            >
              <b class="mt-2 text-grey justify-start">
                {{ labelList[132].Field_Alias }}
              </b>
              <span class="py-2"> {{ checkNullValue(data.Percentage) }}</span>
            </div>
            <div
              class="d-flex flex-column justify-start"
              v-if="labelList[134]?.Field_Visiblity?.toLowerCase() == 'yes'"
            >
              <b class="mt-2 text-grey justify-start">
                {{ labelList[134].Field_Alias }}
              </b>
              <span class="py-2">
                {{ checkNullValue(data.Year_Of_Passing) }}</span
              >
            </div>
            <div
              v-if="data.File_Name"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <span class="text-blue-grey-darken-3 font-weight-bold"></span>
              <span class="text-blue-grey-darken-6">
                <span
                  style="text-decoration: underline"
                  @click="retrieveDocuments(data.File_Name)"
                  class="text-green cursor-pointer"
                >
                  View Document</span
                >
              </span>
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="enableEdit" class="ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
  <FilePreviewModal
    v-if="openModal"
    :fileName="retrievedFileName"
    folderName="Employees Document Upload"
    fileRetrieveType="documents"
    @close-preview-modal="openModal = false"
  ></FilePreviewModal>
</template>

<script>
import { defineAsyncComponent } from "vue";
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);

export default {
  name: "ViewEducationDetails",
  components: { ActionMenu, FilePreviewModal },
  props: {
    educationDetails: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
  },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return {
      retrievedFileName: "",
      openModal: false,
      havingAccess: {},
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return this.formAccess && this.formAccess.update;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.educationDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", [selectedActionItem, "education"]);
      } else {
        this.$emit("on-open-edit", [selectedActionItem, "education"]);
      }
    },
    retrieveDocuments(fileName) {
      let vm = this;
      vm.retrievedFileName = fileName;
      vm.openModal = true;
    },
  },
};
</script>
