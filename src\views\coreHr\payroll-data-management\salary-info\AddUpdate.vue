<template>
  <v-overlay v-model="showOverlay" class="d-flex justify-end" persistent>
    <v-card height="100vh" :width="componentWidth">
      <v-card-title
        class="d-flex justify-space-between align-center bg-primary"
      >
        <div class="text-h6">
          {{ editType ? editType + " " : "Add " }}{{ landedFormName }}
        </div>
        <v-btn
          icon="fas fa-times"
          variant="text"
          @click="closeForm()"
          color="white"
        ></v-btn>
      </v-card-title>
      <v-card-text class="add-update-content">
        <v-form ref="form">
          <v-row class="mt-3">
            <v-col cols="12" md="4">
              <CustomSelect
                ref="employeeSelect"
                density="comfortable"
                label="Employee"
                :disabled="editType !== ''"
                :is-auto-complete="true"
                :items="employeeList"
                item-title="employeeData"
                item-value="employeeId"
                :is-required="true"
                :select-properties="{ clearable: true }"
                :rules="[required('Employee', selectedEmployee)]"
                :loading="employeeListLoading"
                :item-selected="selectedEmployee"
                @selected-item="onChangeEmployee($event)"
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="4">
              <CustomSelect
                ref="salaryTemplateSelect"
                density="comfortable"
                label="Salary Template"
                :items="templateList"
                :is-auto-complete="true"
                item-title="Template_Name"
                item-value="Template_Id"
                :is-required="true"
                :select-properties="{ clearable: true }"
                :rules="[required('Salary Template', selectedSalaryTemplate)]"
                :loading="salaryTemplateListLoading"
                :item-selected="selectedSalaryTemplate"
                @selected-item="onChangeTemplate($event)"
              ></CustomSelect>
            </v-col>
          </v-row>
          <div v-if="listLoading">
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <div v-else>
            <v-row>
              <v-col
                v-if="editType?.toLowerCase() === 'revise'"
                cols="12"
                sm="4"
                class="py-0"
              >
                <CustomSelect
                  ref="reviseTypeSelect"
                  density="comfortable"
                  label="Revise By"
                  :items="['Percentage', 'Amount']"
                  :item-selected="reviseType"
                  @selected-item="reviseType = $event"
                >
                </CustomSelect>
              </v-col>
              <v-col
                v-if="
                  reviseType?.toLowerCase() === 'percentage' &&
                  editType?.toLowerCase() === 'revise'
                "
                cols="12"
                sm="4"
                class="py-0"
              >
                <v-text-field
                  ref="percentageField"
                  variant="solo"
                  density="comfortable"
                  type="number"
                  v-model="percentage"
                  :rules="[required('Percentage', percentage)]"
                  @update:model-value="updatePercentage($event)"
                >
                  <template v-slot:append-inner>
                    <span
                      class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                      style="width: max-content; height: 100%"
                      >% of CTC</span
                    >
                  </template>
                  <template v-slot:label>
                    Percentage
                    <span class="text-red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                v-else-if="editType?.toLowerCase() === 'revise'"
                cols="12"
                sm="4"
                class="py-0"
              >
                <v-text-field
                  ref="amountField"
                  variant="solo"
                  density="comfortable"
                  type="number"
                  v-model="amount"
                  :rules="[required('Amount', amount)]"
                  @update:model-value="updatePercentage($event)"
                >
                  <template v-slot:label>
                    Amount
                    <span class="text-red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                v-if="editType?.toLowerCase() === 'revise'"
                cols="12"
                sm="4"
                class="py-0"
              >
                <v-text-field
                  ref="revisedSalaryEffectiveFromField"
                  readonly
                  variant="solo"
                  :rules="[
                    required(
                      'Salary Effective From',
                      formattedRevisedSalaryEffectiveFrom
                    ),
                  ]"
                  density="comfortable"
                  v-model="formattedRevisedSalaryEffectiveFrom"
                >
                  <template v-slot:prepend-inner>
                    <span
                      class="d-flex justify-center align-center bg-grey-lighten-3"
                      style="height: 100%; width: 40px"
                    >
                      <v-icon size="15" color="grey-darken-1"
                        >fas fa-calendar</v-icon
                      >
                    </span>
                  </template>
                  <template v-slot:label>
                    Salary Effective From
                    <span class="text-red">*</span>
                  </template>

                  <v-menu
                    activator="parent"
                    v-model="revisedSalaryEffectiveFromMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                  >
                    <Datepicker
                      v-model="revisedSalaryEffectiveFrom"
                      :inline="true"
                      :format="'MMMM, yyyy'"
                      :disabled-dates="getDisabledDates('effectivefrom')"
                      maximum-view="year"
                      minimum-view="month"
                      :open-date="
                        revisedSalaryEffectiveFrom
                          ? revisedSalaryEffectiveFrom
                          : new Date()
                      "
                      @update:modelValue="
                        revisedSalaryEffectiveFromMenu = false
                      "
                    />
                  </v-menu>
                </v-text-field>
              </v-col>
              <v-col
                v-if="editType?.toLowerCase() === 'revise'"
                cols="12"
                sm="4"
              >
                <v-text-field
                  ref="payoutMonthField"
                  readonly
                  variant="solo"
                  label="Payout Month"
                  density="comfortable"
                  :rules="[required('Payout Month', formattedPayoutMonth)]"
                  v-model="formattedPayoutMonth"
                >
                  <template v-slot:prepend-inner>
                    <span
                      class="d-flex justify-center align-center bg-grey-lighten-3"
                      style="height: 100%; width: 40px"
                    >
                      <v-icon size="15" color="grey-darken-1"
                        >fas fa-calendar</v-icon
                      >
                    </span>
                  </template>
                  <template v-slot:label>
                    Payout Month
                    <span class="text-red">*</span>
                  </template>

                  <v-menu
                    activator="parent"
                    v-model="payoutMonthMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                  >
                    <Datepicker
                      v-model="payoutMonth"
                      :inline="true"
                      :format="'MMMM, yyyy'"
                      maximum-view="year"
                      minimum-view="month"
                      :disabled-dates="getDisabledDates('payoutmonth')"
                      :open-date="payoutMonth ? payoutMonth : new Date()"
                      @update:modelValue="payoutMonthMenu = false"
                    />
                  </v-menu>
                </v-text-field>
              </v-col>
              <v-col
                v-if="selectedSalaryTemplate || editType !== ''"
                cols="12"
                sm="4"
              >
                <v-tooltip
                  text="Select a 'Revise By' type and input a value in the adjacent field to change this field."
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      ref="annualCTCField"
                      v-bind="editType?.toLowerCase() === 'revise' ? props : {}"
                      v-model="annualCTC"
                      variant="solo"
                      density="comfortable"
                      type="number"
                      :rules="[required('Annual CTC', annualCTC)]"
                      :readonly="editType?.toLowerCase() === 'revise'"
                      @update:model-value="updateAnnualCTC"
                    >
                      <template v-slot:prepend-inner>
                        <span
                          class="d-flex justify-center align-center bg-grey-lighten-3"
                          style="height: 100%; width: 40px"
                        >
                          {{ payrollCurrency }}
                        </span>
                      </template>
                      <template v-slot:label>
                        Annual CTC
                        <span class="text-red">*</span>
                      </template>
                    </v-text-field>
                  </template>
                </v-tooltip>
              </v-col>
              <v-col
                v-if="selectedSalaryTemplate || editType !== ''"
                cols="12"
                sm="4"
              >
                <v-tooltip
                  text="This is an auto-calculated value. Update the Annual CTC to change it."
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      v-bind="props"
                      v-model="monthlyCTC"
                      variant="solo"
                      density="comfortable"
                      type="number"
                      :readonly="true"
                      style="pointer-events: none"
                    >
                      <template v-slot:prepend-inner>
                        <span
                          class="d-flex justify-center align-center bg-grey-lighten-3"
                          style="height: 100%; width: 40px"
                        >
                          {{ payrollCurrency }}
                        </span>
                      </template>
                      <template v-slot:label>
                        Monthly CTC
                        <span class="text-red">*</span>
                      </template>
                    </v-text-field>
                  </template>
                </v-tooltip>
              </v-col>
              <v-col
                cols="12"
                sm="4"
                v-if="
                  formId === 207 &&
                  editType?.toLowerCase() !== 'revise' &&
                  selectedSalaryTemplate
                "
              >
                <v-text-field
                  ref="effectiveDateField"
                  v-model="formattedEffectiveForm"
                  density="comfortable"
                  :readonly="true"
                  :rules="[required('Effective From', formattedEffectiveForm)]"
                  variant="solo"
                  style="pointer-events: none"
                >
                  <template v-slot:prepend-inner>
                    <span
                      class="d-flex justify-center align-center bg-grey-lighten-3"
                      style="height: 100%; width: 40px"
                    >
                      <v-icon size="15" color="grey-darken-1"
                        >fas fa-calendar</v-icon
                      >
                    </span>
                  </template>
                  <template v-slot:label>
                    Effective From
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
            </v-row>
            <v-card v-if="selectedSalaryTemplate" class="pa-2 mt-3">
              <v-row>
                <v-col class="bg-grey-lighten-4">Components</v-col>
                <v-col class="bg-grey-lighten-4">Calculation Type</v-col>
                <v-col class="bg-grey-lighten-4 text-end">
                  <div class="pr-8">Monthly</div>
                </v-col>
                <v-col class="bg-grey-lighten-4 text-end">
                  <div class="pr-5">Annually</div>
                </v-col>
              </v-row>
              <v-row class="mt-2">
                <v-col class="font-weight-bold text-subtitle-1">
                  Earnings
                </v-col>
              </v-row>
              <v-row class="d-flex align-center mt-0">
                <v-col class="text-body-1"> Basic Pay </v-col>
                <v-col>
                  <v-text-field
                    ref="basicPayField"
                    v-if="
                      templateDetails?.BasicPay_Type?.toLowerCase() ===
                      'percentage'
                    "
                    v-model="basicPay"
                    variant="solo"
                    density="comfortable"
                    :rules="[required('Basic Pay', basicPay)]"
                    type="number"
                    @update:model-value="updateBasicPay"
                  >
                    <template v-slot:append-inner>
                      <span
                        v-if="
                          templateDetails?.BasicPay_Type?.toLowerCase() ===
                          'percentage'
                        "
                        class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                        style="width: max-content; height: 100%"
                        >% of CTC</span
                      >
                    </template>
                  </v-text-field>
                  <div v-else>Fixed Amount</div>
                </v-col>
                <v-col class="text-body-1 custom-input-position">
                  <v-text-field
                    ref="monthlyBasicPayField"
                    v-model="monthlyBasicPay"
                    :readonly="
                      templateDetails?.BasicPay_Type?.toLowerCase() ===
                      'percentage'
                    "
                    :style="
                      templateDetails?.BasicPay_Type?.toLowerCase() ===
                      'percentage'
                        ? 'pointer-events: none'
                        : ''
                    "
                    variant="solo"
                    density="comfortable"
                    :rules="[required('Basic Pay', monthlyBasicPay)]"
                    type="number"
                    @update:model-value="updateBasicPay"
                  >
                  </v-text-field>
                </v-col>
                <v-col class="text-body-1 text-end">
                  <div class="pr-5">
                    {{ monthlyBasicPay ? monthlyBasicPay * 12 : 0 }}
                  </div>
                </v-col>
              </v-row>
              <div v-if="templateDetails.allowances?.allowanceArray?.length">
                <v-row
                  v-for="(innerItems, index) in templateDetails.allowances[
                    'allowanceArray'
                  ]"
                  :key="index"
                  class="d-flex align-center mt-0"
                >
                  <v-col class="text-body-1 no-padding">
                    {{ innerItems.Allowance_Name }}
                  </v-col>
                  <v-col>
                    <v-text-field
                      :ref="'allowanceAmountField' + index"
                      v-if="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                      "
                      v-model="innerItems.Percentage"
                      variant="solo"
                      density="comfortable"
                      :rules="[
                        required(
                          innerItems.Allowance_Name,
                          innerItems.Percentage
                        ),
                      ]"
                      type="number"
                      @update:model-value="updateAllowanceAmount(innerItems)"
                    >
                      <template v-slot:append-inner>
                        <span
                          class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                          style="width: max-content; height: 100%"
                          >% of Basic</span
                        >
                      </template>
                    </v-text-field>
                    <div v-else>Fixed Amount</div>
                  </v-col>
                  <v-col class="text-body-1 custom-input-position">
                    <v-text-field
                      :ref="'monthlyAllowanceAmountField' + index"
                      v-model="innerItems.Amount"
                      :readonly="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                      "
                      variant="solo"
                      class="text-right"
                      density="comfortable"
                      :rules="[
                        required(innerItems.Allowance_Name, innerItems.Amount),
                      ]"
                      :style="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                          ? 'pointer-events: none'
                          : ''
                      "
                      type="number"
                      @update:model-value="calculateSumOfComponetsMinusCTC()"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col class="text-body-1 text-end">
                    <div class="pr-5">
                      {{ innerItems.Amount ? innerItems.Amount * 12 : 0 }}
                    </div>
                  </v-col>
                </v-row>
              </div>
              <div
                v-if="templateDetails.allowances?.fixedAllowanceArray?.length"
              >
                <v-row
                  v-for="(innerItems, index) in templateDetails.allowances[
                    'fixedAllowanceArray'
                  ]"
                  :key="index"
                  class="d-flex align-center mt-0"
                >
                  <v-col class="text-body-1">
                    {{ innerItems.Allowance_Name }}
                  </v-col>
                  <v-col> Fixed Amount </v-col>
                  <v-col
                    class="text-body-1"
                    :class="anyValueUpdated ? 'text-start' : 'text-end'"
                  >
                    <div :class="anyValueUpdated ? '' : 'pr-8'">
                      {{
                        anyValueUpdated
                          ? "System Calculated"
                          : fixedAllowanceAmount
                      }}
                    </div>
                  </v-col>
                  <v-col
                    class="text-body-1"
                    :class="anyValueUpdated ? 'text-start' : 'text-end'"
                  >
                    <div :class="anyValueUpdated ? '' : 'pr-5'">
                      {{
                        anyValueUpdated
                          ? "System Calculated"
                          : fixedAllowanceAmount
                          ? fixedAllowanceAmount * 12
                          : 0
                      }}
                    </div>
                  </v-col>
                </v-row>
              </div>
              <div
                v-if="templateDetails?.allowances?.reimbursementArray?.length"
              >
                <v-row class="mt-2">
                  <v-col class="font-weight-bold text-subtitle-1 no-padding">
                    Reimbursement
                  </v-col>
                </v-row>
                <v-row
                  v-for="(innerItems, index) in templateDetails.allowances[
                    'reimbursementArray'
                  ]"
                  :key="index"
                  class="d-flex align-center mt-0"
                >
                  <v-col class="text-body-1 no-padding">
                    {{ innerItems.Allowance_Name }}
                  </v-col>
                  <v-col>
                    <v-text-field
                      :ref="'reimbursementAmountField' + index"
                      v-if="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                      "
                      v-model="innerItems.Percentage"
                      variant="solo"
                      density="comfortable"
                      :rules="[
                        required(
                          innerItems.Allowance_Name,
                          innerItems.Percentage
                        ),
                      ]"
                      type="number"
                      @update:model-value="updateAllowanceAmount(innerItems)"
                    >
                      <template v-slot:append-inner>
                        <span
                          class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                          style="width: max-content; height: 100%"
                          >% of Basic</span
                        >
                      </template>
                    </v-text-field>
                    <div v-else>Fixed Amount</div>
                  </v-col>
                  <v-col class="text-body-1 custom-input-position">
                    <v-text-field
                      :ref="'monthlyReimbursementAmountField' + index"
                      v-model="innerItems.Amount"
                      :readonly="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                      "
                      variant="solo"
                      density="comfortable"
                      :rules="[
                        required(innerItems.Allowance_Name, innerItems.Amount),
                      ]"
                      :style="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                          ? 'pointer-events: none'
                          : ''
                      "
                      type="number"
                      @update:model-value="calculateSumOfComponetsMinusCTC"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col class="text-body-1 text-end">
                    <div class="pr-5">
                      {{ innerItems.Amount ? innerItems.Amount * 12 : 0 }}
                    </div>
                  </v-col>
                </v-row>
              </div>
              <div
                v-if="
                  templateDetails?.allowances?.flexiBenefitPlanArray?.length
                "
              >
                <v-row class="mt-2">
                  <v-col class="font-weight-bold text-subtitle-1">
                    Flexi Benefit Plan
                  </v-col>
                </v-row>
                <v-row
                  v-for="(innerItems, index) in templateDetails.allowances[
                    'flexiBenefitPlanArray'
                  ]"
                  :key="index"
                  class="d-flex align-center mt-0"
                >
                  <v-col class="text-body-1 no-padding">
                    {{ innerItems.Allowance_Name }}
                  </v-col>
                  <v-col>
                    <v-text-field
                      :ref="'flexiAmountField' + index"
                      v-if="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                      "
                      v-model="innerItems.Percentage"
                      variant="solo"
                      density="comfortable"
                      :rules="[
                        required(
                          innerItems.Allowance_Name,
                          innerItems.Percentage
                        ),
                      ]"
                      type="number"
                      @update:model-value="updateAllowanceAmount(innerItems)"
                    >
                      <template v-slot:append-inner>
                        <span
                          class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                          style="width: max-content; height: 100%"
                          >% of Basic</span
                        >
                      </template>
                    </v-text-field>
                    <div v-else>Fixed Amount</div>
                  </v-col>
                  <v-col class="text-body-1 custom-input-position">
                    <v-text-field
                      :ref="'monthlyFlexiAmountField' + index"
                      v-model="innerItems.Amount"
                      :readonly="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                      "
                      variant="solo"
                      density="comfortable"
                      :rules="[
                        required(innerItems.Allowance_Name, innerItems.Amount),
                      ]"
                      :style="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                          ? 'pointer-events: none'
                          : ''
                      "
                      type="number"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col class="text-body-1 text-end">
                    <div class="pr-5">
                      {{ innerItems.Amount ? innerItems.Amount * 12 : 0 }}
                    </div>
                  </v-col>
                </v-row>
              </div>
              <div v-if="templateDetails?.allowances?.bonusArray?.length">
                <v-row class="mt-2">
                  <v-col class="font-weight-bold text-subtitle-1 no-padding">
                    Bonus
                  </v-col>
                </v-row>
                <v-row
                  v-for="(innerItems, index) in templateDetails.allowances[
                    'bonusArray'
                  ]"
                  :key="index"
                  class="d-flex align-center mt-0"
                >
                  <v-col class="text-body-1 no-padding">
                    {{ innerItems.Allowance_Name }}
                  </v-col>
                  <v-col>
                    <v-text-field
                      :ref="'bonusAmountField' + index"
                      v-if="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                      "
                      v-model="innerItems.Percentage"
                      variant="solo"
                      density="comfortable"
                      :rules="[
                        required(
                          innerItems.Allowance_Name,
                          innerItems.Percentage
                        ),
                      ]"
                      type="number"
                      @update:model-value="updateAllowanceAmount(innerItems)"
                    >
                      <template v-slot:append-inner>
                        <span
                          class="bg-grey-lighten-3 d-flex justify-center align-center px-3"
                          style="width: max-content; height: 100%"
                          >%</span
                        >
                      </template>
                    </v-text-field>
                    <div v-else>Fixed Amount</div>
                  </v-col>
                  <v-col class="text-body-1 custom-input-position">
                    <v-text-field
                      :ref="'monthlyBonusAmountField' + index"
                      v-model="innerItems.Amount"
                      :readonly="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                      "
                      variant="solo"
                      density="comfortable"
                      :rules="[
                        required(innerItems.Allowance_Name, innerItems.Amount),
                      ]"
                      :style="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                          ? 'pointer-events: none'
                          : ''
                      "
                      type="number"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col class="text-body-1 text-end">
                    <div class="pr-5">
                      {{ innerItems.Amount ? innerItems.Amount * 12 : 0 }}
                    </div>
                  </v-col>
                </v-row>
              </div>
              <div v-if="templateDetails.retirals?.length && selectedEmployee">
                <v-row class="mt-2">
                  <v-col class="font-weight-bold text-subtitle-1 no-padding">
                    Retiral
                  </v-col>
                </v-row>
                <div
                  v-for="(innerItems, index) in templateDetails.retirals"
                  :key="index"
                >
                  <v-row
                    v-if="checkEmployeeEligible(innerItems.Retirals_Name)"
                    class="d-flex align-center mt-0"
                  >
                    <v-col class="text-body-1 d-flex align-center">
                      <span class="mr-2">{{
                        innerItems.Retirals_Name?.toLowerCase() === "insurance"
                          ? innerItems.Insurance_Name
                          : getCustomFormName(innerItems.Form_Id)
                          ? getCustomFormName(innerItems.Form_Id)
                          : innerItems.Retirals_Name
                      }}</span>
                      <v-tooltip
                        v-if="
                          getCustomText(innerItems.Allowance_Ids) &&
                          innerItems.Retirals_Type?.toLowerCase() ===
                            'percentage'
                        "
                        :text="getCustomText(innerItems.Allowance_Ids)"
                      >
                        <template v-slot:activator="{ props }">
                          <span class="custom-info-icon">
                            <v-icon
                              color="grey-lighten-1"
                              size="10"
                              v-bind="props"
                              >fas fa-info</v-icon
                            >
                          </span>
                        </template>
                      </v-tooltip>
                    </v-col>
                    <v-col>
                      <div
                        v-if="
                          innerItems?.Retirals_Type?.toLowerCase() ===
                          'percentage'
                        "
                      >
                        {{ innerItems.Percentage + "%" }}
                      </div>
                      <div v-else>Fixed Amount</div>
                    </v-col>
                    <v-col
                      class="text-body-1"
                      :class="anyValueUpdated ? 'text-start' : 'text-end'"
                    >
                      <div :class="anyValueUpdated ? '' : 'pr-8'">
                        {{
                          anyValueUpdated
                            ? "System Calculated"
                            : innerItems.Amount
                        }}
                      </div>
                    </v-col>
                    <v-col
                      class="text-body-1"
                      :class="anyValueUpdated ? 'text-start' : 'text-end'"
                    >
                      <div :class="anyValueUpdated ? '' : 'pr-5'">
                        {{
                          anyValueUpdated
                            ? "System Calculated"
                            : innerItems.Amount * 12
                        }}
                      </div>
                    </v-col>
                  </v-row>
                </div>
              </div>
            </v-card>
            <v-card
              v-if="anyValueUpdated"
              class="pa-2 d-flex align-center mt-3"
            >
              <v-row>
                <v-col class="d-flex align-center">
                  <v-icon color="blue" class="mr-4">fas fa-info-circle</v-icon>
                  <div>
                    <div>
                      <span class="text-subtitle-1 font-weight-medium mr-2"
                        >System Calculated Components' Total</span
                      >
                      <span
                        class="text-blue text-subtitle-1 font-weight-medium cursor-pointer"
                        v-if="sumOfComponetsMinusCTC >= 0 && !componentsLoading"
                        @click="getSystemCalculatedComponets()"
                        >(Preview)</span
                      >
                      <span v-else-if="componentsLoading"
                        >Calculating
                        <v-progress-circular
                          size="15"
                          color="primary"
                          indeterminate
                        ></v-progress-circular
                      ></span>
                    </div>
                    <div
                      class="text-caption text-grey-darken-1"
                      v-if="sumOfComponetsMinusCTC < 0"
                    >
                      Amount must be greater than zero. Adjust the CTC or any of
                      the component's amount.
                    </div>
                  </div>
                </v-col>
                <v-col
                  cols="3"
                  :class="sumOfComponetsMinusCTC < 0 ? 'text-red' : ''"
                  class=""
                >
                  {{ sumOfComponetsMinusCTC }}
                </v-col>
                <v-col
                  cols="3"
                  :class="sumOfComponetsMinusCTC < 0 ? 'text-red' : ''"
                  class=""
                >
                  {{ sumOfComponetsMinusCTC * 12 }}
                </v-col>
              </v-row>
            </v-card>
            <v-card v-if="selectedSalaryTemplate" class="pa-2 mt-3">
              <v-row>
                <v-col cols="6" class="bg-grey-lighten-4 text-body-1"
                  >Cost to Company (CTC)</v-col
                >
                <v-col class="bg-grey-lighten-4 text-end">
                  <div class="pr-8 text-body-1">
                    {{ payrollCurrency }}
                    {{ monthlyCTC }}
                  </div>
                </v-col>
                <v-col class="bg-grey-lighten-4 text-end">
                  <div class="pr-5 text-body-1">
                    {{ payrollCurrency }}
                    {{ annualCTC }}
                  </div>
                </v-col>
              </v-row>
            </v-card>
          </div>
        </v-form>
      </v-card-text>
      <v-card
        width="100%"
        class="pa-2 d-flex justify-end position-absolute bottom-0"
        elevation="16"
      >
        <v-btn
          class="mr-2"
          variant="text"
          elevation="4"
          rounded="lg"
          @click="closeForm()"
          >Cancel</v-btn
        >
        <v-btn
          rounded="lg"
          :disabled="anyValueUpdated || sumOfComponetsMinusCTC < 0"
          @click="validateForm()"
          >Save</v-btn
        >
      </v-card>
      <AppLoading v-if="isLoading"></AppLoading>
    </v-card>
  </v-overlay>
  <AppWarningModal
    v-if="openConfirmationModel"
    :open-modal="openConfirmationModel"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationModel = false"
    @accept-modal="$emit('close-form')"
  />
</template>

<script>
import { defineAsyncComponent } from "vue";
import {
  ADD_UPDATE_SALARY_DETAILS,
  RETRIEVE_SYSTEM_CALCULATED_COMPONENTS,
} from "@/graphql/corehr/salaryQueries.js";
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import { checkNullValue } from "@/helper";
const CustomSelect = defineAsyncComponent(() =>
  import("@/components/custom-components/CustomSelect.vue")
);

import Datepicker from "vuejs3-datepicker";
export default {
  name: "AddUpdate",
  components: { CustomSelect, Datepicker },
  mixins: [validationRules],
  emits: ["close-form", "add-update-success"],
  props: {
    showForm: {
      type: Boolean,
      default: false,
    },
    landedFormName: {
      type: String,
      required: true,
    },
    payrollCurrency: {
      type: String,
      required: true,
    },
    editType: {
      type: String,
      default: "",
    },
    formId: {
      type: Number,
      default: 207,
    },
    selectedItem: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      showOverlay: false,
      employeeListLoading: false,
      selectedEmployee: null,
      employeeList: [],
      salaryTemplateListLoading: false,
      selectedSalaryTemplate: null,
      templateList: [],
      templateDetails: {},
      annualCTC: null,
      basicPay: null,
      monthlyBasicPay: null,
      isLoading: false,
      openConfirmationModel: false,
      monthlyCTC: null,
      formattedEffectiveForm: "",
      listLoading: false,
      anyValueUpdated: false,
      sumOfComponetsMinusCTC: 0,
      fixedAllowanceAmount: 0,
      componentsLoading: false,
      reviseType: "Percentage",
      percentage: null,
      amount: null,
      revisedSalaryEffectiveFromMenu: false,
      revisedSalaryEffectiveFrom: null,
      formattedRevisedSalaryEffectiveFrom: "",
      payoutMonth: null,
      payoutMonthMenu: false,
      formattedPayoutMonth: "",
    };
  },
  computed: {
    componentWidth() {
      if (this.windowWidth > 1430) {
        return "60vw";
      } else if (this.windowWidth > 1260 && this.windowWidth <= 1430) {
        return "60vw";
      } else if (this.windowWidth > 960 && this.windowWidth <= 1260) {
        return "70vw";
      } else {
        return "100vw";
      }
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    getGroupName() {
      return (groupName) => {
        let name = "";
        if (groupName) {
          name = groupName.split("Array");
          if (name.length) {
            name = name[0];
            name = name.split(/(?=[A-Z])/).join(" ");
            name = name.charAt(0).toUpperCase() + name.slice(1);
          }
        }
        return name;
      };
    },
    formsBasedOnFormId() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    getCustomFormName() {
      return (formId) => {
        let form = this.formsBasedOnFormId(formId);
        return form ? form.customFormName : "";
      };
    },
    getCustomText() {
      return (formIds) => {
        let allowanceIds = formIds.split(",");
        allowanceIds = new Set(allowanceIds);
        let formNames = [];
        if (allowanceIds.size && this.templateDetails.allowances) {
          Object.keys(this.templateDetails.allowances).forEach((key) => {
            if (this.templateDetails.allowances[key].length) {
              this.templateDetails.allowances[key]?.forEach((item) => {
                if (allowanceIds.has(item.Allowance_Id.toString())) {
                  formNames.push(item.Allowance_Name);
                }
              });
            }
          });
        }
        return formNames.join(" + ");
      };
    },
    getAllowanceAmount() {
      return (item) => {
        const mutiplier = item.Percentage / 100;
        const amount = Math.ceil((this.annualCTC * mutiplier) / 12);
        return amount ? amount : 0;
      };
    },
    monthlyGrossSalary() {
      let total = 0;
      total += parseInt(this.monthlyBasicPay);
      let keysToCheck = [
        "allowanceArray",
        "flexiBenefitPlanArray",
        "reimbursementArray",
      ];
      if (
        this.templateDetails.allowances &&
        Object.keys(this.templateDetails.allowances).length
      ) {
        keysToCheck.forEach((key) => {
          if (this.templateDetails.allowances[key].length) {
            this.templateDetails.allowances[key].forEach((item) => {
              total = total + (parseInt(item.Amount) || 0);
            });
          }
        });
      }
      total = total + (parseInt(this.fixedAllowanceAmount) || 0);
      return total;
    },
    checkEmployeeEligible() {
      return (retiralType) => {
        if (this.editType) return true;
        if (this.selectedEmployee) {
          let selectedEmployeeObj = this.employeeList.find(
            (employee) => employee.employeeId === this.selectedEmployee
          );
          if (
            retiralType?.toLowerCase() === "provident fund" &&
            selectedEmployeeObj?.eligibleForPf
          ) {
            return true;
          } else if (
            retiralType?.toLowerCase() === "insurance" &&
            selectedEmployeeObj?.eligibleForInsurance
          ) {
            return true;
          } else if (
            retiralType?.toLowerCase() === "nps" &&
            selectedEmployeeObj?.eligibleForNps
          ) {
            return true;
          } else if (
            retiralType?.toLowerCase() === "gratuity" &&
            selectedEmployeeObj?.eligibleForGratuity
          ) {
            return true;
          } else if (
            retiralType?.toLowerCase() === "esi" &&
            selectedEmployeeObj?.eligibleForESI
          ) {
            return true;
          } else return false;
        }
        return false;
      };
    },
    getDisabledDates() {
      return (type) => {
        if (type?.toLowerCase() === "effectivefrom") {
          if (this.templateDetails?.Effective_From) {
            return {
              to: new Date(this.templateDetails.Effective_From),
            };
          } else {
            return {};
          }
        } else if (type?.toLowerCase() === "payoutmonth") {
          if (this.revisedSalaryEffectiveFrom) {
            return {
              to: moment(this.revisedSalaryEffectiveFrom)
                .add(1, "month")
                .toDate(),
            };
          } else {
            return {};
          }
        }
      };
    },
  },
  watch: {
    showForm(val) {
      this.showOverlay = val;
    },
    revisedSalaryEffectiveFrom() {
      if (
        this.revisedSalaryEffectiveFrom &&
        moment(this.revisedSalaryEffectiveFrom).isValid()
      ) {
        this.formattedRevisedSalaryEffectiveFrom = moment(
          this.revisedSalaryEffectiveFrom
        ).format("MMM YYYY");
      }
    },
    payoutMonth() {
      if (this.payoutMonth && moment(this.payoutMonth).isValid()) {
        this.formattedPayoutMonth = moment(this.payoutMonth).format("MMM YYYY");
      }
    },
  },
  mounted() {
    this.showOverlay = this.showForm;
    this.getSalartTemplateList();
    if (this.editType) {
      this.employeeList = [
        {
          employeeId: this.selectedItem?.Employee_Id,
          employeeData:
            this.selectedItem?.Employee_Name +
            " - " +
            this.selectedItem?.User_Defined_EmpId,
        },
      ];
      this.selectedEmployee = this.selectedItem?.Employee_Id;
      this.selectedSalaryTemplate = this.selectedItem?.Template_Id;
      this.formattedEffectiveForm = this.selectedItem?.Effective_From;
      let variables = {
        formId: 207,
        isViewMode: true,
        isDropdown: false,
        id: 1,
        employeeId: this.selectedItem?.Employee_Id,
      };
      this.getSalaryDetails(variables);
    } else {
      this.getemployeeList();
    }
  },
  methods: {
    checkNullValue,
    closeForm() {
      this.openConfirmationModel = true;
    },
    onChangeTemplate(templateId) {
      this.selectedSalaryTemplate = templateId;
      let variables = {
        formId: 206,
        isViewMode: true,
        isDropdown: false,
        templateId: templateId,
      };
      this.getSalaryDetails(variables);
    },
    calculateSumOfComponetsMinusCTC() {
      this.anyValueUpdated = true;
      let total = 0;
      total += parseInt(this.monthlyBasicPay);
      if (this.templateDetails?.allowances?.allowanceArray?.length) {
        this.templateDetails.allowances.allowanceArray.forEach((item) => {
          total += parseInt(item.Amount);
        });
      }
      if (this.templateDetails?.allowances?.flexiBenefitPlanArray?.length) {
        this.templateDetails.allowances.flexiBenefitPlanArray.forEach(
          (item) => {
            total += parseInt(item.Amount);
          }
        );
      }
      if (this.templateDetails?.allowances?.reimbursementArray?.length) {
        this.templateDetails.allowances.reimbursementArray.forEach((item) => {
          total += parseInt(item.Amount);
        });
      }
      if (this.templateDetails?.allowances?.bonusArray?.length) {
        this.templateDetails.allowances.bonusArray.forEach((item) => {
          total += parseInt(item.Amount);
        });
      }
      this.sumOfComponetsMinusCTC = this.monthlyCTC - total;
    },
    onChangeEmployee(event) {
      this.selectedEmployee = event;
      let selectedEmployee = this.employeeList.find(
        (emp) => emp.employeeId === event
      );
      const dateOfJoin = selectedEmployee?.dateOfJoin;
      this.formattedEffectiveForm = moment(dateOfJoin).format(
        this.$store.state.orgDetails.orgDateFormat
      );
    },
    updatePercentage(value) {
      if (this.reviseType?.toLowerCase() === "percentage") {
        let percentageValue = value ? value : 0;
        this.annualCTC =
          this.templateDetails.Annual_CTC +
          Math.ceil((percentageValue / 100) * this.templateDetails.Annual_CTC);
      } else {
        this.annualCTC = this.templateDetails.Annual_CTC + parseInt(value);
      }
      this.updateAnnualCTC();
    },
    calculateFixedAllowanceAmount() {
      let total = 0;
      total += parseInt(this.monthlyBasicPay);
      let keysToCheck = [
        "allowanceArray",
        "flexiBenefitPlanArray",
        "reimbursementArray",
        "bonusArray",
      ];
      keysToCheck.forEach((key) => {
        if (this.templateDetails.allowances?.[key]?.length) {
          this.templateDetails.allowances[key].forEach((item) => {
            total += parseInt(item.Amount);
          });
        }
      });
      this.templateDetails?.retirals?.forEach((item) => {
        total += parseInt(item.Amount);
      });
      this.fixedAllowanceAmount = this.monthlyCTC - total;
    },
    async getemployeeList() {
      let vm = this;
      vm.employeeListLoading = true;
      await vm.$store
        .dispatch("getEmployeesList", {
          formId: vm.formId,
          formName: "Salary Details",
          fetchPolicy: "no-cache",
        })
        .then((empData) => {
          if (empData && empData.length) {
            let empList = empData.map((item) => ({
              ...item,
              employeeData: item.employeeName + " - " + item.userDefinedEmpId,
            }));
            vm.employeeList = [...empList];
          } else {
            vm.employeeList = [];
          }
          vm.employeeListLoading = false;
        })
        .catch((err) => {
          vm.employeeListLoading = false;
          vm.employeeList = [];
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "employee list",
            isListError: false,
          });
        });
    },
    async getSalartTemplateList() {
      let vm = this;
      vm.salaryTemplateListLoading = true;
      await vm.$store
        .dispatch("getSalaryDetails", { formId: 206, isDropdown: true })
        .then(({ data }) => {
          if (data?.listSalaryTemplateDetails?.templateDetails) {
            let templateList = JSON.parse(
              data.listSalaryTemplateDetails.templateDetails
            );
            if (templateList) {
              vm.templateList = templateList;
            } else {
              vm.templateList = [];
            }
          } else {
            vm.templateList = [];
          }
          vm.salaryTemplateListLoading = false;
        })
        .catch((err) => {
          vm.templateList = [];
          vm.salaryTemplateListLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "salary details",
            isListError: false,
          });
        });
    },
    async getSalaryDetails(variables) {
      let vm = this;
      vm.listLoading = true;
      await vm.$store
        .dispatch("getSalaryDetails", variables)
        .then(({ data }) => {
          if (data?.listSalaryTemplateDetails?.templateDetails) {
            let templateDetails = JSON.parse(
              data.listSalaryTemplateDetails.templateDetails
            );
            if (templateDetails) {
              vm.templateDetails = templateDetails[0];
              vm.processData();
            } else {
              vm.templateList = [];
            }
          } else {
            vm.templateList = [];
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.templateList = [];
          vm.listLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "salary details",
            isListError: false,
          });
        });
    },
    processData() {
      this.annualCTC = this.templateDetails?.Annual_CTC;
      this.monthlyCTC = Math.ceil(this.annualCTC / 12);
      if (this.templateDetails?.allowances?.basicPayArray?.length) {
        if (
          this.templateDetails?.allowances?.basicPayArray[0]?.Allowance_Type?.toLowerCase() ===
          "percentage"
        ) {
          this.templateDetails.BasicPay_Type = "Percentage";
          this.basicPay =
            this.templateDetails?.allowances?.basicPayArray[0]?.Percentage;
          const mutiplier = this.basicPay / 100;
          const basicPay = Math.ceil((this.annualCTC * mutiplier) / 12);
          this.monthlyBasicPay = basicPay ? Math.ceil(basicPay) : 0;
        } else {
          this.monthlyBasicPay = Math.ceil(
            this.templateDetails?.allowances?.basicPayArray[0]?.Amount
          );
          this.basicPay = null;
        }
      } else {
        this.monthlyBasicPay = 0;
        this.basicPay = null;
      }
      this.updateBasicPay("mounted");
      if (
        this.templateDetails.retirals &&
        this.templateDetails.retirals.length
      ) {
        this.templateDetails.retirals.forEach((item) => {
          if (
            item.Retirals_Type?.toLowerCase() === "percentage" &&
            !item.Employer_Share_Amount
          ) {
            item.Percentage = Number(item.Employer_Share_Percentage ?? 0);
            const mutiplier = item.Employer_Retiral_Wages || 0;
            const basicPay = Math.ceil((item.Percentage / 100) * mutiplier);
            item.Amount = basicPay ? basicPay : 0;
          } else {
            item.Percentage = item.Employer_Share_Percentage || 0;
            item.Amount = Number(item.Employer_Share_Amount ?? 0);
          }
        });
      }
      this.calculateFixedAllowanceAmount();
    },
    updateAllowanceAmount(item) {
      const mutiplier = item.Percentage / 100;
      const amount = Math.ceil(this.monthlyBasicPay * mutiplier);
      item.Amount = amount ? amount : 0;
      this.calculateSumOfComponetsMinusCTC();
    },
    updateBasicPay(msg = "") {
      if (this.templateDetails?.BasicPay_Type?.toLowerCase() === "percentage") {
        const mutiplier = this.basicPay / 100;
        const basicPay = Math.ceil((this.annualCTC * mutiplier) / 12);
        this.monthlyBasicPay = basicPay ? basicPay : 0;
      }
      let keysToUpdate = [
        "allowanceArray",
        "flexiBenefitPlanArray",
        "reimbursementArray",
        "bonusArray",
      ];
      keysToUpdate.forEach((key) => {
        this.templateDetails.allowances?.[key]?.forEach((innerItems) => {
          if (innerItems.Allowance_Type?.toLowerCase() === "percentage") {
            const mutiplier = innerItems.Percentage / 100;
            const basicPay = Math.ceil(this.monthlyBasicPay * mutiplier);
            innerItems.Amount = basicPay ? basicPay : 0;
          }
        });
      });
      if (msg !== "mounted") {
        this.calculateSumOfComponetsMinusCTC();
      }
    },
    updateAnnualCTC() {
      this.monthlyCTC = Math.ceil(this.annualCTC / 12);
      this.updateBasicPay();
    },
    async validateForm() {
      let { valid } = await this.$refs.form.validate();
      if (valid) {
        this.addUpdateFormData();
      } else {
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          const fields = Array.isArray(field) ? field : [field];
          if (fields && fields[0] && fields[0].rules) {
            const allValid = fields[0].rules.every((value) => value === true);
            if (fields[0].rules.length > 0 && !allValid) {
              invalidFields.push(refName);
            }
          }
        });

        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              const fields = Array.isArray(fieldRef) ? fieldRef : [fieldRef];
              if (fields && fields[0] && fields[0].$el) {
                const element = fields[0].$el;
                if (element && element.scrollIntoView) {
                  element.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                  });
                } else if (element && element.getBoundingClientRect) {
                  const rect = element.getBoundingClientRect();
                  window.scrollTo({
                    top: window.scrollY + rect.top - 100, // adjust offset if needed
                    behavior: "smooth",
                  });
                }
              }
            }
          });
        }
      }
    },
    addUpdateFormData() {
      let vm = this;
      vm.isLoading = true;
      let allowanceArray = [];
      let retiralsArray = [];
      if (this.templateDetails?.allowances?.basicPayArray?.length) {
        allowanceArray.push({
          allowanceId:
            this.templateDetails.allowances.basicPayArray[0].Allowance_Id,
          allowanceType:
            this.templateDetails.allowances.basicPayArray[0].Allowance_Type,
          percentage: this.basicPay ? this.basicPay?.toString() : null,
          amount: this.monthlyBasicPay
            ? this.monthlyBasicPay?.toString()
            : null,
          allowanceWages:
            this.templateDetails.allowances.basicPayArray[0].AllowanceWages?.toString(),
        });
      }
      if (this.templateDetails?.allowances?.fixedAllowanceArray?.length) {
        this.templateDetails.allowances.fixedAllowanceArray.forEach((item) => {
          allowanceArray.push({
            allowanceId: item.Allowance_Id,
            allowanceType: item.Allowance_Type,
            percentage: null,
            amount: this.fixedAllowanceAmount?.toString() || "",
          });
        });
      }
      let keysToUpdate = [
        "allowanceArray",
        "flexiBenefitPlanArray",
        "reimbursementArray",
        "bonusArray",
      ];
      if (this.templateDetails.allowances) {
        keysToUpdate.map((item) => {
          this.templateDetails.allowances[item]?.forEach((innerItems) => {
            allowanceArray.push({
              allowanceId: innerItems.Allowance_Id,
              allowanceType: innerItems.Allowance_Type,
              percentage: innerItems.Percentage?.toString(),
              amount: innerItems.Amount?.toString(),
              allowanceWages: innerItems.AllowanceWages?.toString(),
            });
          });
        });
        if (
          this.templateDetails.retirals &&
          this.templateDetails.retirals.length
        ) {
          this.templateDetails.retirals.forEach((item) => {
            if (this.checkEmployeeEligible(item.Retirals_Name)) {
              retiralsArray.push({
                formId: item.Form_Id?.toString(),
                retiralsId: item.Retirals_Id?.toString(),
                retiralsType: item.Retirals_Type?.toString(),
                employeeSharePercentage:
                  item.Employee_Share_Percentage?.toString(),
                employerSharePercentage:
                  item.Employer_Share_Percentage?.toString(),
                employeeShareAmount: item.Employee_Share_Amount?.toString(),
                employerShareAmount: item.Employer_Share_Amount?.toString(),
                pfEmployeeContribution:
                  item.PF_Employee_Contribution?.toString(),
                pfEmployerContribution:
                  item.PF_Employer_Contribution?.toString(),
                employeeStatutoryLimit:
                  item.Employee_Statutory_Limit?.toString(),
                employerStatutoryLimit:
                  item.Employer_Statutory_Limit?.toString(),
                employeeRetiralWages: item.Employee_Retiral_Wages?.toString(),
                employerRetiralWages: item.Employer_Retiral_Wages?.toString(),
                eligibleForEPS: item.Eligible_for_EPS?.toString() || 0,
                adminCharge: item.Admin_Charge?.toString(),
                edliCharge: item.EDLI_Charge?.toString(),
              });
            }
          });
        }
      }
      let variables = {};
      if (vm.formId === 207) {
        if (vm.editType?.toLowerCase() === "revise") {
          let percentage =
            ((vm.annualCTC - vm.templateDetails?.Annual_CTC) /
              vm.templateDetails?.Annual_CTC) *
            100;
          variables = {
            formId: 360,
            isEditMode: false,
            templateId: vm.selectedSalaryTemplate,
            employeeId: vm.selectedEmployee,
            annualCTC: vm.annualCTC.toString(),
            effectiveFrom: moment(
              vm.formattedEffectiveForm,
              this.$store.state.orgDetails.orgDateFormat
            ).isValid()
              ? moment(
                  vm.formattedEffectiveForm,
                  this.$store.state.orgDetails.orgDateFormat
                ).format("YYYY-MM-DD")
              : "",
            effectiveTo: "",
            allowance: allowanceArray || [],
            annualGrossSalary: (this.monthlyGrossSalary * 12).toString(),
            monthlyGrossSalary: this.monthlyGrossSalary.toString(),
            retirals: retiralsArray || [],
            salaryEffectiveMonth: moment(
              vm.revisedSalaryEffectiveFrom
            ).isValid()
              ? moment(vm.revisedSalaryEffectiveFrom).format("M,YYYY")
              : "",
            payoutMonth: moment(vm.payoutMonth).isValid()
              ? moment(vm.payoutMonth).format("M,YYYY")
              : "",
            revisionType: vm.reviseType,
            status: "Applied",
            reviseCtcByPercentage:
              vm.reviseType?.toLowerCase() === "percentage"
                ? vm.percentage
                : percentage?.toFixed(2).toString(),
            previousCtc: vm.templateDetails?.Annual_CTC?.toString(),
          };
        } else {
          variables = {
            formId: 207,
            isEditMode: vm.editType !== "",
            templateId: vm.selectedSalaryTemplate,
            employeeId: vm.selectedEmployee,
            annualCTC: vm.annualCTC.toString(),
            effectiveFrom: moment(
              vm.formattedEffectiveForm,
              this.$store.state.orgDetails.orgDateFormat
            ).isValid()
              ? moment(
                  vm.formattedEffectiveForm,
                  this.$store.state.orgDetails.orgDateFormat
                ).format("YYYY-MM-DD")
              : "",
            effectiveTo: "",
            allowance: allowanceArray || [],
            annualGrossSalary: (this.monthlyGrossSalary * 12).toString(),
            monthlyGrossSalary: this.monthlyGrossSalary.toString(),
            retirals: retiralsArray || [],
          };
        }
      }
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_SALARY_DETAILS,
          variables: variables,
          client: "apolloClientF",
        })
        .then(() => {
          vm.isLoading = false;
          let msg = "";
          if (vm.editType?.toLowerCase() === "revise") {
            msg = "Salary details revised successfully.";
          } else if (vm.editType?.toLowerCase() === "edit") {
            msg = "Salary details updated successfully.";
          } else {
            msg = "Salary details added successfully.";
          }
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: msg,
          };
          vm.showAlert(snackbarData);
          vm.$emit("add-update-success");
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "adding/updating",
            form: "salary details",
            isListError: false,
          });
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    getSystemCalculatedComponets() {
      let vm = this;
      vm.componentsLoading = true;
      let salaryDetails = {
        Employee_Salary_Id: vm.selectedItem?.Employee_Salary_Id,
        Employee_Id: vm.selectedEmployee,
        Annual_Ctc: vm.annualCTC,
        Basic_Pay: vm.monthlyBasicPay,
        Effective_From: moment(
          vm.formattedEffectiveForm,
          this.$store.state.orgDetails.orgDateFormat
        ).isValid()
          ? moment(
              vm.formattedEffectiveForm,
              this.$store.state.orgDetails.orgDateFormat
            ).format("YYYY-MM-DD")
          : "",
        Effective_To: "",
        ESI_Contribution_End_Date: null,
        Status: "Active",
      };
      let keysToSend = [
        "allowanceArray",
        "flexiBenefitPlanArray",
        "reimbursementArray",
        "bonusArray",
      ];
      let allowanceDetails = [];
      allowanceDetails.push({
        Employee_Salary_Id: vm.selectedEmployee,
        Allowance_Id:
          this.templateDetails?.allowances?.basicPayArray[0]?.Allowance_Id,
        Allowance_Type:
          this.templateDetails?.allowances?.basicPayArray[0]?.Allowance_Type,
        Percentage: vm.basicPay,
        Amount: parseInt(vm.monthlyBasicPay),
      });
      keysToSend.forEach((key) => {
        this.templateDetails?.allowances?.[key].forEach((item) => {
          allowanceDetails.push({
            Employee_Salary_Id: vm.selectedEmployee,
            Allowance_Id: item?.Allowance_Id,
            Allowance_Type: item?.Allowance_Type,
            Percentage: item?.Percentage,
            Amount: parseInt(item?.Amount),
          });
        });
      });

      let retiralDetails = [];
      this.templateDetails?.retirals.forEach((item) => {
        if (this.checkEmployeeEligible(item.Retirals_Name)) {
          retiralDetails.push({
            Employee_Salary_Id: vm.selectedEmployee,
            Form_Id: item.Form_Id,
            Retirals_Id: item.Retirals_Id,
            Retirals_Type: item.Retirals_Type,
            Retiral_Wages: item.Retiral_Wages,
            Employee_Share_Percentage: item.Employee_Share_Percentage,
            Employer_Share_Percentage: item.Employer_Share_Percentage,
            Employee_Share_Amount: parseInt(item.Employee_Share_Amount),
            Employer_Share_Amount: parseInt(item.Employer_Share_Amount),
            PF_Employee_Contribution: item.PF_Employee_Contribution,
            PF_Employer_Contribution: item.PF_Employer_Contribution,
            Employee_Statutory_Limit: item.Employee_Statutory_Limit,
            Employer_Statutory_Limit: item.Employer_Statutory_Limit,
            Eligible_for_EPS: item.Eligible_for_EPS,
            Admin_Charge: item.Admin_Charge,
            EDLI_Charge: item.EDLI_Charge,
          });
        }
      }) || [];
      vm.$apollo
        .query({
          query: RETRIEVE_SYSTEM_CALCULATED_COMPONENTS,
          client: "apolloClientAT",
          variables: {
            employeeId: vm.selectedEmployee,
            retiralDetails: JSON.stringify(retiralDetails),
            allowanceDetails: JSON.stringify(allowanceDetails),
            salaryDetails: JSON.stringify(salaryDetails),
          },
          fetchPolicy: "no-cache",
        })
        .then(({ data }) => {
          if (
            data?.calculateSalary?.employeeRetiralDetails &&
            !data.calculateSalary.errorCode
          ) {
            let salaryComponents = JSON.parse(
              data?.calculateSalary?.employeeRetiralDetails
            );
            if (salaryComponents?.employeeSalaryRetirals?.length) {
              const matchedKeys = new Set(
                salaryComponents.employeeSalaryRetirals.map(
                  (item) => `${item.Retirals_Id}|${item.Form_Id}`
                )
              );
              this.templateDetails.retirals =
                this.templateDetails.retirals.filter((retiralItem) => {
                  const key = `${retiralItem.Retirals_Id}|${retiralItem.Form_Id}`;
                  const match = matchedKeys.has(key);

                  if (match) {
                    const matchedItem =
                      salaryComponents.employeeSalaryRetirals.find(
                        (item) =>
                          parseInt(item.Form_Id) ===
                            parseInt(retiralItem.Form_Id) &&
                          parseInt(item.Retirals_Id) ===
                            parseInt(retiralItem.Retirals_Id)
                      );

                    if (matchedItem) {
                      retiralItem.Employee_Share_Amount =
                        matchedItem.Employee_Share_Amount;
                      retiralItem.Employer_Share_Amount =
                        matchedItem.Employer_Share_Amount;
                      retiralItem.Amount = matchedItem.Employer_Share_Amount;
                    }
                  }

                  return match;
                });
            } else {
              this.templateDetails.retirals = [];
            }

            if (salaryComponents?.employeeSalaryBonus?.length) {
              salaryComponents.employeeSalaryBonus.forEach((item) => {
                this.templateDetails.allowances.bonusArray.forEach(
                  (bonusItem) => {
                    if (
                      parseInt(item.Allowance_Id) ===
                      parseInt(bonusItem.Allowance_Id)
                    ) {
                      bonusItem.Amount = item.Amount;
                      bonusItem.Percentage = item.Percentage;
                    }
                  }
                );
              });
            }
            this.calculateFixedAllowanceAmount();
            vm.anyValueUpdated = false;
          } else {
            let snackbarData = {
              isOpen: true,
              type: "warning",
              message:
                data?.calculateSalary?.message ||
                "Something went wrong. Please try after some time.",
            };
            vm.showAlert(snackbarData);
          }
          vm.componentsLoading = false;
        })
        .catch((err) => {
          vm.componentsLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "system calculated components",
            isListError: false,
          });
        });
    },
  },
};
</script>
<style scoped>
.add-update-content {
  max-height: calc(100vh - 130px) !important;
  overflow-y: scroll;
}

:deep(.v-field--prepended) {
  padding-inline-start: 0;
}

:deep(.v-field--appended) {
  padding-inline-end: 0;
}

.custom-info-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 15px;
  height: 15px;
  border: 1.5px solid #9e9e9e;
  border-radius: 50%;
}
:deep(.custom-input-position .v-field__input) {
  text-align: end !important;
}
</style>
