<template>
  <div class="pb-10">
    <div v-if="isMounted">
      <VueTelInput
        v-show="false"
        :autoDefaultCountry="true"
        @country-changed="getCountryCode($event)"
      ></VueTelInput>
      <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
        >Personal Details</span
      >
      <v-form ref="editPersonalDetailsForm" class="pa-2">
        <!-- <v-row v-if="selectedEmpStatus === 'InActive'">
          <v-col cols="12" md="4" sm="6">
            <v-text-field
              v-model.trim="editedPersonalDetails.Personal_Email"
              label="Personal Email"
              :rules="
                checkFieldAvailability(editedPersonalDetails.Personal_Email)
                  ? [
                      validateWithRulesAndReturnMessages(
                        editedPersonalDetails.Personal_Email,
                        'personalEmail',
                        'Personal Email'
                      ),
                    ]
                  : [true]
              "
              variant="solo"
              ref="personalEmail"
              @update:model-value="onChangeFields()"
            ></v-text-field>
          </v-col>
        </v-row> -->
        <div>
          <v-row>
            <v-col
              v-if="
                actionType === 'add' &&
                callingFrom !== 'profile' &&
                fieldForce &&
                idGenerationconfig &&
                idGenerationCoverage?.toLowerCase() === 'serviceprovider'
              "
              cols="12"
              md="4"
              sm="6"
            >
              <CustomSelect
                :items="serviceProviders"
                :label="getCustomFieldName(115, 'Service Provider')"
                :isRequired="true"
                :rules="[
                  required(
                    getCustomFieldName(115, 'Service Provider'),
                    editedPersonalDetails.Service_Provider_Id
                  ),
                ]"
                :isLoading="dropdownListFetching"
                :itemSelected="editedPersonalDetails.Service_Provider_Id"
                itemValue="Service_Provider_Id"
                itemTitle="Service_Provider_Name"
                @selected-item="
                  onChangeCustomSelectField($event, 'Service_Provider_Id')
                "
              ></CustomSelect>
            </v-col>
            <v-col v-if="callingFrom !== 'profile'" cols="12" md="4" sm="6">
              <v-text-field
                v-model.trim="editedPersonalDetails.User_Defined_EmpId"
                :rules="[
                  alreadyExistErrMsg['User_Defined_EmpId'],
                  required(
                    'Employee Id',
                    editedPersonalDetails.User_Defined_EmpId
                  ),
                  validateWithRulesAndReturnMessages(
                    editedPersonalDetails.User_Defined_EmpId,
                    'userDefinedEmpId',
                    'Employee Id'
                  ),
                ]"
                variant="solo"
                :loading="fetchingMaxEmpId"
                :disabled="!isActive || idGenerationconfig"
                ref="employeeId"
                @update:model-value="onChangeFields('User_Defined_EmpId')"
                @change="
                  validateFieldAlreadyExist('User_Defined_EmpId', 'Employee Id')
                "
              >
                <template v-slot:label>
                  Employee Id<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col v-if="callingFrom !== 'profile'" cols="12" md="4" sm="6">
              <v-text-field
                v-model.trim="editedPersonalDetails.External_EmpId"
                ref="externalEmpId"
                label="Biometric Integration Id"
                :rules="
                  checkFieldAvailability(editedPersonalDetails.External_EmpId)
                    ? [
                        alreadyExistErrMsg['External_EmpId'],
                        validateWithRulesAndReturnMessages(
                          editedPersonalDetails.External_EmpId,
                          'biometricIntegraionId',
                          'Biometric Integration Id'
                        ),
                      ]
                    : [true]
                "
                variant="solo"
                @update:model-value="onChangeFields('External_EmpId')"
                @change="
                  validateFieldAlreadyExist(
                    'External_EmpId',
                    'Biometric Integration Id'
                  )
                "
                :disabled="!isActive"
              >
              </v-text-field>
              <div
                v-if="isValidExternalEmpId"
                class="text-caption mt-n4 ml-2"
                style="color: #f3012d; line-height: 1.2"
              >
                Please enter the biometric integration id for biometric data
                processing.
              </div>
            </v-col>

            <v-col cols="12" md="4" sm="6">
              <CustomSelect
                :items="['Mr', 'Miss', 'Mrs', 'Dr', 'Prof', 'Ms']"
                label="Salutation"
                :isRequired="true"
                :disabled="!isActive"
                :itemSelected="editedPersonalDetails.Salutation"
                :rules="[
                  required('Salutation', editedPersonalDetails.Salutation),
                ]"
                @selected-item="onChangeCustomSelectField($event, 'Salutation')"
                ref="salutation"
              ></CustomSelect>
            </v-col>

            <v-col v-if="callingFrom !== 'profile'" cols="12" md="4" sm="6">
              <v-text-field
                v-model.trim="editedPersonalDetails.Emp_First_Name"
                :rules="[
                  required('First Name', editedPersonalDetails.Emp_First_Name),
                  validateWithRulesAndReturnMessages(
                    editedPersonalDetails.Emp_First_Name,
                    'empFirstName',
                    'First Name'
                  ),
                ]"
                variant="solo"
                :disabled="!isActive"
                ref="firstName"
                @update:model-value="onChangeFields()"
              >
                <template v-slot:label>
                  First Name<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>

            <v-col v-if="callingFrom !== 'profile'" cols="12" md="4" sm="6">
              <v-text-field
                v-model.trim="editedPersonalDetails.Emp_Middle_Name"
                label="Middle Name"
                :rules="
                  checkFieldAvailability(editedPersonalDetails.Emp_Middle_Name)
                    ? [
                        validateWithRulesAndReturnMessages(
                          editedPersonalDetails.Emp_Middle_Name,
                          'empMiddleName',
                          'Middle Name'
                        ),
                      ]
                    : [true]
                "
                :disabled="!isActive"
                ref="middleName"
                variant="solo"
                @update:model-value="onChangeFields()"
              ></v-text-field>
            </v-col>
            <v-col v-if="callingFrom !== 'profile'" cols="12" md="4" sm="6">
              <v-text-field
                v-model.trim="editedPersonalDetails.Emp_Last_Name"
                :rules="[
                  required('Last Name', editedPersonalDetails.Emp_Last_Name),
                  validateWithRulesAndReturnMessages(
                    editedPersonalDetails.Emp_Last_Name,
                    'empLastName',
                    'Last Name'
                  ),
                ]"
                variant="solo"
                :disabled="!isActive"
                ref="lastName"
                @update:model-value="onChangeFields()"
              >
                <template v-slot:label>
                  Last Name<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-text-field
                label="Suffix"
                variant="solo"
                v-model="editedPersonalDetails.Suffix"
                @update:model-value="onChangeFields()"
                :disabled="!isActive"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <CustomSelect
                :items="genderList"
                label="Gender"
                :isRequired="true"
                :rules="
                  editedPersonalDetails.Gender_Id == 0
                    ? [true]
                    : [required('Gender', editedPersonalDetails.Gender_Id)]
                "
                :itemSelected="editedPersonalDetails.Gender_Id"
                ref="gender"
                itemValue="genderId"
                itemTitle="gender"
                :disabled="!isActive"
                :isLoading="genderListLoading"
                :noDataText="
                  genderListLoading ? 'Loading...' : 'No data available'
                "
                @selected-item="onChangeCustomSelectField($event, 'Gender_Id')"
              ></CustomSelect>
            </v-col>
            <!-- Gender Expression -->
            <v-col
              cols="12"
              md="4"
              sm="6"
              v-if="labelList[348]?.Field_Visiblity?.toLowerCase() === 'yes'"
            >
              <CustomSelect
                :items="genderExpressionList"
                :label="labelList[348]?.Field_Alias"
                v-model="editedPersonalDetails.Gender_Expression_Id"
                :isRequired="
                  labelList[348]?.Mandatory_Field?.toLowerCase() === 'yes'
                    ? true
                    : false
                "
                :rules="
                  labelList['348'].Mandatory_Field?.toLowerCase() == 'yes'
                    ? [
                        required(
                          labelList['348'].Field_Alias,
                          editedPersonalDetails.Gender_Expression_Id
                        ),
                      ]
                    : [true]
                "
                clearable
                :itemSelected="editedPersonalDetails.Gender_Expression_Id"
                ref="genderExpressionId"
                itemValue="Gender_Expression_Id"
                itemTitle="Gender_Expression"
                :isLoading="dropdownLoading"
                @selected-item="
                  onChangeCustomSelectField($event, 'Gender_Expression_Id')
                "
              />
            </v-col>
            <!-- Gender Identity -->
            <v-col
              cols="12"
              md="4"
              sm="6"
              v-if="labelList[347]?.Field_Visiblity?.toLowerCase() === 'yes'"
            >
              <CustomSelect
                :items="genderIdentityList"
                :label="labelList[347]?.Field_Alias"
                v-model="editedPersonalDetails.Gender_Identity_Id"
                :isRequired="
                  labelList[347]?.Mandatory_Field?.toLowerCase() === 'yes'
                    ? true
                    : false
                "
                :rules="
                  labelList['347'].Mandatory_Field?.toLowerCase() == 'yes'
                    ? [
                        required(
                          labelList['347'].Field_Alias,
                          editedPersonalDetails.Gender_Identity_Id
                        ),
                      ]
                    : [true]
                "
                clearable
                :itemSelected="editedPersonalDetails.Gender_Identity_Id"
                ref="genderIdentityId"
                itemValue="Gender_Identity_Id"
                itemTitle="Gender_Identity"
                :isLoading="dropdownLoading"
                @selected-item="
                  onChangeCustomSelectField($event, 'Gender_Identity_Id')
                "
              />
            </v-col>
            <v-col
              v-if="labelList[207].Field_Visiblity?.toLowerCase() == 'yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <CustomSelect
                :items="pronounList"
                item-title="Gender_Pronoun_Name"
                item-value="Gender_Pronoun_Name"
                :isLoading="dropdownLoading"
                :label="labelList[207].Field_Alias"
                :itemSelected="editedPersonalDetails.Pronoun"
                ref="pronoun"
                :disabled="!isActive"
                :rules="[
                  labelList[207].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        labelList[207].Field_Alias,
                        editedPersonalDetails.Pronoun
                      )
                    : true,
                ]"
                clearable
                :isRequired="
                  labelList[208].Mandatory_Field?.toLowerCase() == 'yes'
                "
                @selected-item="onChangeCustomSelectField($event, 'Pronoun')"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[207].Field_Visiblity?.toLowerCase() == 'yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <CustomSelect
                :items="genderOrientationList"
                item-title="Gender_Orientations_Name"
                item-value="Gender_Orientations_Name"
                :isLoading="dropdownLoading"
                :label="labelList[208].Field_Alias"
                :itemSelected="editedPersonalDetails.Gender_Orientations"
                ref="genderOrientation"
                :disabled="!isActive"
                :rules="[
                  labelList[208].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        labelList[208].Field_Alias,
                        editedPersonalDetails.Gender_Orientations
                      )
                    : true,
                ]"
                :isRequired="
                  labelList[208].Mandatory_Field?.toLowerCase() == 'yes'
                "
                @selected-item="
                  onChangeCustomSelectField($event, 'Gender_Orientations')
                "
              ></CustomSelect>
            </v-col>
            <!-- Marital Status -->
            <v-col cols="12" md="4" sm="6">
              <CustomSelect
                :items="maritalStatusList"
                :label="labelList[399]?.Field_Alias"
                itemValue="Marital_Status_Id"
                itemTitle="Marital_Status"
                :isLoading="maritalStatusListLoading"
                :disabled="!isActive"
                :isRequired="true"
                :rules="[
                  required(
                    labelList[399]?.Field_Alias,
                    editedPersonalDetails.Marital_Status !== null &&
                      editedPersonalDetails.Marital_Status !== undefined
                      ? editedPersonalDetails.Marital_Status + 1
                      : 0
                  ),
                ]"
                clearable
                ref="maritalStatus"
                :itemSelected="editedPersonalDetails.Marital_Status"
                @selected-item="
                  onChangeCustomSelectField($event, 'Marital_Status')
                "
              ></CustomSelect>
            </v-col>
            <v-col v-if="callingFrom !== 'profile'" cols="12" md="4" sm="6">
              <section class="text-body-2">
                <v-menu
                  v-model="dobMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                  ><template v-slot:activator="{ props }">
                    <v-text-field
                      ref="dob"
                      v-model="formattedDateOfBirth"
                      prepend-inner-icon="fas fa-calendar"
                      :rules="[required('Date of Birth', formattedDateOfBirth)]"
                      readonly
                      v-bind="props"
                      variant="solo"
                      :disabled="!isActive"
                    >
                      <template v-slot:label>
                        Date of Birth
                        <span style="color: red">*</span>
                      </template></v-text-field
                    >
                  </template>
                  <v-date-picker
                    v-if="editedPersonalDetails.DOB"
                    v-model="editedPersonalDetails.DOB"
                    :min="minimumBirthDate"
                    :max="maximumBirthDate"
                    @update:model-value="onChangeFields()"
                  />
                  <v-date-picker
                    v-else
                    v-model="defaultDOB"
                    @update:modelValue="onChangeDefaultDOB(defaultDOB)"
                    :max="maximumBirthDate"
                    :min="minimumBirthDate"
                  />
                </v-menu>
              </section>
            </v-col>
            <!-- Blood Group -->
            <v-col
              v-if="labelList[401]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <CustomSelect
                :items="[
                  'A+',
                  'A-',
                  'B+',
                  'B-',
                  'AB+',
                  'AB-',
                  'O+',
                  'O-',
                  'A1B+',
                  'Unknown',
                ]"
                :label="labelList[401]?.Field_Alias"
                :isRequired="
                  labelList[401]?.Mandatory_Field.toLowerCase() == 'yes'
                "
                clearable
                :rules="[
                  labelList[401].Mandatory_Field.toLowerCase() == 'yes'
                    ? required(
                        `${labelList[401].Field_Alias}`,
                        editedPersonalDetails.Blood_Group
                      )
                    : true,
                ]"
                ref="bloodGroup"
                :itemSelected="editedPersonalDetails.Blood_Group"
                @selected-item="
                  onChangeCustomSelectField($event, 'Blood_Group')
                "
                :disabled="!isActive"
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <CustomSelect
                :items="nationalityList"
                label="Nationality"
                :isRequired="true"
                itemTitle="nationality"
                itemValue="nationalityId"
                :isAutoComplete="true"
                :isLoading="nationalityListLoading"
                :itemSelected="editedPersonalDetails.Nationality_Id"
                :disabled="!isActive"
                :rules="[
                  required('Nationality', editedPersonalDetails.Nationality_Id),
                ]"
                @selected-item="
                  onChangeCustomSelectField($event, 'Nationality_Id')
                "
                ref="nationalityDropdown"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="
                !editedPersonalDetails.Nationality_Id ||
                editedPersonalDetails.Nationality_Id == 11
              "
              cols="12"
              md="4"
              sm="6"
            >
              <v-text-field
                v-model.trim="editedPersonalDetails.Nationality"
                :rules="[
                  required(
                    'Other Nationality',
                    editedPersonalDetails.Nationality
                  ),
                  validateWithRulesAndReturnMessages(
                    editedPersonalDetails.Nationality,
                    'nationality',
                    'Other Nationality'
                  ),
                ]"
                variant="solo"
                ref="nationality"
                @update:model-value="onChangeFields()"
                :disabled="!isActive"
              >
                <template v-slot:label>
                  Other Nationality<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col
              v-if="
                labelList['212'] &&
                labelList['212'].Field_Visiblity?.toLowerCase() == 'yes'
              "
              cols="12"
              md="4"
              sm="6"
            >
              <v-text-field
                v-model.trim="editedPersonalDetails.Aadhaar_Card_Number"
                :label="labelList['212'].Field_Alias"
                :rules="[
                  labelList['212'].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        `${labelList['212'].Field_Alias}`,
                        editedPersonalDetails.Aadhaar_Card_Number
                      )
                    : true,
                  alreadyExistErrMsg['Aadhaar_Card_Number'],
                  editedPersonalDetails.Aadhaar_Card_Number
                    ? validateWithRulesAndReturnMessages(
                        editedPersonalDetails.Aadhaar_Card_Number,
                        'aadhar',
                        `${labelList['212'].Field_Alias}`
                      )
                    : true,
                ]"
                ref="nationalIdentityNumber"
                @update:model-value="onChangeFields('Aadhaar_Card_Number')"
                @change="
                  validateFieldAlreadyExist(
                    'Aadhaar_Card_Number',
                    `${labelList['212'].Field_Alias}`,
                    'emp_personal_info'
                  )
                "
                variant="solo"
                ><template v-slot:label>
                  <span>{{ labelList[212].Field_Alias }}</span>
                  <span
                    v-if="
                      labelList[212].Mandatory_Field?.toLowerCase() == 'yes'
                    "
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </v-col>
            <v-col
              v-if="
                labelList['213'] &&
                labelList['213'].Field_Visiblity?.toLowerCase() == 'yes'
              "
              cols="12"
              md="4"
              sm="6"
            >
              <v-text-field
                v-model.trim="editedPersonalDetails.PAN"
                :label="labelList['213'].Field_Alias"
                :rules="[
                  labelList['213'].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        `${labelList['213'].Field_Alias}`,
                        editedPersonalDetails.PAN
                      )
                    : true,
                  alreadyExistErrMsg['PAN'],

                  alphaNumSpaceWithElevenSymbolValidation(
                    editedPersonalDetails.PAN
                  ),
                  maxLengthValidation(
                    `${labelList['213'].Field_Alias}`,
                    editedPersonalDetails.PAN,
                    30
                  ),
                ]"
                variant="solo"
                ref="taxIdentificationNumber"
                @update:model-value="onChangeFields('PAN')"
                @change="
                  validateFieldAlreadyExist(
                    'PAN',
                    `${labelList['213'].Field_Alias}`,
                    'emp_personal_info'
                  )
                "
                ><template v-slot:label>
                  <span>{{ labelList[213].Field_Alias }}</span>
                  <span
                    v-if="
                      labelList[213].Mandatory_Field?.toLowerCase() == 'yes'
                    "
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </v-col>
            <v-col
              v-if="
                labelList['188'] &&
                labelList['188'].Field_Visiblity?.toLowerCase() == 'yes'
              "
              cols="12"
              md="4"
              sm="6"
            >
              <CustomSelect
                v-if="labelList['188'].Predefined?.toLowerCase() == 'yes'"
                :items="taxCodeList"
                :label="labelList['188'].Field_Alias"
                :isRequired="
                  labelList['188'].Mandatory_Field?.toLowerCase() == 'yes'
                "
                itemTitle="Tax_Description"
                itemValue="Tax_Code"
                :isAutoComplete="true"
                :disabled="!isActive"
                :isLoading="taxCodeListLoading"
                :itemSelected="editedPersonalDetails.Tax_Code"
                :rules="[
                  labelList['188'].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        labelList['188'].Field_Alias,
                        editedPersonalDetails.Tax_Code
                      )
                    : true,
                ]"
                @selected-item="onChangeCustomSelectField($event, 'Tax_Code')"
                ref="taxCodeDropdown"
              ></CustomSelect>
              <v-text-field
                v-else
                v-model.trim="editedPersonalDetails.Tax_Code"
                :label="labelList['188'].Field_Alias"
                :rules="[
                  labelList['188'].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        `${labelList['188'].Field_Alias}`,
                        editedPersonalDetails.Tax_Code
                      )
                    : true,
                ]"
                variant="solo"
                ref="taxCode"
                @update:model-value="onChangeFields('Tax_Code')"
                :disabled="!isActive"
                ><template v-slot:label>
                  <span>{{ labelList[188].Field_Alias }}</span>
                  <span
                    v-if="
                      labelList[188].Mandatory_Field?.toLowerCase() == 'yes'
                    "
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-text-field
                v-model.trim="editedPersonalDetails.Personal_Email"
                label="Personal Email"
                :rules="
                  checkFieldAvailability(editedPersonalDetails.Personal_Email)
                    ? [
                        validateWithRulesAndReturnMessages(
                          editedPersonalDetails.Personal_Email,
                          'personalEmail',
                          'Personal Email'
                        ),
                      ]
                    : [true]
                "
                variant="solo"
                ref="personalEmail"
                @update:model-value="onChangeFields()"
              ></v-text-field>
            </v-col>
            <v-col
              v-if="
                labelList['214'] &&
                labelList['214'].Field_Visiblity?.toLowerCase() == 'yes'
              "
              cols="12"
              md="4"
              sm="6"
            >
              <v-text-field
                v-model.trim="editedPersonalDetails.UAN"
                :label="labelList['214'].Field_Alias"
                :rules="[
                  labelList['214'].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        `${labelList['214'].Field_Alias}`,
                        editedPersonalDetails.UAN
                      )
                    : true,
                  alreadyExistErrMsg['UAN'],
                  editedPersonalDetails.UAN
                    ? validateWithRulesAndReturnMessages(
                        editedPersonalDetails.UAN,
                        'uan',
                        `${labelList['214'].Field_Alias}`
                      )
                    : true,
                ]"
                variant="solo"
                ref="uan"
                @update:model-value="onChangeFields('UAN')"
                @change="
                  validateFieldAlreadyExist(
                    'UAN',
                    `${labelList['214'].Field_Alias}`,
                    'emp_personal_info'
                  )
                "
                ><template v-slot:label>
                  <span>{{ labelList[214].Field_Alias }}</span>
                  <span
                    v-if="
                      labelList[214].Mandatory_Field?.toLowerCase() == 'yes'
                    "
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </v-col>
            <v-col v-if="callingFrom !== 'profile'" cols="12">
              <div class="d-flex mb-n6">
                <span class="v-label pr-3 pb-5">Manager</span>
                <v-switch
                  color="primary"
                  v-model="editedPersonalDetails.Is_Manager"
                  :true-value="1"
                  :false-value="0"
                  @update:model-value="onChangeFields()"
                  :disabled="!isActive"
                ></v-switch>
              </div>
            </v-col>
            <v-col v-if="callingFrom !== 'profile'" cols="12">
              <div class="d-flex mb-n6">
                <span class="v-label pr-3 pb-5">Recruiter</span>
                <v-switch
                  color="primary"
                  v-model="editedPersonalDetails.Is_Recruiter"
                  :true-value="1"
                  :false-value="0"
                  @update:model-value="onChangeFields()"
                  :disabled="!isActive"
                ></v-switch>
              </div>
            </v-col>
          </v-row>
          <v-row v-if="callingFrom !== 'profile'">
            <v-col cols="12">
              <div class="d-flex">
                <span class="v-label pr-3 pb-5">Allow user sign in</span>
                <v-switch
                  color="primary"
                  v-model="editedPersonalDetails.Allow_User_Signin"
                  :true-value="1"
                  :false-value="0"
                  @update:model-value="onChangeFields()"
                  :disabled="!isActive"
                ></v-switch>
              </div>
            </v-col>

            <v-col
              v-if="
                editedPersonalDetails.Allow_User_Signin &&
                !entomoIntegrationEnabled
              "
              cols="12"
              md="4"
              sm="6"
            >
              <v-btn-toggle
                v-model="editedPersonalDetails.Enable_Sign_In_With_Mobile_No"
                rounded="lg"
                mandatory
                elevation="2"
                @update:model-value="onChangeFields(val)"
                :disabled="!isActive"
              >
                <v-btn
                  color="primary"
                  style="background-color: white; color: black"
                  >Email Address</v-btn
                >
                <v-btn
                  color="primary"
                  style="background-color: white; color: black"
                  >Mobile Number</v-btn
                ></v-btn-toggle
              >
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12" md="4" sm="6" v-if="isActive">
              <div class="d-flex">
                <span class="v-label pr-3 pb-5 mb-2">More Details</span>
                <v-switch
                  color="primary"
                  v-model="showMoreDetails"
                  class="ml-9"
                  @update:model-value="onChangeFields()"
                ></v-switch>
              </div>
            </v-col>
          </v-row>
          <div v-if="showMoreDetails">
            <v-row>
              <v-col cols="12" md="4" sm="6">
                <v-text-field
                  v-model.trim="editedPersonalDetails.Place_Of_Birth"
                  label="Place of Birth"
                  :rules="[
                    checkFieldAvailability(editedPersonalDetails.Place_Of_Birth)
                      ? validateWithRulesAndReturnMessages(
                          editedPersonalDetails.Place_Of_Birth,
                          'placeOfBirth',
                          labelList['295'].Field_Alias
                        )
                      : true,
                    labelList['295'].Mandatory_Field?.toLowerCase() == 'yes'
                      ? required(
                          labelList['295'].Field_Alias,
                          editedPersonalDetails.Place_Of_Birth
                        )
                      : true,
                  ]"
                  :disabled="!isActive"
                  variant="solo"
                  ref="placeOfBirth"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList["295"].Field_Alias }}
                    <span
                      v-if="
                        labelList['295'].Mandatory_Field?.toLowerCase() == 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <v-text-field
                  v-model.trim="editedPersonalDetails.Emp_Pref_First_Name"
                  :label="labelList[232].Field_Alias"
                  :rules="
                    checkFieldAvailability(
                      editedPersonalDetails.Emp_Pref_First_Name
                    )
                      ? [
                          validateWithRulesAndReturnMessages(
                            editedPersonalDetails.Emp_Pref_First_Name,
                            'knownAs',
                            labelList[232].Field_Alias
                          ),
                        ]
                      : [true]
                  "
                  :disabled="!isActive"
                  variant="solo"
                  ref="knownAs"
                  @update:model-value="onChangeFields()"
                ></v-text-field>
              </v-col>
              <v-col
                v-if="labelList[380]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <CustomSelect
                  :items="languageList"
                  :label="labelList[380]?.Field_Alias"
                  :isAutoComplete="true"
                  :isLoading="languageListLoading"
                  :noDataText="
                    languageListLoading ? 'Loading...' : 'No data available'
                  "
                  :itemSelected="editedPersonalDetails.Lang_Known"
                  :isRequired="
                    labelList[380]?.Mandatory_Field?.toLowerCase() == 'yes'
                  "
                  :rules="[
                    labelList[380]?.Mandatory_Field?.toLowerCase() === 'tes'
                      ? required(
                          'Languages Known',
                          editedPersonalDetails.Lang_Known?.length
                            ? editedPersonalDetails.Lang_Known
                            : null
                        )
                      : true,
                  ]"
                  itemValue="Lang_Id"
                  itemTitle="Language_Name"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Lang_Known')
                  "
                  ref="languageKnown"
                  :selectProperties="{
                    multiple: true,
                    chips: true,
                    clearable: true,
                    closableChips: true,
                  }"
                  :disabled="!isActive"
                ></CustomSelect>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <v-combobox
                  v-model="hobbiesCombo"
                  label="Hobbies"
                  multiple
                  chips
                  closable-chips
                  :rules="[
                    hobbiesCombo.length > 0
                      ? validateWithRulesAndReturnMessages(
                          hobbies,
                          'hobbies',
                          'Hobbies'
                        )
                      : true,
                  ]"
                  ref="hobbies"
                  variant="solo"
                  @update:modelValue="onChangeFields"
                  :disabled="!isActive"
                ></v-combobox>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[296].Field_Visiblity?.toLowerCase() == 'yes'"
              >
                <CustomSelect
                  :items="religionList"
                  :label="labelList[296].Field_Alias"
                  itemTitle="religion"
                  itemValue="religionId"
                  :isAutoComplete="true"
                  :isRequired="
                    labelList[296].Mandatory_Field?.toLowerCase() == 'yes'
                      ? true
                      : false
                  "
                  :isLoading="religionListLoading"
                  :itemSelected="editedPersonalDetails.Religion_Id"
                  :disabled="!isActive"
                  :rules="[
                    labelList[296].Mandatory_Field?.toLowerCase() == 'yes'
                      ? required(
                          labelList[296].Field_Alias,
                          editedPersonalDetails.Religion_Id
                        )
                      : true,
                  ]"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Religion_Id')
                  "
                  ref="religionDropdown"
                >
                </CustomSelect>
              </v-col>
              <v-col
                v-if="
                  !editedPersonalDetails.Religion_Id ||
                  editedPersonalDetails.Religion_Id == 67
                "
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  v-model.trim="editedPersonalDetails.Religion"
                  label="Other Religion"
                  :rules="
                    checkFieldAvailability(editedPersonalDetails.Religion)
                      ? [
                          validateWithRulesAndReturnMessages(
                            editedPersonalDetails.Religion,
                            'religion',
                            'Religion'
                          ),
                        ]
                      : [true]
                  "
                  variant="solo"
                  ref="religion"
                  :disabled="!isActive"
                  @update:model-value="onChangeFields()"
                ></v-text-field>
              </v-col>
              <v-col
                v-if="labelList[242]?.Field_Visiblity.toLowerCase() === 'yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  v-model.trim="editedPersonalDetails.Caste"
                  variant="solo"
                  :rules="[
                    labelList[242].Mandatory_Field?.toLowerCase() == 'yes'
                      ? required(
                          labelList[242].Field_Alias,
                          editedPersonalDetails.Caste
                        )
                      : true,
                    checkFieldAvailability(editedPersonalDetails.Caste)
                      ? validateWithRulesAndReturnMessages(
                          editedPersonalDetails.Caste,
                          'caste',
                          'Caste'
                        )
                      : true,
                  ]"
                  ref="caste"
                  @update:model-value="onChangeFields()"
                  :disabled="!isActive"
                >
                  <template v-slot:label>
                    {{ labelList[242].Field_Alias }}
                    <span
                      v-if="
                        labelList[242].Mandatory_Field?.toLowerCase() == 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template></v-text-field
                >
              </v-col>

              <v-col
                v-if="labelList[276]?.Field_Visiblity.toLowerCase() === 'yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  v-model.trim="editedPersonalDetails.Ethnic_Race"
                  variant="solo"
                  :rules="[
                    labelList[276].Mandatory_Field?.toLowerCase() == 'yes'
                      ? required(
                          labelList[276].Field_Alias,
                          editedPersonalDetails.Ethnic_Race
                        )
                      : true,
                    checkFieldAvailability(editedPersonalDetails.Ethnic_Race)
                      ? validateWithRulesAndReturnMessages(
                          editedPersonalDetails.Ethnic_Race,
                          'ethnicRace',
                          'Ethnic Race'
                        )
                      : true,
                  ]"
                  :disabled="!isActive"
                  ref="ethnicRace"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[276].Field_Alias }}
                    <span
                      v-if="
                        labelList[276].Mandatory_Field?.toLowerCase() == 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template></v-text-field
                >
              </v-col>
              <v-col
                v-if="
                  labelList['223'] &&
                  labelList['223'].Field_Visiblity?.toLowerCase() == 'yes'
                "
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  v-model.trim="
                    editedPersonalDetails.Statutory_Insurance_Number
                  "
                  :label="labelList['223'].Field_Alias"
                  variant="solo"
                  :rules="[
                    labelList['223'].Mandatory_Field?.toLowerCase() == 'yes'
                      ? required(
                          `${labelList['223'].Field_Alias}`,
                          editedPersonalDetails.Statutory_Insurance_Number
                        )
                      : true,
                  ]"
                  ref="statutoryInsuranceNumber"
                  @update:model-value="onChangeFields()"
                  ><template v-slot:label>
                    <span>{{ labelList[223].Field_Alias }}</span>
                    <span
                      v-if="
                        labelList[223].Mandatory_Field?.toLowerCase() == 'yes'
                      "
                      class="ml-1"
                      style="color: red"
                      >*</span
                    >
                  </template></v-text-field
                >
              </v-col>
              <v-col
                v-if="
                  labelList['224'] &&
                  labelList['224'].Field_Visiblity?.toLowerCase() == 'yes'
                "
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  v-model.trim="editedPersonalDetails.PRAN_No"
                  :label="labelList['224'].Field_Alias"
                  variant="solo"
                  :rules="[
                    labelList['224'].Mandatory_Field?.toLowerCase() == 'yes'
                      ? required(
                          `${labelList['224'].Field_Alias}`,
                          editedPersonalDetails.PRAN_No
                        )
                      : true,
                  ]"
                  ref="pranNo"
                  @update:model-value="onChangeFields()"
                  ><template v-slot:label>
                    <span>{{ labelList[224].Field_Alias }}</span>
                    <span
                      v-if="
                        labelList[224].Mandatory_Field?.toLowerCase() == 'yes'
                      "
                      class="ml-1"
                      style="color: red"
                      >*</span
                    >
                  </template></v-text-field
                >
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <div>
                  <div class="text-grey-darken-1" style="font-size: 16px">
                    Have you ever served in the military?
                  </div>
                  <v-switch
                    color="primary"
                    v-model="editedPersonalDetails.Military_Service"
                    :true-value="1"
                    :false-value="0"
                    @update:model-value="onChangeFields()"
                    :disabled="!isActive"
                  ></v-switch>
                </div>
              </v-col>

              <v-col
                v-if="labelList[382]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <div>
                  <div class="text-grey-darken-1" style="font-size: 16px">
                    {{ labelList[382]?.Field_Alias }}
                  </div>
                  <v-switch
                    color="primary"
                    v-model="editedPersonalDetails.Physically_Challenged"
                    :true-value="1"
                    :false-value="0"
                    class="ml-1"
                    @update:model-value="onChangeFields()"
                    :disabled="!isActive"
                  ></v-switch>
                </div>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <div>
                  <div class="text-grey-darken-1" style="font-size: 16px">
                    Are you a smoker?
                  </div>
                  <v-switch
                    color="primary"
                    v-model="editedPersonalDetails.Smoker"
                    :true-value="1"
                    :false-value="0"
                    @update:model-value="onChangeFields()"
                    :disabled="!isActive"
                  >
                  </v-switch>
                </div>
              </v-col>
              <v-col
                v-if="editedPersonalDetails.Smoker"
                cols="12"
                md="4"
                sm="6"
              >
                <section class="text-body-2">
                  <v-menu
                    v-model="smokerMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                    ><template v-slot:activator="{ props }">
                      <v-text-field
                        ref="smokerAsOf"
                        v-model="formattedSmoker"
                        prepend-inner-icon="fas fa-calendar"
                        :rules="[required('Smoker As Of', formattedSmoker)]"
                        readonly
                        v-bind="props"
                        variant="solo"
                        :disabled="!isActive"
                      >
                        <template v-slot:label>
                          Smoker As Of
                          <span style="color: red">*</span>
                        </template></v-text-field
                      >
                    </template>
                    <v-date-picker
                      v-model="editedPersonalDetails.Smokerasof"
                      :min="smokerAsOfMin"
                      :max="currentDate"
                      @update:model-value="onChangeFields()"
                    />
                  </v-menu>
                </section>
              </v-col>
            </v-row>
          </div>
        </div>
        <div
          class="mt-3"
          v-if="labelList[375]?.Field_Visiblity?.toLowerCase() === 'yes'"
        >
          <div>
            <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold">
              Language(s) Known
            </span>
            <span class="d-flex justify-end mt-n6">
              <v-btn
                v-if="editedPersonalDetails.Lang_Known?.length"
                color="primary"
                variant="text"
                @click="
                  editedPersonalDetails.Lang_Known.push({
                    langKnown: null,
                    langSpoken: 0,
                    langReadWrite: 0,
                    langProficiency: null,
                  })
                "
              >
                <v-icon class="mr-1" size="15">fas fa-plus</v-icon>
                Add New
              </v-btn>
            </span>
          </div>
          <v-row class="mt-4">
            <v-col
              v-for="(lang, index) in editedPersonalDetails.Lang_Known"
              :key="index"
              cols="12"
              md="12"
            >
              <v-row>
                <!-- Language Known -->
                <v-col cols="12" md="4" sm="6">
                  <CustomSelect
                    v-model="lang.Lang_Id"
                    :ref="`langKnown${index}`"
                    :items="languageList"
                    :label="labelList[375]?.Field_Alias"
                    :isRequired="
                      labelList[375]?.Mandatory_Field?.toLowerCase() === 'yes'
                    "
                    :rules="
                      labelList['375'].Mandatory_Field === 'Yes'
                        ? [required(labelList['375'].Field_Alias, lang.Lang_Id)]
                        : [true]
                    "
                    clearable
                    :itemSelected="lang.Lang_Id"
                    itemValue="Lang_Id"
                    itemTitle="Language_Name"
                    :isLoading="languageListLoading"
                    @selected-item="
                      onChangeCustomSelectField($event, 'Lang_Id')
                    "
                  />
                </v-col>

                <!-- Spoken -->
                <v-col cols="12" md="2" sm="3">
                  <div class="text-grey-darken-1" style="font-size: 16px">
                    {{ labelList[369]?.Field_Alias }}
                  </div>
                  <v-switch
                    v-model="lang.langSpoken"
                    :true-value="1"
                    :false-value="0"
                    color="primary"
                    class="ml-1"
                  />
                </v-col>

                <!-- Read/Write -->
                <v-col cols="12" md="2" sm="3">
                  <div class="text-grey-darken-1" style="font-size: 16px">
                    {{ labelList[370]?.Field_Alias }}
                  </div>
                  <v-switch
                    v-model="lang.langReadWrite"
                    :true-value="1"
                    :false-value="0"
                    color="primary"
                    class="ml-1"
                  />
                </v-col>

                <!-- Proficiency -->
                <v-col cols="12" md="4" sm="6">
                  <div class="d-flex align-center">
                    <CustomSelect
                      v-if="labelList[371]?.Predefined?.toLowerCase() === 'yes'"
                      :items="languageProficiencyList"
                      :label="labelList[371].Field_Alias"
                      :isAutoComplete="true"
                      :isLoading="languageListLoading"
                      :itemSelected="lang.langProficiency"
                      v-model="lang.langProficiency"
                      itemValue="Language_Proficiency"
                      itemTitle="Language_Proficiency"
                      :isRequired="
                        labelList[371].Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      class="flex-grow-1"
                      variant="solo"
                      clearable
                      :rules="[
                        labelList[371]?.Mandatory_Field?.toLowerCase() === 'yes'
                          ? required(
                              labelList[371]?.Field_Alias,
                              lang.langProficiency
                            )
                          : true,
                      ]"
                      :ref="`langProficiency${index}`"
                      @selected-item="
                        onChangeCustomSelectField($event, 'langProficiency')
                      "
                    />
                    <v-text-field
                      v-else
                      v-model="lang.langProficiency"
                      :ref="`langProficiency${index}`"
                      clearable
                      :rules="[
                        labelList[371]?.Mandatory_Field?.toLowerCase() === 'yes'
                          ? required(
                              labelList[371]?.Field_Alias,
                              lang.langProficiency
                            )
                          : true,
                        validateWithRulesAndReturnMessages(
                          lang.langProficiency,
                          'skillName',
                          labelList[371]?.Field_Alias
                        ),
                      ]"
                      class="flex-grow-1"
                      variant="solo"
                    >
                      <template v-slot:label>
                        <span>{{ labelList[371]?.Field_Alias }}</span>
                        <span
                          v-if="
                            labelList[371]?.Mandatory_Field?.toLowerCase() ===
                            'yes'
                          "
                          style="color: red"
                        >
                          *
                        </span>
                      </template>
                    </v-text-field>
                    <div class="ml-2">
                      <v-icon
                        v-if="editedPersonalDetails.Lang_Known.length > 1"
                        size="15"
                        class="fas fa-trash"
                        color="primary"
                        @click="
                          editedPersonalDetails.Lang_Known.splice(index, 1)
                        "
                      />
                    </div>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </div>
      </v-form>
    </div>
  </div>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateEditForm()"
          >
            <span class="primary">Save</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  ADD_UPDATE_PERSONAL_DETAILS,
  RETRIEVE_MAX_EMP_ID,
  VALIDATE_FIELD_AVAILABILITY,
} from "@/graphql/employee-profile/profileQueries.js";
import {
  LIST_MARITAL_STATUS,
  LIST_RELIGIONS,
  LIST_NATIONALITIES,
  GENDER_LIST,
  TAX_CODE_LIST,
} from "@/graphql/dropDownQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import { VueTelInput } from "vue-tel-input";
import {
  RETRIEVE_EMPLOYEE_NUMBER_SERIES,
  GET_MAX_EMPLOYEE_ID,
} from "@/graphql/settings/core-hr/employeeNumberSeries.js";
import { getCustomFieldName } from "@/helper";

export default {
  name: "EditPersonalDetails",

  mixins: [validationRules],
  emits: ["close-edit-form", "edit-updated", "close-add-form"],

  props: {
    personalDetails: {
      type: Object,
      required: true,
    },
    actionType: {
      type: String,
      default: "",
    },
    selectedEmpStatus: {
      type: String,
      default: "Active",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
  },

  components: {
    CustomSelect,
    VueTelInput,
  },

  data() {
    return {
      dropdownLoading: false,
      genderExpressionList: [],
      genderIdentityList: [],
      isMounted: false,
      showMoreDetails: false,
      editedPersonalDetails: {},
      isFormDirty: false,
      //Date-picker
      formattedDateOfBirth: "",
      defaultDOB: "",
      formattedSmoker: "",
      dobMenu: false,
      smokerMenu: false,
      alreadyExistErrMsg: {
        Aadhaar_Card_Number: true,
        PAN: true,
        UAN: true,
        External_EmpId: true,
        User_Defined_EmpId: true,
      },
      openWarningModal: false,
      // add
      fetchingMaxEmpId: false,
      // edit
      openBottomSheet: true,
      isLoading: false,
      validationMessages: [],
      hobbiesCombo: [],
      showValidationAlert: false,
      // dropdown list
      languageList: [],
      languageListLoading: false,
      maritalStatusList: [],
      maritalStatusListLoading: false,
      religionList: [],
      nationalityList: [],
      nationalityListLoading: false,
      religionListLoading: false,
      genderList: [],
      genderListLoading: false,
      genderOrientationList: [],
      languageProficiencyList: [],
      pronounList: [],
      taxCodeListLoading: false,
      taxCodeList: [],
      defaultCountryCode: "",
      idGenerationconfig: false,
      idGenerationCoverage: "",
      serviceProviders: [],
      serviceProviderId: null,
      dropdownListFetching: false,
      fieldForce: 0,
      settingsArray: [],
    };
  },

  computed: {
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    smokerAsOfMin() {
      if (
        this.editedPersonalDetails.DOB &&
        this.editedPersonalDetails.DOB !== "0000-00-00"
      ) {
        return moment(this.editedPersonalDetails.DOB).format("YYYY-MM-DD");
      } else return null;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    defaultBirthDate() {
      let minBirthDate = new Date();
      minBirthDate.setFullYear(minBirthDate.getFullYear() - 30);
      minBirthDate.setDate(1);
      const minBirthDateISO = minBirthDate;
      return minBirthDateISO;
    },
    minimumBirthDate() {
      let minBirthDate = new Date();
      minBirthDate.setFullYear(minBirthDate.getFullYear() - 80);
      const minBirthDateISO = minBirthDate;
      return moment(minBirthDateISO).format("YYYY-MM-DD");
    },
    maximumBirthDate() {
      let minBirthDate = new Date();
      minBirthDate.setFullYear(minBirthDate.getFullYear() - 15);
      const minBirthDateISO = minBirthDate;
      return moment(minBirthDateISO).format("YYYY-MM-DD");
    },
    hobbies() {
      return this.hobbiesCombo && this.hobbiesCombo.length > 0
        ? this.hobbiesCombo.join(",")
        : "";
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    isValidExternalEmpId() {
      if (this.editedPersonalDetails.External_EmpId) {
        let field = this.$refs.externalEmpId;
        if (field && field.rules && field.rules.length > 0) {
          return field.rules.every((value) => value === true);
        } else {
          return true;
        }
      } else {
        return true;
      }
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    isActive() {
      if (this.selectedEmpStatus.toLowerCase() == "active") {
        return true;
      } else {
        return false;
      }
    },
  },

  watch: {
    isFormDirty(val) {
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", val);
    },
    genderList() {
      if (
        this.editedPersonalDetails.Gender_Id === 0 ||
        this.editedPersonalDetails.Gender_Id === null
      ) {
        let genderValue = this.genderList.find(
          (el) => el.gender == this.editedPersonalDetails?.Gender
        );
        this.editedPersonalDetails["Gender_Id"] = genderValue?.genderId;
      }
    },
    nationalityList() {
      if (
        this.editedPersonalDetails?.Nationality === 0 ||
        this.editedPersonalDetails.Gender_Id === null
      ) {
        let nationalityValue = this.nationalityList.find(
          (el) => el.nationality === this.editedPersonalDetails?.Nationality
        );
        this.editedPersonalDetails["Nationality_Id"] =
          nationalityValue?.nationalityId;
      }
    },
    "editedPersonalDetails.DOB": function (val) {
      if (val) {
        this.dobMenu = false;
        this.formattedDateOfBirth = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "editedPersonalDetails.Smokerasof": function (val) {
      if (val) {
        this.smokerMenu = false;
        this.formattedSmoker = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.editedPersonalDetails = JSON.parse(
      JSON.stringify(this.personalDetails)
    );
    if (this.editedPersonalDetails.DOB) {
      this.formattedDateOfBirth = this.formatDate(
        this.editedPersonalDetails?.DOB
      );
      this.editedPersonalDetails.DOB = this.editedPersonalDetails.DOB
        ? new Date(this.editedPersonalDetails.DOB)
        : null;
    }
    if (this.editedPersonalDetails.Smokerasof) {
      this.formattedSmoker = this.formatDate(
        this.editedPersonalDetails?.Smokerasof
      );
      this.editedPersonalDetails.Smokerasof = this.editedPersonalDetails
        .Smokerasof
        ? new Date(this.editedPersonalDetails.Smokerasof)
        : null;
    }
    this.editedPersonalDetails.Is_Recruiter =
      this.editedPersonalDetails.Is_Recruiter &&
      this.editedPersonalDetails.Is_Recruiter.toLowerCase() == "yes"
        ? 1
        : 0;
    this.hobbiesCombo = this.editedPersonalDetails.Hobbies
      ? this.editedPersonalDetails.Hobbies.split(",")
      : [];
    let langKnown = [];
    if (this.editedPersonalDetails.Languages?.length) {
      langKnown = this.editedPersonalDetails.Languages.map((item) => ({
        Lang_Id: item.Lang_Id,
        Language_Name: item.Language_Name || null,
        Lang_Spoken: item.Lang_Spoken || null,
        Lang_Read_Write: item.Lang_Read_Write || null,
        Lang_Proficiency: item.Lang_Proficiency || null,
      }));
    }
    if (this.labelList[380]?.Field_Visiblity?.toLowerCase() === "yes") {
      this.editedPersonalDetails["Lang_Known"] = langKnown.map(
        (lang) => lang.Lang_Id
      );
    } else if (this.labelList[375]?.Field_Visiblity?.toLowerCase() === "yes") {
      if (!langKnown.length) {
        this.editedPersonalDetails["Lang_Known"] = [
          {
            Lang_Id: null,
            langSpoken: 0,
            langReadWrite: 0,
            langProficiency: null,
          },
        ];
      } else {
        this.editedPersonalDetails["Lang_Known"] = langKnown.map((lang) => ({
          Lang_Id: lang.Lang_Id,
          langSpoken: lang.Lang_Spoken ? 1 : 0,
          langReadWrite: lang.Lang_Read_Write ? 1 : 0,
          langProficiency: lang.Lang_Proficiency,
        }));
      }
    } else {
      this.editedPersonalDetails["Lang_Known"] = [];
    }
    this.editedPersonalDetails["Marital_Status"] =
      this.editedPersonalDetails["Marital_Status"] &&
      this.editedPersonalDetails["maritalStatusName"]
        ? parseInt(this.editedPersonalDetails["Marital_Status"])
        : null;
    this.retrieveLanguages();
    this.retrieveMaritalStatusList();
    if (this.actionType === "add" && !this.selectedEmpId) {
      this.getCoverageSetting();
      this.onChangeFields();
    }
    this.showMoreDetails = true;
    this.retrieveNationalityList();
    this.retrieveReligionList();
    this.getDropdownDetails();
    this.retrieveGender();
    this.retrieveTaxCodeList();
    this.defaultDOB = new Date(this.defaultBirthDate);
    this.isMounted = true;
    this.retrieveDropdownDetails();
    if (this.entomoIntegrationEnabled && !this.selectedEmpId) {
      this.editedPersonalDetails["Allow_User_Signin"] = 1;
    }
  },

  methods: {
    getCustomFieldName,
    onChangeDefaultDOB(value) {
      this.isFormDirty = true;
      this.editedPersonalDetails.DOB = value;
    },
    getCountryCode(countryCode) {
      if (countryCode && countryCode.iso2) {
        this.defaultCountryCode = countryCode.iso2.toLowerCase();
        if (
          this.editedPersonalDetails.Nationality_Id == null &&
          this.editedPersonalDetails.Nationality == null
        ) {
          this.assignNationalityBasedOnCode();
        }
      }
    },
    retrieveDropdownDetails() {
      this.dropdownListFetching = true;
      this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const { serviceProvider, fieldForce } =
              res.data.getDropDownBoxDetails;
            this.serviceProviders = serviceProvider;
            this.fieldForce = fieldForce;
          }
          this.dropdownListFetching = false;
        })
        .catch(() => {
          this.dropdownListFetching = false;
        });
    },
    getCoverageSetting() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMPLOYEE_NUMBER_SERIES,
          variables: { formId: 243 },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listEmployeeIdPrefixSettings) {
            vm.idGenerationconfig =
              response.data.listEmployeeIdPrefixSettings.config?.isEnabled ||
              false;
            vm.idGenerationCoverage =
              response.data.listEmployeeIdPrefixSettings.config?.configLevel ||
              "";
            vm.settingsArray =
              response.data.listEmployeeIdPrefixSettings.employeeIdPrefixSettings;
            if (
              vm.idGenerationconfig &&
              vm.idGenerationCoverage?.toLowerCase() !== "serviceprovider"
            ) {
              vm.getEmployeeId();
            } else if (!vm.idGenerationconfig) {
              vm.retrieveEmpMaxId();
            }
          } else {
            vm.idGenerationconfig = false;
            vm.idGenerationCoverage = "";
            vm.settingsArray = [];
            vm.retrieveEmpMaxId();
          }
          vm.isLoading = false;
        })
        .catch(() => {
          vm.idGenerationconfig = false;
          vm.idGenerationCoverage = "";
          vm.settingsArray = [];
          vm.isLoading = false;
          vm.retrieveEmpMaxId();
        });
    },
    getEmployeeId() {
      let vm = this;
      vm.fetchingMaxEmpId = true;
      vm.$apollo
        .query({
          query: GET_MAX_EMPLOYEE_ID,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
          variables: {
            serviceProviderId:
              vm.idGenerationCoverage?.toLowerCase() === "serviceprovider"
                ? this.editedPersonalDetails.Service_Provider_Id
                : null,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveMaxEmployeeId &&
            !response.data.retrieveMaxEmployeeId.errorCode
          ) {
            const { maxEmployeeId } = response.data.retrieveMaxEmployeeId;
            vm.editedPersonalDetails["User_Defined_EmpId"] = maxEmployeeId
              ? maxEmployeeId.toString()
              : "";
            vm.validateFieldAlreadyExist("User_Defined_EmpId", "Employee Id");
          } else {
            vm.editedPersonalDetails["User_Defined_EmpId"] = "";
            vm.showAlert({
              isOpen: true,
              message:
                response.data?.retrieveMaxEmployeeId?.message ||
                "Something went wrong while retrieving the employee id. Please try after some time.",
              type: "warning",
            });
          }
          vm.fetchingMaxEmpId = false;
        })
        .catch((err) => {
          vm.fetchingMaxEmpId = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "employee id",
            isListError: false,
          });
        });
    },
    retrieveNationalityList() {
      let vm = this;
      vm.nationalityListLoading = true;
      vm.$apollo
        .query({
          query: LIST_NATIONALITIES,
          variables: { Org_Code: this.$store.getters.orgCode },
          client: "apolloClientX",
        })
        .then((data) => {
          let res = data.data;
          if (
            res &&
            res.retrieveNationalityList &&
            res.retrieveNationalityList.nationalityData
          ) {
            this.nationalityList = res.retrieveNationalityList.nationalityData;
            if (
              this.editedPersonalDetails.Nationality_Id == null &&
              this.editedPersonalDetails.Nationality == null
            ) {
              this.assignNationalityBasedOnCode();
            }
          }
          vm.nationalityListLoading = false;
        })
        .catch(() => {
          vm.nationalityListLoading = false;
        });
    },
    assignNationalityBasedOnCode() {
      let nationalityIdList = this.nationalityList.map((ele) => {
        return ele.nationalityId;
      });
      if (this.defaultCountryCode == "in") {
        if (nationalityIdList.includes(12)) {
          this.editedPersonalDetails.Nationality_Id = 12;
          this.editedPersonalDetails.Nationality = "Indian";
        }
      } else if (this.defaultCountryCode == "ph") {
        if (nationalityIdList.includes(3)) {
          this.editedPersonalDetails.Nationality_Id = 3;
          this.editedPersonalDetails.Nationality = "Filipino";
        }
      } else if (this.defaultCountryCode == "id") {
        if (nationalityIdList.includes(5)) {
          this.editedPersonalDetails.Nationality_Id = 5;
          this.editedPersonalDetails.Nationality = "Indonesian";
        }
      } else if (this.defaultCountryCode == "th") {
        if (nationalityIdList.includes(9)) {
          this.editedPersonalDetails.Nationality_Id = 9;
          this.editedPersonalDetails.Nationality = "Thai";
        }
      }
    },
    retrieveReligionList() {
      let vm = this;
      vm.religionListLoading = true;
      vm.$apollo
        .query({
          query: LIST_RELIGIONS,
          variables: { Org_Code: this.$store.getters.orgCode },
          client: "apolloClientX",
        })
        .then((data) => {
          let res = data.data;
          if (
            res &&
            res.retrieveReligionList &&
            res.retrieveReligionList.religionData
          ) {
            this.religionList = res.retrieveReligionList.religionData;
          }
          vm.religionListLoading = false;
        })
        .catch(() => {
          vm.religionListLoading = false;
        });
    },

    retrieveTaxCodeList() {
      let vm = this;
      vm.taxCodeListLoading = true;
      vm.$apollo
        .query({
          query: TAX_CODE_LIST,
          client: "apolloClientAS",
        })
        .then((data) => {
          let res = data.data;
          if (
            res.listTimekeepingCareerDetail &&
            res.listTimekeepingCareerDetail.taxDetail
          ) {
            this.taxCodeList = res.listTimekeepingCareerDetail.taxDetail;
          }
          vm.taxCodeListLoading = false;
        })
        .catch(() => {
          vm.taxCodeListLoading = false;
        });
    },
    checkFieldAvailability(value) {
      if (value) {
        let strValue = value.toString();
        return strValue.trim().length > 0;
      } else return false;
    },
    onChangeFields(field = "") {
      this.isFormDirty = true;
      if (field) {
        this.alreadyExistErrMsg[field] = true;
      }
    },
    onChangeCustomSelectField(value, field) {
      if (field == "Religion_Id") {
        this.editedPersonalDetails.Religion = "";
      }
      if (field == "Nationality_Id") {
        this.editedPersonalDetails.Nationality = "";
      }
      this.onChangeFields();
      this.editedPersonalDetails[field] = value;
      if (
        field === "Service_Provider_Id" &&
        this.idGenerationconfig &&
        this.idGenerationCoverage?.toLowerCase() === "serviceprovider"
      ) {
        this.getEmployeeId();
      }
    },

    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    closeEditForm() {
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        if (this.actionType === "add" && !this.selectedEmpId) {
          this.$emit("close-add-form");
        } else {
          this.openBottomSheet = false;
          this.$emit("close-edit-form");
        }
      }
    },

    async validateEditForm() {
      let isFormValid = await this.$refs.editPersonalDetailsForm.validate();
      mixpanel.track("EmpProfile-personalDetails-edit-submit-click");
      if (isFormValid && isFormValid.valid) {
        if (
          this.editedPersonalDetails.Nationality_Id &&
          this.editedPersonalDetails.Nationality_Id != 11
        ) {
          let selectedNationality = this.nationalityList.filter(
            (el) =>
              el.nationalityId == this.editedPersonalDetails.Nationality_Id
          );
          this.editedPersonalDetails.Nationality =
            selectedNationality && selectedNationality[0]
              ? selectedNationality[0].nationality
              : "";
        }
        if (
          this.editedPersonalDetails.Religion_Id &&
          this.editedPersonalDetails.Religion_Id != 67
        ) {
          let selectedReligion = this.religionList.filter(
            (el) => el.religionId == this.editedPersonalDetails.Religion_Id
          );
          this.editedPersonalDetails.Religion =
            selectedReligion && selectedReligion[0]
              ? selectedReligion[0].religion
              : "";
        }
        this.updatePersonalDetails();
      } else {
        // Check the validity of each field
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            let allTrue = field.rules.every((value) => value === true);
            if (field.rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });
        // Log or handle the invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              let selectFields = [
                "salutation",
                "gender",
                "genderOrientation",
                "pronoun",
                "maritalStatus",
                "bloodGroup",
                "languageKnown",
                "religionDropdown",
                "nationalityDropdown",
              ];
              if (selectFields.includes(firstErrorField)) {
                fieldRef.onFocusCustomSelect();
              } else {
                // except for select
                fieldRef.focus();
              }
              if (fieldRef.$el) {
                const rect = fieldRef.$el.getBoundingClientRect();
                window.scrollTo({
                  top: (window.scrollY + rect.top) * 2, // Adjust as needed
                  behavior: "smooth",
                });
              }
            }
          });
        }
      }
    },

    getDropdownDetails() {
      let vm = this;
      vm.dropdownLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;
      vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            formId: 243,
            key: [
              "Gender_Identity_Id",
              "Gender_Expression_Id",
              "gender_orientations",
              "gender_pronoun",
              "language_proficiency",
            ],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            tempData.forEach((item) => {
              if (item.tableKey?.toLowerCase() === "gender_identity_id") {
                vm.genderIdentityList = item.data.map((dataItem) => ({
                  Gender_Identity_Id: dataItem.Gender_Identity_Id,
                  Gender_Identity: dataItem.Gender_Identity,
                }));
              } else if (item.tableKey?.toLowerCase() === "gender_pronoun") {
                vm.pronounList = item.data.map((dataItem) => ({
                  Gender_Pronoun_Id: dataItem.Gender_Pronoun_Id,
                  Gender_Pronoun_Name: dataItem.Gender_Pronoun_Name,
                }));
              } else if (
                item.tableKey?.toLowerCase() === "gender_orientations"
              ) {
                vm.genderOrientationList = item.data.map((dataItem) => ({
                  Gender_Orientations_Id: dataItem.Gender_Orientations_Id,
                  Gender_Orientations_Name: dataItem.Gender_Orientations_Name,
                }));
              } else if (
                item.tableKey?.toLowerCase() === "language_proficiency"
              ) {
                vm.languageProficiencyList = item.data.map((dataItem) => ({
                  Language_Proficiency_Id: dataItem.Language_Proficiency_Id,
                  Language_Proficiency: dataItem.Language_Proficiency,
                }));
              } else if (
                item.tableKey?.toLowerCase() === "gender_expression_id"
              ) {
                vm.genderExpressionList = item.data.map((dataItem) => ({
                  Gender_Expression_Id: dataItem.Gender_Expression_Id,
                  Gender_Expression: dataItem.Gender_Expression,
                }));
              }
            });
          } else {
            let err = res.data.retrieveDropdownDetails.errorCode;
            vm.handleGetDropdownDetails(err);
          }
          vm.dropdownLoading = false;
        })
        .catch((err) => {
          vm.dropdownLoading = false;
          vm.handleGetDropdownDetails(err);
        });
    },
    handleGetDropdownDetails(err) {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "dropdown details",
        isListError: false,
      });
    },
    updatePersonalDetails() {
      let vm = this;
      vm.isLoading = true;
      let genderValue = vm.genderList.filter(
        (el) => el.genderId == vm.editedPersonalDetails.Gender_Id
      );
      let langArray = [];
      let languagesKnown = [];
      langArray = vm.editedPersonalDetails.Lang_Known;
      if (this.labelList[380]?.Field_Visiblity?.toLowerCase() === "yes") {
        languagesKnown = langArray.map((langId) => ({
          Lang_Known: langId,
          Lang_Spoken: null,
          Lang_Read_Write: null,
          Lang_Proficiency: "",
        }));
      } else if (
        this.labelList[375]?.Field_Visiblity?.toLowerCase() === "yes"
      ) {
        languagesKnown = langArray.map((lang) => ({
          Lang_Known: lang.Lang_Id,
          Lang_Spoken: lang.langSpoken,
          Lang_Read_Write: lang.langReadWrite,
          Lang_Proficiency: lang.langProficiency,
        }));
      } else {
        languagesKnown = [];
      }
      let settingId = null;
      if (this.idGenerationconfig) {
        if (this.idGenerationCoverage?.toLowerCase() === "serviceprovider") {
          settingId = this.settingsArray.find((item) => {
            return (
              item.serviceProviderId ===
              this.editedPersonalDetails.Service_Provider_Id
            );
          })?.empPrefixSettingId;
        } else {
          settingId = this.settingsArray.find((item) => {
            return item.serviceProviderId === null;
          })?.empPrefixSettingId;
        }
      }
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_PERSONAL_DETAILS,
          variables: {
            employeeId: vm.editedPersonalDetails.Employee_Id
              ? vm.editedPersonalDetails.Employee_Id
              : 0,
            userDefinedEmpId: vm.editedPersonalDetails.User_Defined_EmpId,
            biometricIntegrationId: vm.editedPersonalDetails.External_EmpId,
            photoPath: vm.editedPersonalDetails.Photo_Path,
            salutation: vm.editedPersonalDetails.Salutation,
            empFirstName: vm.editedPersonalDetails.Emp_First_Name,
            empMiddleName: vm.editedPersonalDetails.Emp_Middle_Name,
            empLastName: vm.editedPersonalDetails.Emp_Last_Name,
            knownAs: vm.editedPersonalDetails.Emp_Pref_First_Name,
            gender: genderValue[0].gender,
            genderId: vm.editedPersonalDetails.Gender_Id,
            genderIdentityId:
              parseInt(vm.editedPersonalDetails.Gender_Identity_Id) || null,
            genderExpressionId:
              parseInt(vm.editedPersonalDetails.Gender_Expression_Id) || null,
            dob: moment(vm.editedPersonalDetails.DOB).isValid()
              ? moment(vm.editedPersonalDetails.DOB).format("YYYY-MM-DD")
              : null,
            placeOfBirth: vm.editedPersonalDetails.Place_Of_Birth,
            maritalStatus: vm.editedPersonalDetails.Marital_Status,
            bloodGroup: vm.editedPersonalDetails.Blood_Group,
            languages: languagesKnown,
            nationality: vm.editedPersonalDetails.Nationality,
            taxCode: vm.editedPersonalDetails?.Tax_Code,
            nationalityId: vm.editedPersonalDetails.Nationality_Id
              ? vm.editedPersonalDetails.Nationality_Id
              : 11,
            religion: vm.editedPersonalDetails.Religion,
            religionId: vm.editedPersonalDetails.Religion_Id
              ? vm.editedPersonalDetails.Religion_Id
              : 67,
            appellation: vm.editedPersonalDetails.Suffix,
            militaryService: vm.editedPersonalDetails.Military_Service,
            caste: vm.editedPersonalDetails.Caste,
            disabled: vm.editedPersonalDetails.Physically_Challenged,
            isManager: vm.editedPersonalDetails.Is_Manager,
            isRecruiter: vm.editedPersonalDetails.Is_Recruiter ? "Yes" : "No",
            personalEmail: vm.editedPersonalDetails.Personal_Email,
            smoker: vm.editedPersonalDetails.Smoker,
            smokerAsOf:
              vm.editedPersonalDetails.Smoker &&
              moment(vm.editedPersonalDetails.Smokerasof).isValid()
                ? moment(vm.editedPersonalDetails.Smokerasof).format(
                    "YYYY-MM-DD"
                  )
                : null,
            aadharNumber: vm.editedPersonalDetails.Aadhaar_Card_Number
              ? vm.editedPersonalDetails.Aadhaar_Card_Number.toString()
              : "",
            uan: vm.editedPersonalDetails.UAN,
            pan: vm.editedPersonalDetails.PAN,
            formStatus: vm.editedPersonalDetails.Form_Status
              ? vm.editedPersonalDetails.Form_Status
              : 0,
            allowUserSignIn: vm.editedPersonalDetails.Allow_User_Signin,
            enableMobileSignIn:
              vm.editedPersonalDetails.Enable_Sign_In_With_Mobile_No,
            formId: vm.callingFrom === "profile" ? 18 : 243,
            hobbies: vm.hobbies,
            genderOrientations: vm.editedPersonalDetails.Gender_Orientations,
            pronoun: vm.editedPersonalDetails.Pronoun,
            statutoryInsuranceNumber: vm.editedPersonalDetails
              .Statutory_Insurance_Number
              ? vm.editedPersonalDetails.Statutory_Insurance_Number.toString()
              : "",
            pranNo: vm.editedPersonalDetails.PRAN_No
              ? vm.editedPersonalDetails.PRAN_No.toString()
              : "",
            empPrefixSettingId: settingId,
          },
          client: "apolloClientAD",
        })
        .then((res) => {
          mixpanel.track("EmpProfile-personalDetails-edit-submit-success");
          if (res && res.data && res.data.addUpdatePersonalInfo) {
            const { errorCode, employeeId } = res.data.addUpdatePersonalInfo;
            if (!errorCode) {
              let snackbarData = {
                isOpen: true,
                message:
                  vm.actionType === "add" && !vm.selectedEmpId
                    ? "Personal details added successfully"
                    : "Personal details updated successfully",
                type: "success",
              };
              vm.showAlert(snackbarData);
              vm.isLoading = false;
              vm.$store.commit(
                "employeeProfile/UPDATE_EDIT_FORM_CHANGED",
                false
              );
              vm.$emit("edit-updated", employeeId);
            } else {
              vm.handleUpdateError();
            }
          } else {
            vm.handleUpdateError();
          }
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("EmpProfile-personalDetails-edit-submit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action:
            this.actionType === "add" && !this.selectedEmpId
              ? "adding"
              : "updating",
          form: "personal details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    async retrieveLanguages() {
      this.languageListLoading = true;
      this.languageList = [];
      await this.$store
        .dispatch("listLanguages")
        .then((langList) => {
          this.languageList = langList;
          this.languageListLoading = false;
        })
        .catch(() => {
          this.languageListLoading = false;
        });
    },

    retrieveMaritalStatusList() {
      let vm = this;
      vm.maritalStatusListLoading = true;
      vm.$apollo
        .query({
          query: LIST_MARITAL_STATUS,
          client: "apolloClientAC",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveMaritalStatus &&
            !response.data.retrieveMaritalStatus.errorCode
          ) {
            const { maritalStatus } = response.data.retrieveMaritalStatus;
            vm.maritalStatusList = maritalStatus
              ? JSON.parse(maritalStatus)
              : [];
          }
          vm.maritalStatusListLoading = false;
        })
        .catch(() => {
          vm.maritalStatusListLoading = false;
        });
    },

    retrieveEmpMaxId() {
      let vm = this;
      if (!vm.editedPersonalDetails["User_Defined_EmpId"]) {
        vm.fetchingMaxEmpId = true;
        vm.$apollo
          .query({
            query: RETRIEVE_MAX_EMP_ID,
            client: "apolloClientAC",
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.retrieveMaxEmployeeId &&
              !response.data.retrieveMaxEmployeeId.errorCode
            ) {
              const { maxEmployeeId } = response.data.retrieveMaxEmployeeId;
              vm.editedPersonalDetails["User_Defined_EmpId"] = maxEmployeeId
                ? (parseInt(maxEmployeeId) + 1).toString()
                : "";
              vm.validateFieldAlreadyExist("User_Defined_EmpId", "Employee Id");
            }
            vm.fetchingMaxEmpId = false;
          })
          .catch(() => {
            vm.fetchingMaxEmpId = false;
          });
      }
    },

    retrieveGender() {
      let vm = this;
      vm.genderListLoading = true;
      vm.$apollo
        .query({
          query: GENDER_LIST,
          client: "apolloClientX",
          variables: {
            Org_Code: this.orgCode,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveGenderList &&
            !response.data.retrieveGenderList.errorCode
          ) {
            const { genderData } = response.data.retrieveGenderList;
            vm.genderList = genderData;
          }
          vm.genderListLoading = false;
        })
        .catch(() => {
          vm.genderListLoading = false;
        });
    },

    validateFieldAlreadyExist(field, label, table = "emp_job") {
      let vm = this;
      if (
        vm.editedPersonalDetails[field] &&
        vm.editedPersonalDetails[field] !== vm.personalDetails[field]
      ) {
        vm.$apollo
          .query({
            query: VALIDATE_FIELD_AVAILABILITY,
            client: "apolloClientAC",
            variables: {
              employeeId: vm.editedPersonalDetails.Employee_Id
                ? vm.editedPersonalDetails.Employee_Id
                : 0,
              columnValue: vm.editedPersonalDetails[field],
              columnName: field,
              tableName: table,
            },
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.validateCommonAvailability &&
              !response.data.validateCommonAvailability.errorCode
            ) {
              const { isAvailable } = response.data.validateCommonAvailability;
              if (!isAvailable) {
                vm.alreadyExistErrMsg[field] = label + " already exist";
                vm.$refs.editPersonalDetailsForm.validate();
              } else {
                vm.alreadyExistErrMsg[field] = true;
              }
            }
          })
          .catch((err) => {
            vm.alreadyExistErrMsg[field] = true;
            vm.$refs.editPersonalDetailsForm.validate();
            let fieldLabel = label.toLowerCase();
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              action: "validating",
              form: fieldLabel,
              isListError: false,
            });
          });
      }
    },
  },
};
</script>
