import moment from "moment";
import validationRules from "./validationRules";

const {
  validateWithRulesAndReturnMessages,
} = require("@cksiva09/validationlib/src/validator");
const {
  numericRequiredValidation,
  alphaNumSpaceNewLineWithElevenSymbolValidation,
} = validationRules.methods;

export default {
  data() {
    return {
      workSchedules: [],
      designations: [],
      departments: [],
      locations: [],
      employeeTypes: [],
      managers: [],
      roles: [],
      effectiveDates: [],
    };
  },
  methods: {
    async getDropDownBoxDetails() {
      await this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const {
              departments,
              locations,
              employeeType,
              workSchedules,
              managers,
              roles,
            } = res.data.getDropDownBoxDetails;
            this.departments = this.departments.concat(departments);
            this.locations = this.locations.concat(locations);
            this.employeeTypes = this.employeeTypes.concat(employeeType);
            this.workSchedules = this.workSchedules.concat(workSchedules);
            this.managers = this.managers.concat(managers);
            this.roles = this.roles.concat(roles);
          }
        });
    },

    async getDesignationList(offset) {
      await this.$store
        .dispatch("getDesignationList", {
          offset: offset,
          limit: 25000,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            const { designationResult } = res.data.getDesignationDetails;
            this.designations = this.designations.concat(designationResult);
          }
        })
        .catch(() => {});
    },

    validateData(data, type, fullData, row) {
      if (type === "employee") {
        var employeeId = row.EmployeeId;
        var errorMessage = ""; // Variable to store the error message
        switch (data.type) {
          case "Designation":
            if (!data.changed) {
              errorMessage = "Designation required";
            } else {
              var designations = this.designations.map(
                (el) => el.Designation_Name
              );
              if (!designations.includes(data.changed)) {
                errorMessage = "Invalid Designation";
              }
            }
            break;

          case "Department":
            if (!data.changed) {
              errorMessage = "Department required";
            } else {
              var departments = this.departments.map(
                (el) => el.Department_Name
              );
              if (!departments.includes(data.changed)) {
                errorMessage = "Invalid Department";
              }
            }
            break;

          case "Location":
            if (!data.changed) {
              errorMessage = "Location required";
            } else {
              var locations = this.locations.map((el) => el.Location_Name);
              if (!locations.includes(data.changed)) {
                errorMessage = "Invalid Location";
              }
            }
            break;

          case "EmployeeType":
            if (!data.changed) {
              errorMessage = "Employee Type required";
            } else {
              var employeeTypes = this.employeeTypes.map(
                (el) => el.Employee_Type
              );
              if (!employeeTypes.includes(data.changed)) {
                errorMessage = "Invalid Employee Type";
              }
            }
            break;

          case "WorkSchedule":
            if (!data.changed) {
              errorMessage = "Workschedule required";
            } else {
              var workSchedules = this.workSchedules.map((el) => el.Title);
              if (!workSchedules.includes(data.changed)) {
                errorMessage = "Invalid Work Schedule";
              }
            }
            break;

          case "Manager":
            var managers = this.managers.map((el) => el.Manager_Name);
            if (!managers.includes(data.changed)) {
              errorMessage = "Invalid Manager";
            }
            break;

          case "Work_Schedule_End_Date":
          case "Designation_Id_End_Date":
          case "Department_Id_End_Date":
          case "Location_Id_End_Date":
          case "EmpType_Id_End_Date":
          case "Manager_Id_End_Date":
            if (!employeeId) {
              errorMessage = "Employee not found";
              break;
            }

            data.extendValidation.forEach((el) => {
              if (employeeId === el.Employee_Id) {
                let currentDate = moment().format("YYYY-MM-DD");
                if (el["Effective_Date"]) {
                  let effectiveDate = moment(el[data.type]).add(1, "days");
                  let minDate = moment
                    .max(moment(el["Effective_Date"]), effectiveDate)
                    .format("YYYY-MM-DD");
                  let maxDate = currentDate;

                  //Check if min date and max date are correct else keep the min date as max date
                  if (!moment(maxDate).isAfter(minDate)) {
                    maxDate = minDate;
                  }

                  if (
                    !data.changed ||
                    !moment(data.changed).isBetween(
                      minDate,
                      maxDate,
                      null,
                      "[]"
                    )
                  ) {
                    errorMessage = `Date should be between ${minDate} and ${maxDate}`;
                  }
                } else {
                  let effectiveDate = moment(el[data.type]).add(1, "days");
                  let minDate = moment
                    .max(moment(el["Date_Of_Join"]), effectiveDate)
                    .format("YYYY-MM-DD");
                  let maxDate = currentDate;

                  //Check if min date and max date are correct else keep the min date as max date
                  if (!moment(maxDate).isAfter(minDate)) {
                    maxDate = minDate;
                  }
                  if (
                    !data.changed ||
                    !moment(data.changed, "YYYY-MM-DD").isBetween(
                      minDate,
                      maxDate,
                      null,
                      "[]"
                    )
                  ) {
                    errorMessage = `Date should be between ${minDate} and ${maxDate}`;
                  }
                }
              }
            });
            break;

          case "Email":
            if (!data.changed) {
              break;
            }

            if (data.changed.length > 50) {
              errorMessage = "Email should not exceed 50 characters";
              break;
            }

            // Sanitize input to remove any potentially harmful characters
            var sanitizedEmail = data.changed.trim();

            // Improved regex pattern for email validation
            var emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (!emailRegex.test(sanitizedEmail)) {
              errorMessage = "Invalid Email";
              break;
            }

            var duplicates = fullData.filter((e) => e.Email === sanitizedEmail);
            if (duplicates.length > 1) {
              errorMessage = "Same Email Already Exists";
            }
            break;

          default:
            break;
        }

        return errorMessage; // Return the error message
      } else if (type === "bonusImport") {
        switch (data.type) {
          case "Bonus Amount":
            if (!data.changed) {
              errorMessage = "Bonus Amount is required";
            }
            break;
          default:
            break;
        }

        return errorMessage; // Return the error message
      } else if (type === "tdsHistoryImport") {
        const minValue = 0;
        const maxValue = 9999999999999;
        let errorMessage = "";
        if (data.type === "History Type" && !data.changed) {
          errorMessage = "History Type is required";
        }

        const requiredFields = [
          "Gross Salary",
          "Basic Pay",
          "Dearness Allowance",
          "House Rent Allowance",
          "Other Allowance",
          "Commission",
          "Perquisites",
          "Profits In Lieu Of Salary",
          "Professional Tax",
          "Total Income Tax Paid",
          "Medical Expense",
          "Gratuity Amount",
          "VRS Exemption Amount",
          "No of times LTA claimed",
        ];

        const notRequiredFields = [
          "Medical Insurance",
          "Leave Encashment Exemptions",
          "No Of times LTA carried forward",
          "Education cess",
          "Surcharge",
          "Total Employee PF contribution",
          "Total Employee VPF Contribution",
          "Total Employer NPS Contribution",
        ];

        if (requiredFields.includes(data.type)) {
          errorMessage = this.validateField(
            data,
            minValue,
            maxValue,
            "Required"
          );
        }

        if (notRequiredFields.includes(data.type)) {
          errorMessage = this.validateField(
            data,
            minValue,
            maxValue,
            "Not Required"
          );
        }

        return errorMessage;
      } else if (type === "taxRelief") {
        switch (data.type) {
          case "Employee Id":
          case "Employee Name":
            if (!data.changed) {
              errorMessage = "Field is required";
            }
            break;
          case "Amount":
            if (!data.changed) {
              errorMessage = "Amount is required";
            }
            var amountValidator = validateWithRulesAndReturnMessages(
              data.changed,
              "approvedAmount",
              "Amount",
              true
            );
            if (amountValidator.length) {
              errorMessage = amountValidator;
            }
            break;
          case "Month":
            var months = data.extendValidation.months;
            if (!months.includes(data.changed)) {
              errorMessage = "Invalid Month";
            }
            //Validate max payslip
            var maxPayslip = data.extendValidation.maxPayslip.find(
              (el) => el.User_Defined_EmpId == row["Employee Id"]
            );
            if (maxPayslip) {
              if (
                moment(maxPayslip.Max_Payslip).isSameOrAfter(
                  moment(data.changed)
                )
              ) {
                errorMessage =
                  "Payslip already generated for this month / Employee's date of join is beyond this month";
              }
            }
            break;
          case "Tax Relief Category":
            var categories = data.extendValidation.categoryList;
            if (!categories.includes(data.changed)) {
              errorMessage = "Invalid Tax Relief Category";
            }
            break;
          default:
            break;
        }
        return errorMessage; // Return the error message
      } else if (type == "activities") {
        switch (data.type) {
          case "Activity":
            if (!data.changed) {
              errorMessage = "Activity is required";
              break;
            }

            var activityName = validateWithRulesAndReturnMessages(
              data.changed,
              "activityName",
              "Activity"
            );
            if (activityName.length) {
              errorMessage = activityName;
              break;
            }
            var activity = data.extendValidation.activity;
            if (activity && activity.includes(data.changed)) {
              errorMessage = "Activity already exists";
              break;
            }
            var activityDuplicates = fullData.filter(
              (e) => e.Activity === data.changed
            );
            if (activityDuplicates.length > 1) {
              errorMessage = "Activity duplicated";
            }
            break;
          case "Billable":
            var billable = data.extendValidation.billable;
            if (billable && !billable.includes(data.changed)) {
              errorMessage = "Invalid Billable";
            }
            if (!data.changed) {
              errorMessage = "Billable is required";
            }
            break;
          case "Activity Description":
            var activityDescription =
              data.changed && data.changed.length
                ? validateWithRulesAndReturnMessages(
                    data.changed,
                    "description",
                    "Activity Description"
                  )
                : "";
            if (activityDescription && activityDescription.length) {
              errorMessage = activityDescription;
            }
            break;
          default:
            break;
        }
        return errorMessage; // Return the error message
      } else if (type == "departmentsHierarchy") {
        switch (data.type) {
          case "Department Code":
            if (!data.changed) {
              errorMessage = "Department Code is required";
              break;
            }

            var departmentCodeName = validateWithRulesAndReturnMessages(
              data.changed,
              "department",
              "Department Code"
            );
            if (departmentCodeName.length) {
              errorMessage = departmentCodeName;
              break;
            }
            var departmentCode = data.extendValidation.departmentCode;
            if (departmentCode && departmentCode.includes(data.changed)) {
              errorMessage = "Department Code already exists";
              break;
            }
            var departmentCodeDuplicates = fullData.filter(
              (e) => e.Activity === data.changed
            );
            if (departmentCodeDuplicates.length > 1) {
              errorMessage = "Department Code duplicated";
            }
            break;
          case "Department":
            if (!data.changed) {
              errorMessage = "Department is required";
              break;
            }

            var departmentName = validateWithRulesAndReturnMessages(
              data.changed,
              "department",
              "Department"
            );
            if (departmentName.length) {
              errorMessage = departmentName;
              break;
            }
            var department = data.extendValidation.department;
            if (department && department.includes(data.changed)) {
              errorMessage = "Department already exists";
              break;
            }
            var departmentDuplicates = fullData.filter(
              (e) => e.Activity === data.changed
            );
            if (departmentDuplicates.length > 1) {
              errorMessage = "Department duplicated";
            }
            break;
          case "Department Header":
            var departmentHeader = data.extendValidation.departmentHeader;
            if (departmentHeader && departmentHeader.includes(data.changed)) {
              errorMessage = "Invalid Department Header";
            }
            if (!data.changed) {
              errorMessage = "Department Header is required";
            }
            break;
          case "Status":
            var departmentStatus = data.extendValidation.status;
            if (departmentStatus && !departmentStatus.includes(data.changed)) {
              errorMessage = "Invalid Status";
            }
            if (!data.changed) {
              errorMessage = "Status is required";
            }
            break;
          case "Description":
            var departmentDescription =
              data.changed && data.changed.length
                ? validateWithRulesAndReturnMessages(
                    data.changed,
                    "description",
                    "Description"
                  )
                : "";
            if (departmentDescription && departmentDescription.length) {
              errorMessage = departmentDescription;
            }
            break;
          default:
            break;
        }
        return errorMessage; // Return the error message
      } else if (type == "employeeDetails") {
        switch (data.type) {
          case "Employee Id": {
            if (!data.changed) {
              errorMessage = "Employee Id is required";
              break;
            }

            const userDefinedEmpId = validateWithRulesAndReturnMessages(
              data.changed,
              "userDefinedEmpId",
              "Employee Id"
            );

            if (userDefinedEmpId.length) {
              errorMessage = userDefinedEmpId;
              break;
            }

            const duplicateCheck = fullData.filter(
              (e) => e["Employee Id"] === data.changed
            );
            if (duplicateCheck.length > 1) {
              errorMessage = "Employee Id should be unique";
            }
            // Additional validation logic for employeeId, if any
            break;
          }

          case "Biometric Integration Id": {
            if (data.changed) {
              const biometricIntegrationId = validateWithRulesAndReturnMessages(
                data.changed,
                "biometricIntegraionId",
                "Biometric Integration Id"
              );

              if (biometricIntegrationId.length) {
                errorMessage = biometricIntegrationId;
                break;
              }

              const duplicateCheck = fullData.filter(
                (e) => e["Biometric Integration Id"] === data.changed
              );
              if (duplicateCheck.length > 1) {
                errorMessage = "Biometric Integration Id should be unique";
              }
            }
            // Additional validation logic for biometricIntegrationId, if any
            break;
          }

          case "Salutation": {
            if (!data.changed) {
              errorMessage = "Salutation is required";
              break;
            }
            if (
              data.extendValidation &&
              data.extendValidation.Salutation.length
            ) {
              if (!data.extendValidation.Salutation.includes(data.changed)) {
                errorMessage = "Invalid Salutation";
              }
              break;
            }
            // Additional validation logic for salutation, if any
            break;
          }

          case "First Name": {
            if (!data.changed) {
              errorMessage = "First Name is required";
              break;
            }
            let validation = validateWithRulesAndReturnMessages(
              data.changed,
              "empFirstName",
              "First Name"
            );
            if (validation.length) {
              errorMessage = validation;
              break;
            }
            break;
          }

          case "Middle Name": {
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "empMiddleName",
                "Middle Name"
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
              break;
            }
            break;
          }

          case "Last Name": {
            if (!data.changed) {
              errorMessage = "Last Name is required";
              break;
            }
            let validation = validateWithRulesAndReturnMessages(
              data.changed,
              "empLastName",
              "Last Name"
            );
            if (validation.length) {
              errorMessage = validation;
              break;
            }
            break;
          }

          case "Gender": {
            if (!data.changed) {
              errorMessage = "Gender is required";
              break;
            }
            if (!data.extendValidation.Gender.includes(data.changed)) {
              errorMessage = "Invalid Gender";
              break;
            }
            break;
          }

          case "Marital Status": {
            if (!data.changed) {
              errorMessage = "Marital Status is required";
              break;
            }
            if (!data.extendValidation.Marital_Status.includes(data.changed)) {
              errorMessage = "Invalid Marital Status";
              break;
            }
            break;
          }

          case "Date of Birth":
            if (!data.changed) {
              errorMessage = "Date of Birth is required";
              break;
            }
            // Additional validation logic for dateOfBirth, if any
            break;

          case "Blood Group": {
            if (!data.changed) {
              errorMessage = "Blood Group is required";
              break;
            }
            if (!data.extendValidation.Blood_Group.includes(data.changed)) {
              errorMessage = "Blood Group is Invalid";
              break;
            }
            break;
          }

          case "Nationality": {
            if (!data.changed) {
              errorMessage = "Nationality is required";
              break;
            }
            if (!data.extendValidation.Nationality.includes(data.changed)) {
              errorMessage = "Nationality is Invalid";
              break;
            }

            if (data.changed === "Others") {
              if (!row["Other Nationality"]) {
                errorMessage = "Other Nationality is required";
              }
              break;
            }

            // Additional validation logic for nationality, if any
            break;
          }

          case "Other Nationality": {
            if (row["Nationality"] === "Others") {
              if (!data.changed) {
                errorMessage = "Other Nationality is required";
                break;
              }

              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "nationality",
                "Nationality"
              );

              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          }

          case "Tax Identification Number (PAN/TFN/SSN)": {
            if (!data.changed) {
              errorMessage = "Tax  is required";
              break;
            }
            break;
          }

          case "Personal Email": {
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "personalEmail",
                "Personal Email"
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          }

          case "Work Email": {
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "workEmail",
                "Work Email"
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
              const duplicateCheck = fullData.filter(
                (e) => e["Work Email"] === data.changed
              );
              if (duplicateCheck.length > 1) {
                errorMessage = "Work Email should be unique";
              }
            }
            break;
          }

          case "Sign-In Option": {
            if (row["Allow User Sign-In"] === "Yes") {
              if (!data.changed) {
                errorMessage = "Sign-In Option is required";
                break;
              }
              if (
                !data.extendValidation.Sign_In_Option.includes(data.changed)
              ) {
                errorMessage = "Invalid Sign-In Option";
                break;
              }
            }
            break;
          }

          case this.labelList["295"].Field_Alias: {
            if (this.labelList["295"].Mandatory == "Yes" && !data.changed) {
              errorMessage = `${this.labelList["295"].Field_Alias} is required`;
              break;
            } else {
              if (data.changed) {
                let validation = validateWithRulesAndReturnMessages(
                  data.changed,
                  "placeOfBirth",
                  this.labelList["295"].Field_Alias
                );
                if (validation.length) {
                  errorMessage = validation;
                  break;
                }
              }
            }
            break;
          }

          case this.labelList[232].Field_Alias: {
            if (this.labelList[232].Mandatory == "Yes" && !data.changed) {
              errorMessage = `${this.labelList[232].Field_Alias} is required`;
              break;
            } else {
              if (data.changed) {
                let validation = validateWithRulesAndReturnMessages(
                  data.changed,
                  "knownAs",
                  this.labelList[232].Field_Alias
                );
                if (validation.length) {
                  errorMessage = validation;
                  break;
                }
              }
            }
            break;
          }

          case "Hobbies": {
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "hobbies",
                "Hobbies"
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          }

          case this.labelList[296].Field_Alias: {
            if (this.labelList[296].Mandatory === "Yes" && !data.changed) {
              errorMessage = `${this.labelList[296].Field_Alias} is required`;
              break;
            }
            if (!data.extendValidation.Religion.includes(data.changed)) {
              errorMessage = `${this.labelList[296].Field_Alias} is Invalid`;
              break;
            }
            break;
          }

          case "Other Religion": {
            if (
              row[this.labelList[296].Field_Alias] === "Others" &&
              !data.changed
            ) {
              errorMessage = `Other Region is required`;
              break;
            }
            if (
              row[this.labelList[296].Field_Alias] === "Others" &&
              data.changed
            ) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "religion",
                this.labelList[296].Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          }

          case "Caste": {
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "caste",
                "Caste"
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          }

          case "Ethnic Race": {
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "ethnicRace",
                "Ethnic Race"
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          }

          case this.labelList[223].Field_Alias: {
            if (this.labelList[223].Mandatory === "Yes" && !data.changed) {
              errorMessage = `${this.labelList[223].Field_Alias} is required`;
              break;
            }
            break;
          }

          case this.labelList[224].Field_Alias: {
            if (this.labelList[224].Mandatory === "Yes" && !data.changed) {
              errorMessage = `${this.labelList[224].Field_Alias} is required`;
              break;
            }
            break;
          }

          case "Smoker As Of": {
            if (row["Smoker"] === "Yes" && !data.changed) {
              errorMessage = "Smoker As Of is required";
              break;
            }
            break;
          }

          case "Smoker":
          case "Disabled":
          case "Served in Military":
          case "Is Manager":
          case "Allow User Sign-In":
          case "Is Recruiter": {
            if (!data.changed) {
              errorMessage = `${data.type} is required`;
              break;
            }
            if (!data.extendValidation.Toggle_List.includes(data.changed)) {
              errorMessage = `Invalid ${data.type}`;
              break;
            }
            break;
          }

          case this.labelList["212"].Field_Alias: {
            if (!data.changed) {
              errorMessage = `${this.labelList["212"].Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "aadhar",
                this.labelList["212"].Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          }

          case "Role Access Rights": {
            if (!data.changed) {
              errorMessage = "Role Access Rights is required";
              break;
            }

            if (!data.extendValidation.Role.includes(data.changed)) {
              errorMessage = "Invalid Role Access Rights";
              break;
            }
            break;
          }

          case "Designation": {
            if (!data.changed) {
              errorMessage = "Designation is required";
              break;
            }

            if (!data.extendValidation.Designation.includes(data.changed)) {
              errorMessage = "Invalid Designation";
              break;
            }
            break;
          }

          case "Department": {
            if (!data.changed) {
              errorMessage = "Department is required";
              break;
            }

            if (!data.extendValidation.Department.includes(data.changed)) {
              errorMessage = "Invalid Department";
              break;
            }
            break;
          }

          case "Location": {
            if (!data.changed) {
              errorMessage = "Location is required";
              break;
            }

            if (!data.extendValidation.Location.includes(data.changed)) {
              errorMessage = "Invalid Location";
              break;
            }
            break;
          }

          case "Work Schedule": {
            if (!data.changed) {
              errorMessage = "Work Schedule is required";
              break;
            }

            if (!data.extendValidation.Work_Schedule.includes(data.changed)) {
              errorMessage = "Invalid Work Schedule";
              break;
            }
            break;
          }

          case "Employee Type": {
            if (!data.changed) {
              errorMessage = "Employee Type is required";
              break;
            }

            if (!data.extendValidation.Employee_Type.includes(data.changed)) {
              errorMessage = "Invalid Employee Type";
              break;
            }
            break;
          }

          case "Employee Profession": {
            if (!data.changed) {
              errorMessage = "Employee Profession is required";
              break;
            }

            if (!data.extendValidation.Profession.includes(data.changed)) {
              errorMessage = "Invalid Employee Profession";
              break;
            }
            break;
          }

          case "dateOfJoin":
            if (!data.changed) {
              errorMessage = "Date of Join is required";
              break;
            }
            // Additional validation logic for dateOfJoin, if any
            break;

          case "designation":
            if (!data.changed) {
              errorMessage = "Designation is required";
              break;
            }
            // Additional validation logic for designation, if any
            break;

          case "department":
            if (!data.changed) {
              errorMessage = "Department is required";
              break;
            }
            if (!optionValues.department.includes(data.changed)) {
              errorMessage = "Invalid Department";
              break;
            }
            break;

          case "employmentType":
            if (!data.changed) {
              errorMessage = "Employment Type is required";
              break;
            }
            if (!optionValues.employmentType.includes(data.changed)) {
              errorMessage = "Invalid Employment Type";
              break;
            }
            break;

          // case "contactInformation.mobileNumber":
          //   if (!data.changed) {
          //     errorMessage = "Mobile Number is required";
          //     break;
          //   }
          //   let phoneNumberValidation = validateWithRulesAndReturnMessages(
          //     data.changed,
          //     "phoneNumber",
          //     "Mobile Number"
          //   );
          //   if (phoneNumberValidation.length) {
          //     errorMessage = phoneNumberValidation;
          //     break;
          //   }
          //   break;

          // case "contactInformation.emergencyContactName":
          //   if (!data.changed) {
          //     errorMessage = "Emergency Contact Name is required";
          //     break;
          //   }
          //   let emergencyContactNameValidation =
          //     validateWithRulesAndReturnMessages(
          //       data.changed,
          //       "contactInfo",
          //       "Emergency Contact Name"
          //     );
          //   if (emergencyContactNameValidation.length) {
          //     errorMessage = emergencyContactNameValidation;
          //     break;
          //   }
          //   break;

          case "permanentAddress.apartmentName":
            if (!data.changed) {
              errorMessage = "Permanent Address - Apartment Name is required";
              break;
            }
            // Additional validation logic for apartmentName, if any
            break;

          case "permanentAddress.streetName":
            if (!data.changed) {
              errorMessage = "Permanent Address - Street Name is required";
              break;
            }
            // Additional validation logic for streetName, if any
            break;

          case "permanentAddress.cityMunicipality":
            if (!data.changed) {
              errorMessage =
                "Permanent Address - City/Municipality is required";
              break;
            }
            // Additional validation logic for cityMunicipality, if any
            break;

          case "permanentAddress.stateProvince":
            if (!data.changed) {
              errorMessage = "Permanent Address - State/Province is required";
              break;
            }
            // Additional validation logic for stateProvince, if any
            break;

          case "permanentAddress.country":
            if (!data.changed) {
              errorMessage = "Permanent Address - Country is required";
              break;
            }
            // Additional validation logic for country, if any
            break;

          case "permanentAddress.zipCode":
            if (!data.changed) {
              errorMessage = "Permanent Address - Zip Code is required";
              break;
            }
            // Additional validation logic for zipCode, if any
            break;

          // case "contactInformation.email":
          //   if (!data.changed) {
          //     errorMessage = "Email is required";
          //     break;
          //   }
          //   let emailValidation = validateWithRulesAndReturnMessages(
          //     data.changed,
          //     "email",
          //     "Email"
          //   );
          //   if (emailValidation.length) {
          //     errorMessage = emailValidation;
          //     break;
          //   }
          //   break;
          default:
            break;
        }
        return errorMessage;
      } else if (type == "locations") {
        switch (data.type) {
          case "Location Name":
            if (!data.changed) {
              errorMessage = "Location Name is required";
              break;
            }

            var locationName = validateWithRulesAndReturnMessages(
              data.changed,
              "locationStreet",
              "Location Name"
            );
            if (locationName.length) {
              errorMessage = locationName;
              break;
            }
            var location = data.extendValidation.location;
            if (location && location.includes(data.changed)) {
              errorMessage = "Location Name already exists";
              break;
            }
            var locationNameDuplicates = fullData.filter(
              (e) => e.Location === data.changed
            );
            if (locationNameDuplicates.length > 1) {
              errorMessage = "Location Name duplicated";
            }
            break;
          case "Location Type":
            var locationType = data.extendValidation.locationType;
            if (locationType && !locationType.includes(data.changed)) {
              errorMessage = "Invalid Location Type";
            }
            if (!data.changed) {
              errorMessage = "Location Type is required";
            }
            var locationTypeValidation = validateWithRulesAndReturnMessages(
              data.changed,
              "location",
              "Location Type"
            );
            if (locationTypeValidation.length) {
              errorMessage = locationTypeValidation;
            }
            break;
          case "Location Code":
            var locationCode = data.extendValidation.locationCode;
            if (locationCode && !locationCode.includes(data.changed)) {
              errorMessage = "Invalid Location Code";
            }
            if (!data.changed) {
              errorMessage = "Location Code is required";
              break;
            }
            var locationCodeDuplicates = fullData.filter(
              (e) => e.Location === data.changed
            );
            if (locationCodeDuplicates.length > 1) {
              errorMessage = "Location Code duplicated";
            }
            break;
          case "Street1":
            var Street1 = data.extendValidation.Street1;
            if (Street1 && !Street1.includes(data.changed)) {
              errorMessage = "Invalid Street1";
            }
            if (!data.changed) {
              errorMessage = "Street1 is required";
            }
            var street1Validation = validateWithRulesAndReturnMessages(
              data.changed,
              "locationStreet",
              "Street1"
            );
            if (street1Validation.length) {
              errorMessage = street1Validation;
            }
            break;
          case "Country":
            var CountryCode = data.extendValidation.CountryCode;
            if (CountryCode && !CountryCode.includes(data.changed)) {
              errorMessage = "Invalid Country";
            }
            if (!data.changed) {
              errorMessage = "Country is required";
            }
            var countryValidation = validateWithRulesAndReturnMessages(
              data.changed,
              "location",
              "Country"
            );
            if (countryValidation.length) {
              errorMessage = countryValidation;
            }
            break;
          case "State":
            var StateId = data.extendValidation.StateId;
            if (StateId && !StateId.includes(data.changed)) {
              errorMessage = "Invalid State";
            }
            if (!data.changed) {
              errorMessage = "State is required";
            }
            var stateValidation = validateWithRulesAndReturnMessages(
              data.changed,
              "location",
              "State"
            );
            if (stateValidation.length) {
              errorMessage = stateValidation;
            }
            break;
          case "City":
            var CityId = data.extendValidation.CityId;
            if (CityId && !CityId.includes(data.changed)) {
              errorMessage = "Invalid City";
            }
            if (!data.changed) {
              errorMessage = "City is required";
            }
            var cityValidation = validateWithRulesAndReturnMessages(
              data.changed,
              "location",
              "City"
            );
            if (cityValidation.length) {
              errorMessage = cityValidation;
            }
            break;
          case "PinCode":
            if (!data.changed) {
              errorMessage = "PinCode is required";
              break;
            }
            var PincodeList = data.extendValidation.Pincode;
            if (PincodeList && !PincodeList.includes(data.changed)) {
              errorMessage = "Invalid PinCode";
            }
            var PincodeValidation = validateWithRulesAndReturnMessages(
              data.changed,
              "pinCode",
              "PinCode"
            );
            if (PincodeValidation.length) {
              errorMessage = PincodeValidation;
            }
            break;
          case "TimeZone":
            var TimeZoneId = data.extendValidation.TimeZoneId;
            if (TimeZoneId && !TimeZoneId.includes(data.changed)) {
              errorMessage = "Invalid TimeZone";
            }
            if (!data.changed) {
              errorMessage = "TimeZone is required";
            }
            break;
          case "Contact Number":
            if (data.changed) {
              var PhoneNumberValidation = validateWithRulesAndReturnMessages(
                data.changed,
                "workNo",
                "ContactNumber"
              );
              if (PhoneNumberValidation.length) {
                errorMessage = PhoneNumberValidation;
              }
            }
            break;
          case "Status":
            var LocationStatus = data.extendValidation.LocationStatus;
            if (LocationStatus && !LocationStatus.includes(data.changed)) {
              errorMessage = "Invalid Status";
            }
            if (!data.changed) {
              errorMessage = "Status is required";
            }
            break;
          case "Description":
            var descriptionValidation = validateWithRulesAndReturnMessages(
              data.changed,
              "description",
              "Description"
            );
            if (descriptionValidation.length) {
              errorMessage = descriptionValidation;
            }
            break;
          default:
            break;
        }
        return errorMessage; // Return the error message
      } else if (type === "employeeGrade") {
        switch (data.type) {
          case "Employee Grade":
            if (!data.changed) {
              errorMessage = "Employee Grade is required";
              break;
            }
            var employeeGradeValidation = validateWithRulesAndReturnMessages(
              data.changed,
              "employeeGrade",
              "Employee Grade"
            );
            if (employeeGradeValidation.length) {
              errorMessage = employeeGradeValidation;
              break;
            }
            break;
          case "Grade Code":
            if (!data.changed) {
              errorMessage = "Grade Code is required";
              break;
            }
            var gradeCodeValidation = validateWithRulesAndReturnMessages(
              data.changed,
              "gradeCode",
              "Grade Code"
            );
            if (gradeCodeValidation.length) {
              errorMessage = gradeCodeValidation;
              break;
            }
            break;
          case "Parent Grade":
            var parentGrade = data.extendValidation.ParentGrade;
            if (parentGrade && !parentGrade.includes(data.changed)) {
              errorMessage = "Invalid Parent Grade";
            }
            if (!data.changed) {
              errorMessage = "Parent Grade is required";
            }
            break;
          case "Overtime Eligibility":
            var overtimeEligibility = data.extendValidation.OvertimeEligibility;
            if (
              overtimeEligibility &&
              !overtimeEligibility.includes(data.changed)
            ) {
              errorMessage = "Invalid Overtime Eligibility";
            }
            if (!data.changed) {
              errorMessage = "Overtime Eligibility is required";
            }
            break;
          case "Overtime Allocation":
            var overtimeAllocation = data.extendValidation.OvertimeAllocation;
            if (
              overtimeAllocation &&
              !overtimeAllocation.includes(data.changed)
            ) {
              errorMessage = "Invalid Overtime Allocation";
            }
            if (!data.changed) {
              errorMessage = "Overtime Allocation is required";
            }
            break;
          case "Description":
            var descriptionGradeValidation = validateWithRulesAndReturnMessages(
              data.changed,
              "description",
              "Description"
            );
            if (descriptionGradeValidation.length) {
              errorMessage = descriptionGradeValidation;
            }
            break;
        }
        return errorMessage; // Return the error message
      } else if (type.toLowerCase() == "employeeType") {
        switch (data.type) {
          case "Employee Type":
            if (!data.changed) {
              errorMessage = "Employee Type is required";
              break;
            }
            var employeeTypeValidation = validateWithRulesAndReturnMessages(
              data.changed,
              "employeeType",
              "Employee Type"
            );
            if (employeeTypeValidation.length) {
              errorMessage = employeeTypeValidation;
              break;
            }
            break;
          case "Work Schedule":
            var workSchedule = data.extendValidation.WorkSchedule;
            if (workSchedule && !workSchedule.includes(data.changed)) {
              errorMessage = "Invalid Work Schedule";
            }
            if (!data.changed) {
              errorMessage = "Work Schedule is required";
            }
            break;
          case "Eligible For Benefits":
            if (!data.changed) {
              errorMessage = "Eligible For Benefits is required";
            }
            break;
          case "Holiday Eligibility":
            if (!data.changed) {
              errorMessage = "Holiday Eligibility is required";
            }
            break;
          case "Salary Calculation Days":
            var salaryCalculationDays =
              data.extendValidation.SalaryCalculationDays;
            if (
              salaryCalculationDays &&
              !salaryCalculationDays.includes(data.changed)
            ) {
              errorMessage = "Invalid Salary Calculation Days";
            }
            if (!data.changed) {
              errorMessage = "Salary Calculation Days is required";
            }
            break;
          case "Comp Off Calculation Days":
            var compOffCalculationDays =
              data.extendValidation.CompOffCalculationDays;
            if (
              compOffCalculationDays &&
              !compOffCalculationDays.includes(data.changed)
            ) {
              errorMessage = "Invalid Comp Off Calculation Days";
            }
            if (!data.changed) {
              errorMessage = "Comp Off Calculation Days is required";
            }
            break;
          case "Display Duration In Hours And Minutes":
            if (!data.changed) {
              errorMessage =
                "Display Duration In Hours And Minutes is required";
            }
            break;
          case "Exclude Break Hours":
            if (!data.changed) {
              errorMessage = "Exclude Break Hours is required";
            }
            break;
          case "Processed Biometric Attendance Record Status":
            var biometricStatus =
              data.extendValidation.ProcessedBiometricAttendanceRecordStatus;
            if (biometricStatus && !biometricStatus.includes(data.changed)) {
              errorMessage =
                "Invalid Processed Biometric Attendance Record Status";
            }
            if (!data.changed) {
              errorMessage =
                "Processed Biometric Attendance Record Status is required";
            }
            break;
          case "Enable Work Place":
            if (!data.changed) {
              errorMessage = "Enable Work Place is required";
            }
            break;
          case "Approve Dashboard Attendance":
            var approveAttendance =
              data.extendValidation.ApproveDashboardAttendance;
            if (
              approveAttendance &&
              !approveAttendance.includes(data.changed)
            ) {
              errorMessage = "Invalid Approve Dashboard Attendance";
            }
            if (!data.changed) {
              errorMessage = "Approve Dashboard Attendance is required";
            }
            break;
          case "Work Place":
            var workPlace = data.extendValidation.WorkPlace;
            if (workPlace && !workPlace.includes(data.changed)) {
              errorMessage = "Invalid Work Place";
            }
            if (!data.changed) {
              errorMessage = "Work Place is required";
            }
            break;
          case "Status":
            var status = data.extendValidation.Status;
            if (status && !status.includes(data.changed)) {
              errorMessage = "Invalid Status";
            }
            if (!data.changed) {
              errorMessage = "Status is required";
            }
            break;
          case "Description":
            var descriptionTypeValidation = validateWithRulesAndReturnMessages(
              data.changed,
              "description",
              "Description"
            );
            if (descriptionTypeValidation.length) {
              errorMessage = descriptionTypeValidation;
            }
            break;
          default:
            break;
        }
        return errorMessage; // Return the error message
      } else if (type.toLowerCase() == "job-candidates" && this.labelList) {
        switch (data.type) {
          case "First_Name":
            if (!data.changed) {
              errorMessage = "First Name is required";
              break;
            } else {
              if (data.changed) {
                let validation = validateWithRulesAndReturnMessages(
                  data.changed,
                  "empFirstName",
                  "First Name"
                );
                if (validation.length) {
                  errorMessage = validation;
                  break;
                }
              }
            }
            // Additional validation logic for firstName, if any
            break;
          case "Middle_Name":
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "empMiddleName",
                "Middle Name"
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          case "Last_Name":
            if (!data.changed) {
              errorMessage = "Last Name is required";
              break;
            } else {
              if (data.changed) {
                let validation = validateWithRulesAndReturnMessages(
                  data.changed,
                  "empLastName",
                  "Last Name"
                );
                if (validation.length) {
                  errorMessage = validation;
                  break;
                }
              }
            }
            // Additional validation logic for lastName, if any
            break;
          // Nick Name fields
          case "Emp_Pref_First_Name": {
            if (
              this.labelList[328]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[328]?.Field_Alias} is required`;
              break;
            } else {
              if (data.changed) {
                let validation = validateWithRulesAndReturnMessages(
                  data.changed,
                  "knownAs",
                  `${this.labelList[328]?.Field_Alias}`
                );
                if (validation.length) {
                  errorMessage = validation;
                  break;
                }
              }
            }
            break;
          }
          case "Date_of_Birth":
            if (!data.changed) {
              errorMessage = "Date ofBirth is required";
              break;
            }
            if (data.changed) {
              if (!this.formatDate(data.changed)) {
                errorMessage = "Invalid Date of Birth";
                break;
              }
            }
            // Additional validation logic for dateOfBirth, if any
            break;
          //suffix fields
          case "Suffix": {
            if (
              this.labelList[268]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[268]?.Field_Alias} is required`;
              break;
            }
            break;
          }
          //gender fields
          case "Gender": {
            if (
              this.labelList[267]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[267]?.Field_Alias} is required`;
              break;
            }
            break;
          }
          //gender Orientation fields
          case "Gender_Orientation":
            if (
              this.labelList[210]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[210]?.Field_Alias} is required`;
              break;
            }
            break;
          //gender expresion fields
          case "Gender_Expression ":
            if (
              this.labelList[330]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[330]?.Field_Alias} is required`;
              break;
            }
            break;
          //gender identity fields
          case "Gender_Identity ":
            if (
              this.labelList[329]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[329]?.Field_Alias} is required`;
              break;
            }
            break;
          //gender pronoun fields
          case "gender_pronoun ":
            if (
              this.labelList[309]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[309]?.Field_Alias} is required`;
              break;
            }
            break;
          //Languages  Known fields
          case "Languages_Known":
            if (
              this.labelList[325]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[325]?.Field_Alias} is required`;
              break;
            }
            break;
          //Blood Group fields
          case "Blood_Group":
            if (
              this.labelList[262]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[262]?.Field_Alias} is required`;
              break;
            }
            break;
          // New Languages  Known fields
          case "Lang_Id":
            if (
              this.labelList[383]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[383]?.Field_Alias} is required`;
              break;
            }
            break;
          // Languages  Spoken fields
          case "langSpoken":
            if (
              this.labelList[356]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[356]?.Field_Alias} is required`;
              break;
            }
            break;
          // Languages  Read/Write fields
          case "langReadWrite":
            if (
              this.labelList[357]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[357]?.Field_Alias} is required`;
              break;
            }
            break;
          // Languages  Proficiency fields
          case "langProficiency":
            if (
              this.labelList[358]?.Mandatory_Field == "Yes" &&
              this.labelList[358]?.Predefined === "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[358]?.Field_Alias} is required`;
              break;
            } else {
              if (
                !data.changed &&
                this.labelList[358]?.Mandatory_Field == "Yes"
              ) {
                errorMessage = `${this.labelList[358]?.Field_Alias} is required`;
                break;
              } else if (data.changed) {
                let validation = validateWithRulesAndReturnMessages(
                  data.changed,
                  "skillName",
                  this.labelList[358]?.Field_Alias
                );
                if (validation.length) {
                  errorMessage = validation;
                  break;
                }
              }
            }
            break;
          //Marital Status fields
          case "Marital_Status":
            if (
              this.labelList[270]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[270]?.Field_Alias} is required`;
              break;
            }
            break;
          //Nationality fields
          case "Nationality":
            if (
              this.labelList[271]?.Mandatory_Field == "Yes" &&
              this.labelList[271]?.Predefined === "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[271]?.Field_Alias} is required`;
              break;
            } else {
              if (
                !data.changed &&
                this.labelList[271]?.Mandatory_Field == "Yes"
              ) {
                errorMessage = `${this.labelList[271]?.Field_Alias} is required`;
                break;
              } else if (data.changed) {
                let validation = validateWithRulesAndReturnMessages(
                  data.changed,
                  "nationality",
                  this.labelList[271]?.Field_Alias
                );
                if (validation.length) {
                  errorMessage = validation;
                  break;
                }
              }
            }
            break;
          //Mother name fields
          case "Mother_s_Name":
            if (
              this.labelList[263]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[263]?.Field_Alias} is required`;
              break;
            } else if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "empLastName",
                this.labelList[263]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          // Father name fields
          case "Father_s_Name":
            if (
              this.labelList[264]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[264]?.Field_Alias} is required`;
              break;
            } else if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "empLastName",
                this.labelList[264]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          //National identity number fields
          case "National_Identity_Number_Aadhar_Social_Security":
            if (
              this.labelList[220]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[220]?.Field_Alias} is required`;
              break;
            } else if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "aadhar",
                this.labelList[220]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          //Work permit fields
          case "Work_Permit":
            if (
              this.labelList[265]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[265]?.Field_Alias} is required`;
              break;
            }
            break;
          //other Work permit fields
          case "Other_Work_Permit":
            if (
              this.labelList[266]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[266]?.Field_Alias} is required`;
              break;
            } else if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "empLastName",
                this.labelList[266]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          //Source application fields
          case "Source_of_Application":
            if (
              this.labelList[287]?.Mandatory_Field == "Yes" &&
              this.labelList[287]?.Predefined === "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[287]?.Field_Alias} is required`;
              break;
            } else {
              if (
                !data.changed &&
                this.labelList[287]?.Mandatory_Field == "Yes"
              ) {
                errorMessage = `${this.labelList[287]?.Field_Alias} is required`;
                break;
              } else if (data.changed) {
                let validation = validateWithRulesAndReturnMessages(
                  data.changed,
                  "empLastName",
                  this.labelList[287]?.Field_Alias
                );
                if (validation.length) {
                  errorMessage = validation;
                  break;
                }
              }
            }
            break;
          //Physically Chalenged fields
          case "Physically_Challenged":
            if (
              this.labelList[331]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[331]?.Field_Alias} is required`;
              break;
            }
            break;
          //Job Title fields
          case "Job_Title":
            if (!data.changed) {
              errorMessage = "Job Title is required";
              break;
            }
            // Additional validation logic for job Title, if any
            break;
          // preferred location fields
          case "Preferred_Location":
            if (
              this.labelList[290]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[290]?.Field_Alias} is required`;
              break;
            }
            break;
          //Key Skills fields
          case "Keys_Skills":
            if (!data.changed) {
              errorMessage = "Key Skills is required";
              break;
            } else if (data.changed) {
              let validation = alphaNumSpaceNewLineWithElevenSymbolValidation(
                data.changed
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            // Additional validation logic for Key Skills, if any
            break;
          //Current Employer fields
          case "Current_Employer":
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "currentEmployer",
                "Current Employer"
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            // Additional validation logic for Current Employer, if any
            break;
          // notice period fields
          case "Availablity_To_Join":
            if (
              this.labelList[227]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[227]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = numericRequiredValidation(
                data.changed,
                this.labelList[227].Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          // Total Experience (In years) fields
          case "Total_Experience_In_years":
            if (!data.changed) {
              errorMessage = "Total Experience (In years) is required";
              break;
            } else if (data.changed) {
              let validation = numericRequiredValidation(
                "Total Experience (In years)",
                data.changed
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          // Total Experience (In months) fields
          case "Total_Experience_In_months":
            if (!data.changed) {
              errorMessage = "Total Experience (In months) is required";
              break;
            } else if (data.changed) {
              let validation = numericRequiredValidation(
                "Total Experience (In months)",
                data.changed
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          case "Current_Basic_Salary":
            if (!data.changed) {
              errorMessage = "Current Basic Salary is required";
              break;
            } else if (data.changed) {
              let validation = numericRequiredValidation(
                "Current Basic Salary",
                data.changed
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          case "Expected_Basic_Salary":
            if (!data.changed) {
              errorMessage = "Expected Basic Salary is required";
              break;
            } else if (data.changed) {
              let validation = numericRequiredValidation(
                "Expected Basic Salary",
                data.changed
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          // currency fields
          case "Currency":
            if (
              this.labelList[289]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[289]?.Field_Alias} is required`;
              break;
            }
            break;
          // passportNo fields
          case "Passport_Number":
            if (
              this.labelList[269]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[269]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "passportNo",
                this.labelList[269]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          // Name of Character Reference fields
          case "Name_Of_Character_Reference":
            if (
              this.labelList[322]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[322]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "empLastName",
                this.labelList[322]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          // Character Reference Mobile Number fields
          case "Character_Reference_Mobile_Number":
            if (
              this.labelList[323]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[323]?.Field_Alias} is required`;
              break;
            }
            break;
          // Character Reference Email fields
          case "Character_Reference_Email":
            if (
              this.labelList[324]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[324]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "empEmail",
                this.labelList[324].Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          // Street 1 fields
          case "Street_One":
            if (!data.changed) {
              errorMessage = `Street 1 is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "street1",
                "Street 1"
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          // Street 2 fields
          case "Street":
            // It's not a mandatory field
            // if (!data.changed) {
            //   errorMessage = `Street 2 is required`;
            //   break;
            // }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "street",
                "Street 2"
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          //City fields
          case "City_Municipality":
            if (
              this.labelList[143]?.Mandatory_Field == "Yes" &&
              this.labelList[143]?.Predefined === "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[143]?.Field_Alias} is required`;
              break;
            } else {
              if (
                !data.changed &&
                this.labelList[143]?.Mandatory_Field == "Yes"
              ) {
                errorMessage = `${this.labelList[143]?.Field_Alias} is required`;
                break;
              } else if (data.changed) {
                let validation = validateWithRulesAndReturnMessages(
                  data.changed,
                  "city",
                  this.labelList[143]?.Field_Alias
                );
                if (validation.length) {
                  errorMessage = validation;
                  break;
                }
              }
            }
            break;

          //Barangay fields
          case "pBarangay":
            if (
              this.labelList[332]?.Mandatory_Field == "Yes" &&
              this.labelList[332]?.Predefined === "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[332]?.Field_Alias} is required`;
              break;
            } else {
              if (
                !data.changed &&
                this.labelList[332]?.Mandatory_Field == "Yes"
              ) {
                errorMessage = `${this.labelList[332]?.Field_Alias} is required`;
                break;
              } else if (data.changed) {
                let validation = validateWithRulesAndReturnMessages(
                  data.changed,
                  "barangay",
                  this.labelList[332]?.Field_Alias
                );
                if (validation.length) {
                  errorMessage = validation;
                  break;
                }
              }
            }
            break;
          //Region fields
          case "pRegion":
            if (
              this.labelList[333]?.Mandatory_Field == "Yes" &&
              this.labelList[333]?.Predefined === "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[333]?.Field_Alias} is required`;
              break;
            } else {
              if (
                !data.changed &&
                this.labelList[333]?.Mandatory_Field == "Yes"
              ) {
                errorMessage = `${this.labelList[333]?.Field_Alias} is required`;
                break;
              } else if (data.changed) {
                let validation = validateWithRulesAndReturnMessages(
                  data.changed,
                  "region",
                  this.labelList[333]?.Field_Alias
                );
                if (validation.length) {
                  errorMessage = validation;
                  break;
                }
              }
            }
            break;
          // State/Province fields
          case "State_Province":
            if (!data.changed) {
              errorMessage = `State/Province is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "state",
                "State/Province"
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          // Country fields
          case "Country":
            if (!data.changed) {
              errorMessage = `Country is required`;
              break;
            }
            break;
          // Pin Code fields
          case "Pincode":
            if (
              this.labelList[150]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[150]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "pinCode",
                this.labelList[150]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          // Mobile Number Country Code fields
          case "Mobile_Number_Country_Code":
            if (!data.changed) {
              errorMessage = `Mobile Number Country Code is required`;
              break;
            }
            break;
          // Mobile Number fields
          case "Mobile_Number":
            if (!data.changed) {
              errorMessage = `Mobile Number is required`;
              break;
            }
            break;
          // Personal Email fields
          case "Personal_Email":
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "empEmail",
                "Personal Email"
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          // Emergency Contact Name fields
          case "Emergency_Contact_Name":
            if (
              this.labelList[334]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[334]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "empFirstName",
                this.labelList[334]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          // Emergency Contact Number fields
          case "Fax_No":
            if (
              this.labelList[335]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[335]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "landLineNo",
                this.labelList[335]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          // Emergency Contact Relation fields
          case "Emergency_Contact_Relation":
            if (
              this.labelList[359]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[359]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "awardReceivedFrom",
                this.labelList[359]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          //companyName
          case `Experience_Company`:
            if (
              this.labelList[279]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[279]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "companyName",
                this.labelList[279]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          //company Location
          case `Prev_Company_Location`:
            if (
              this.labelList[336]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[336]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "companyLocation",
                this.labelList[336]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          //designation
          case `Experience_Designation`:
            if (
              this.labelList[280]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[280]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "designation",
                this.labelList[280]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          // experienceStart
          case `Experience_From`:
            if (
              this.labelList[281]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[281]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              if (!this.formatDate(data.changed)) {
                errorMessage = `Invalid Experience ${this.labelList[281]?.Field_Alias}`;
                break;
              }
            }
            break;
          // experienceEnd
          case `Experience_To`:
            if (
              this.labelList[282]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[282]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              if (!this.formatDate(data.changed)) {
                errorMessage = `Invalid Experience ${this.labelList[282]?.Field_Alias}`;
                break;
              }
            }
            break;
          //Experience Reference Name
          case `Reference_Name`:
            if (
              this.labelList[360]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[360]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "skillName",
                this.labelList[360]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          //Experience Reference Eamil
          case `Reference_Email`:
            if (
              this.labelList[361]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[361]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "empEmail",
                this.labelList[361]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          //Experience Reference Number
          case `Reference_Number`:
            if (
              this.labelList[362]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[362]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "emergencyContactNo",
                this.labelList[362]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          //Certification certificationName
          case `Certification_Certificate`:
            if (
              this.labelList[284]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[284]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "certificationName",
                this.labelList[284]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          //Certification certificateReceivedOn
          case `Certification_Received_On`:
            if (
              this.labelList[285]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[285]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              if (!this.formatDate(data.changed)) {
                errorMessage = `Invalid Certification ${this.labelList[285]?.Field_Alias}`;
                break;
              }
            }
            break;
          //Certification receivedFrom
          case `Certification_Received_From`:
            if (
              this.labelList[286]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[286]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "receivedFrom",
                this.labelList[286]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          //Certification Ranking
          case `ranking`:
            if (
              this.labelList[337]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[337]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "skillCategory",
                this.labelList[337]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
          //Education receivedFrom
          case `Education_Educational_Attainment`:
            if (
              this.labelList[128]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[128]?.Field_Alias} is required`;
              break;
            }
            break;
          //Education Institution
          case `Education_Institute_Name`:
            if (
              this.labelList[175]?.Mandatory_Field == "Yes" &&
              this.labelList[175]?.Predefined === "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[175]?.Field_Alias} is required`;
              break;
            } else {
              if (
                !data.changed &&
                this.labelList[175]?.Mandatory_Field == "Yes"
              ) {
                errorMessage = `${this.labelList[175]?.Field_Alias} is required`;
                break;
              } else if (data.changed) {
                let validation = validateWithRulesAndReturnMessages(
                  data.changed,
                  "instituteName",
                  this.labelList[175]?.Field_Alias
                );
                if (validation.length) {
                  errorMessage = validation;
                  break;
                }
              }
            }
            break;
          //Education specialisation
          case `Education_Field_of_Study`:
            if (
              this.labelList[174]?.Mandatory_Field == "Yes" &&
              this.labelList[174]?.Predefined === "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[174]?.Field_Alias} is required`;
              break;
            } else {
              if (
                !data.changed &&
                this.labelList[174]?.Mandatory_Field == "Yes"
              ) {
                errorMessage = `${this.labelList[174]?.Field_Alias} is required`;
                break;
              } else if (data.changed) {
                let validation = validateWithRulesAndReturnMessages(
                  data.changed,
                  "specialisation",
                  this.labelList[174]?.Field_Alias
                );
                if (validation.length) {
                  errorMessage = validation;
                  break;
                }
              }
            }
            break;
          //Education Year_Of_Passing
          case `Education_Year_of_Passing`:
            if (
              this.labelList[179]?.Mandatory_Field == "Yes" &&
              this.labelList[179]?.Predefined === "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[179]?.Field_Alias} is required`;
              break;
            }
            break;
          //Education Education_Year_of_Start
          case `Education_Year_of_Start`:
            // It's not a mandatory field
            // if (!data.changed) {
            //   errorMessage = `Year of Start is required`;
            //   break;
            // }
            break;
          //Education percentage
          case `Education_Percentage`:
            if (
              this.labelList[177]?.Mandatory_Field == "Yes" &&
              !data.changed
            ) {
              errorMessage = `${this.labelList[177]?.Field_Alias} is required`;
              break;
            }
            if (data.changed) {
              let validation = validateWithRulesAndReturnMessages(
                data.changed,
                "percentage",
                this.labelList[177]?.Field_Alias
              );
              if (validation.length) {
                errorMessage = validation;
                break;
              }
            }
            break;
        }
        return errorMessage;
      }
    },
    extendedValidation(validate, typeOfImport, editorData, record) {
      if (typeOfImport == "taxRelief" && validate.type === "Amount") {
        let taxCategory = record["Tax Relief Category"];
        for (let i = 0; i < editorData.length; i++) {
          if (editorData[i]["Tax Relief Category"] === taxCategory) {
            this.setFieldError("", editorData[i], { name: "Amount" });
          }
        }
      }
    },
    capitalizeFirstLetter(str) {
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    validateField(data, minValue, maxValue, action) {
      let errorMessage = "";

      if (!data.changed && action == "Required") {
        errorMessage = `${data.type} is required`;
      } else if (isNaN(data.changed)) {
        errorMessage = "Only numerics are allowed";
      } else if (data.changed < minValue) {
        errorMessage = `Please enter a value greater than or equal to ${minValue}.`;
      } else if (data.changed > maxValue) {
        errorMessage = `Please enter a value less than or equal to ${maxValue}.`;
      }
      return errorMessage;
    },
  },

  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    formatDate() {
      return (date) => {
        return moment(date, "YYYY-MM-DD", true).isValid()
          ? moment(date).format("YYYY-MM-DD")
          : false;
      };
    },
  },
};
