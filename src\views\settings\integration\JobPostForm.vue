<template>
  <v-form class="ma-4" ref="jobPostForm" @submit.prevent="validateJobPostForm">
    <div>
      <v-card
        class="cardColor"
        style="overflow: visible; z-index: 1; margin-top: 20px"
      >
        <div class="ma-4">
          <div
            class="d-flex align-center pt-8 text-h6 text-grey-darken-1 font-weight-bold"
          >
            <v-progress-circular
              model-value="100"
              color="orange"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            Whats the job you are hiring for?
          </div>
          <v-row class="pt-2">
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[155]?.Field_Alias
                }}<span
                  v-if="labelList[155]?.Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>

              <CustomSelect
                v-if="labelList[155]?.Predefined === 'Yes'"
                :items="jobTitles"
                :itemSelected="jobTitle"
                ref="jobTitle"
                :rules="[
                  labelList[155]?.Mandatory_Field == 'Yes'
                    ? required(`${labelList[155]?.Field_Alias}`, jobTitle)
                    : true,
                ]"
                itemValue="Pos_Name"
                itemTitle="Pos_Name"
                @selected-item="jobTitle = $event"
                :isAutoComplete="true"
                :isLoading="isJobTitleListLoading"
                :disabled="isPositionRecordSelected"
                :noDataText="
                  isJobTitleListLoading ? 'Loading...' : 'No data available'
                "
              ></CustomSelect>
              <v-text-field
                v-else
                ref="jobTitleText"
                v-model="jobTitle"
                :counter="200"
                :rules="[
                  labelList[155]?.Mandatory_Field == 'Yes'
                    ? required('Job Title', jobTitle)
                    : true,
                  alphaNumSpaceWithThirteenSymbolValidation(jobTitle),
                  minLengthValidation('Job Title', jobTitle, 3),
                  maxLengthValidation('Job Title', jobTitle, 200),
                ]"
                variant="solo"
                :disabled="isPositionRecordSelected"
              >
              </v-text-field>
            </v-col>
            <v-col
              v-if="labelList[228]?.Field_Visiblity == 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[228].Field_Alias
                }}<span
                  v-if="labelList[228].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>

              <CustomSelect
                ref="priority"
                :items="dropdownPriority"
                :isAutoComplete="true"
                :rules="[
                  labelList[228].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[228].Field_Alias}`,
                        selectedPriority
                      )
                    : true,
                ]"
                variant="solo"
                clearable
                :itemSelected="selectedPriority"
                @selected-item="selectedPriority = $event"
              ></CustomSelect>
            </v-col>
            <v-col
              cols="12"
              md="4"
              sm="6"
              v-if="labelList[202]?.Field_Visiblity == 'Yes'"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[202].Field_Alias
                }}<span
                  v-if="labelList[202].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <CustomSelect
                :items="dropdownExperienceLevel"
                :isLoading="dropdownLoading"
                item-title="Experience_Level"
                item-value="Experience_Level"
                :isAutoComplete="true"
                :rules="[
                  labelList[202].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[202].Field_Alias}`,
                        selectedExperienceLevel
                      )
                    : true,
                ]"
                variant="solo"
                clearable
                :itemSelected="selectedExperienceLevel"
                @selected-item="selectedExperienceLevel = $event"
              ></CustomSelect> </v-col
            ><v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">Status</p>
              <CustomSelect
                ref="status"
                :items="dropDownStatus"
                item-title="Status"
                item-value="Id"
                :isAutoComplete="true"
                variant="solo"
                clearable
                :disabled="true"
                :isLoading="checkForDropDownLoading()"
                :itemSelected="selectedStatus"
                @selected-item="selectedStatus = $event"
              ></CustomSelect>
            </v-col>

            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                Posting Date<span style="color: red">*</span>
              </p>
              <v-menu
                v-model="postingDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="postingDate"
                    v-model="formattedPostingDate"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('Posting Date', formattedPostingDate)]"
                    readonly
                    :disabled="true"
                    v-bind="props"
                    variant="solo"
                  ></v-text-field>
                </template>
                <v-date-picker v-model="postingDate"></v-date-picker>
              </v-menu>
            </v-col>

            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                Closing Date<span style="color: red">*</span>
              </p>
              <v-menu
                v-model="closingDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="closingDate"
                    v-model="formattedClosingDate"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('Closing Date', formattedClosingDate)]"
                    readonly
                    v-bind="props"
                    variant="solo"
                  ></v-text-field>
                </template>
                <v-date-picker v-model="closingDate"></v-date-picker>
              </v-menu>
            </v-col>

            <v-col
              v-if="labelList[211]?.Field_Visiblity == 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[211].Field_Alias
                }}<span
                  v-if="labelList[211].Mandatory_Field.toLowerCase() == 'yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <v-menu
                v-model="joiningDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="expectedJoiningDate"
                    v-model="formattedJoiningDate"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="
                      labelList['211'].Mandatory_Field === 'Yes'
                        ? [
                            required(
                              labelList['211'].Field_Alias,
                              formattedJoiningDate
                            ),
                          ]
                        : [true]
                    "
                    readonly
                    v-bind="props"
                    variant="solo"
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="expectedJoiningDate"
                  :min="minDate"
                ></v-date-picker>
              </v-menu>
            </v-col>

            <v-col cols="12" md="12" sm="12">
              <p class="text-subtitle-1 text-grey-darken-1">
                Job Description<span style="color: red">*</span>
              </p>
            </v-col>
            <v-col cols="12" md="12" sm="12">
              <div>
                <div ref="editor" class="quill-editor" id="formEditor"></div>
              </div>
              <div
                v-if="descriptionErrorMsg"
                class="text-caption ml-4"
                style="color: #b00020"
              >
                {{ descriptionErrorMsg ? descriptionErrorMsg : "" }}
              </div>
            </v-col>
            <v-col v-if="fieldForce" cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ getCustomFieldName(115, "Service Provider")
                }}<span style="color: red">*</span>
              </p>
              <CustomSelect
                ref="serviceProvider"
                :items="dropDownServiceProvider"
                item-title="Service_Provider_Name"
                item-value="Service_Provider_Id"
                variant="solo"
                :isAutoComplete="true"
                :rules="[
                  required(
                    getCustomFieldName(115, 'Service Provider'),
                    selectedServiceProvider
                  ),
                ]"
                clearable
                :isLoading="checkForDropDownLoading()"
                :itemSelected="selectedServiceProvider"
                @selected-item="selectedServiceProvider = $event"
              >
              </CustomSelect>
            </v-col>
            <v-col v-if="fieldForce" cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[154]?.Field_Alias
                }}<span
                  v-if="labelList[154]?.Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <CustomSelect
                ref="orgGroup"
                :items="dropdownOrgGroup"
                item-title="organizationGroupFullName"
                item-value="organizationGroupId"
                :isLoading="isOrgGroupLoading"
                :rules="[
                  labelList[154]?.Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[154]?.Field_Alias}`,
                        selectedOrgGroup
                      )
                    : true,
                ]"
                :isAutoComplete="true"
                :select-properties="{
                  clearable: true,
                  closableChips: true,
                }"
                :disabled="disableCoreGroup"
                variant="solo"
                :itemSelected="selectedOrgGroup"
                @selected-item="selectedOrgGroup = $event"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="
                customGroupCoverage &&
                customGroupCoverage.toLowerCase() === 'custom group'
              "
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                Custom Group<span style="color: red">*</span>
              </p>
              <CustomSelect
                ref="customGroup"
                :items="dropdownCustomGroup"
                item-title="Custom_Group_Name"
                item-value="Custom_Group_Id"
                variant="solo"
                :isAutoComplete="true"
                :rules="[required('Custom Group', selectedCustomGroup)]"
                clearable
                :isLoading="isCustomGroupListLoading"
                :itemSelected="selectedCustomGroup"
                @selected-item="selectedCustomGroup = $event"
              >
              </CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[254] && labelList[254].Field_Visiblity === 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[254].Field_Alias
                }}<span
                  v-if="labelList[254].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <div class="tooltip-wrapper">
                <CustomSelect
                  :items="groupList"
                  :isAutoComplete="true"
                  :isLoading="groupListLoading"
                  :noDataText="
                    groupListLoading ? 'Loading...' : 'No data available'
                  "
                  :itemSelected="selectedGroupId"
                  itemValue="Organization_Structure_Id"
                  itemTitle="Pos_Name"
                  :disabled="isPositionRecordSelected"
                  :rules="[
                    labelList[254].Mandatory_Field == 'Yes'
                      ? required(labelList[254].Field_Alias, selectedGroupId)
                      : true,
                  ]"
                  ref="group"
                  @selected-item="selectedGroupId = $event"
                ></CustomSelect>

                <!-- Custom tooltip element -->
                <span v-if="selectedGroupId" class="custom-tooltip">{{
                  groupName
                }}</span>
              </div>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                Division/Department/Section<span style="color: red">*</span>
              </p>
              <CustomSelect
                ref="department"
                :items="dropdownFunctionalArea"
                item-title="Department_Name"
                item-value="Department_Id"
                variant="solo"
                :isAutoComplete="true"
                :rules="[required('Department', selectedFunctionalArea)]"
                clearable
                :disabled="disableDivisionField"
                :isLoading="checkForDropDownLoading()"
                :itemSelected="selectedFunctionalArea"
                @selected-item="selectedFunctionalArea = $event"
              >
              </CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[260] && labelList[260].Field_Visiblity === 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[260].Field_Alias
                }}<span
                  v-if="labelList[260].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <div class="tooltip-wrapper">
                <CustomSelect
                  ref="position"
                  :items="dropdownDesignation"
                  item-title="Designation_Name"
                  item-value="Designation_Id"
                  :isLoading="designationListLoading"
                  :rules="[
                    labelList[260].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[260].Field_Alias,
                          selectedDesignation
                        )
                      : true,
                  ]"
                  :no-data-text="noDataText"
                  placeholder="Type minimum 3 characters to list"
                  :isAutoComplete="true"
                  variant="solo"
                  clearable
                  :itemSelected="selectedDesignation"
                  :disabled="disableDesignation"
                  @update-search-value="callDesignationList($event)"
                  @selected-item="selectedDesignation = $event"
                ></CustomSelect>

                <!-- Custom tooltip element -->
                <span v-if="selectedDesignation" class="custom-tooltip">{{
                  designationName
                }}</span>
              </div>
            </v-col>

            <v-col
              v-if="labelList[261] && labelList[261].Field_Visiblity === 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[261].Field_Alias
                }}<span
                  v-if="labelList[261].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <CustomSelect
                ref="workSchedule"
                :items="dropdownWorkSchecule"
                item-title="Title"
                item-value="WorkSchedule_Id"
                :isLoading="checkForDropDownLoading()"
                variant="solo"
                :rules="[
                  labelList[261].Mandatory_Field == 'Yes'
                    ? required(labelList[261].Field_Alias, selectedWorkSchedule)
                    : true,
                ]"
                :isAutoComplete="true"
                clearable
                :itemSelected="selectedWorkSchedule"
                @selected-item="selectedWorkSchedule = $event"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[257] && labelList[257].Field_Visiblity === 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[257].Field_Alias
                }}<span
                  v-if="labelList[257].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <CustomSelect
                ref="jobLocation"
                :items="dropdownJobLocation"
                item-title="Location_Name"
                item-value="Location_Id"
                :isLoading="checkForDropDownLoading()"
                :isAutoComplete="true"
                variant="solo"
                :rules="[
                  labelList[257].Mandatory_Field == 'Yes'
                    ? required(labelList[257].Field_Alias, selectedJobLocation)
                    : true,
                ]"
                clearable
                :itemSelected="selectedJobLocation"
                @selected-item="onChangeLocation($event)"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[253] && labelList[253].Field_Visiblity === 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[253].Field_Alias
                }}<span
                  v-if="labelList[253].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <CustomSelect
                :items="cityList"
                :isAutoComplete="true"
                :isLoading="cityListLoading"
                :noDataText="
                  cityListLoading ? 'Loading...' : 'No data available'
                "
                :itemSelected="selectedCityId"
                itemValue="City_Id"
                itemTitle="City_Name"
                variant="solo"
                :rules="[
                  labelList[253].Mandatory_Field == 'Yes'
                    ? required(labelList[253].Field_Alias, selectedCityId)
                    : true,
                ]"
                ref="city"
                @selected-item="selectedCityId = $event"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[252] && labelList[252].Field_Visiblity === 'Yes'"
              cols="12"
              sm="12"
              lg="4"
              md="4"
              xl="4"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[252].Field_Alias
                }}<span
                  v-if="labelList[252].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <CustomSelect
                :items="stateList"
                :isAutoComplete="true"
                :isLoading="cityListLoading"
                :noDataText="
                  cityListLoading ? 'Loading...' : 'No data available'
                "
                :itemSelected="selectedStateId ? selectedStateId : ''"
                itemValue="State_Id"
                itemTitle="State_Name"
                :rules="[
                  labelList[252].Mandatory_Field == 'Yes'
                    ? required(labelList[252].Field_Alias, selectedStateId)
                    : true,
                ]"
                variant="solo"
                ref="state"
                @selected-item="selectedStateId = $event"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[204]?.Field_Visiblity == 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[204].Field_Alias
                }}<span
                  v-if="labelList[204].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <CustomSelect
                ref="jobLocationType"
                :items="dropdownJobLocationType"
                :isAutoComplete="true"
                variant="solo"
                :rules="[
                  labelList[204].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[204].Field_Alias}`,
                        selectedJobLocationType
                      )
                    : true,
                ]"
                clearable
                :itemSelected="selectedJobLocationType"
                @selected-item="selectedJobLocationType = $event"
              ></CustomSelect>
            </v-col>
            <v-col
              cols="12"
              md="4"
              sm="6"
              v-if="labelList[203]?.Field_Visiblity == 'Yes'"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[203].Field_Alias
                }}<span
                  v-if="labelList[203].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <CustomSelect
                ref="workplaceType"
                :items="workPlaceOptions"
                item-title="Description"
                item-value="Code"
                :isLoading="isWorkplaceTypeLoading"
                :isAutoComplete="true"
                variant="solo"
                :rules="[
                  labelList[203].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[203].Field_Alias}`,
                        selectedWorkplaceType
                      )
                    : true,
                ]"
                clearable
                :itemSelected="selectedWorkplaceType"
                @selected-item="selectedWorkplaceType = $event"
              ></CustomSelect>
            </v-col>
            <!-- <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">Company Address</p>
              <v-card>
                <vue-google-autocomplete
                  id="map1"
                  ref="googleval"
                  v-on:placechanged="setAddress"
                  classname="form-control pa-5 google-auto-complete-address-field"
                  placeholder="Type your address"
                  :enable-geolocation="true"
                  :country="['au', 'in']"
                >
                </vue-google-autocomplete>
              </v-card>
            </v-col> -->
            <v-col
              v-if="labelList[258] && labelList[258].Field_Visiblity === 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[258].Field_Alias
                }}<span
                  v-if="labelList[258].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <CustomSelect
                ref="empType"
                :items="dropdownJobType"
                :isLoading="checkForDropDownLoading()"
                item-title="Employee_Type"
                item-value="EmpType_Id"
                variant="solo"
                :rules="[
                  labelList[258].Mandatory_Field == 'Yes'
                    ? required(labelList[258].Field_Alias, selectedJobType)
                    : true,
                ]"
                :isAutoComplete="true"
                clearable
                :itemSelected="selectedJobType"
                :disabled="isPositionRecordSelected"
                @selected-item="selectedJobType = $event"
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                No of Vacancies<span style="color: red">*</span>
              </p>
              <v-text-field
                ref="noOfVacancies"
                v-model="noOfVacancies"
                :model-value="validateNoOfVacancies()"
                :rules="[
                  required('No of Vacancies', noOfVacancies),
                  minMaxNumberValidation(
                    'No of Vacancies',
                    noOfVacancies,
                    1,
                    500
                  ),
                ]"
                variant="solo"
                type="number"
              >
              </v-text-field>
            </v-col>
            <v-col
              v-if="mytoken && irukkaStatus == 'Active'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                No of Male Vacancies
                <span style="color: red">*</span>
              </p>
              <v-text-field
                ref="noOfMaleVacancies"
                v-model="noOfMaleVacancies"
                :model-value="validateNoOfMaleVacancies()"
                :rules="[
                  required('No of Male Vacancies', noOfMaleVacancies),
                  minMaxNumberValidation(
                    'No of Male Vacancies',
                    noOfMaleVacancies,
                    1,
                    noOfVacancies - 0
                  ),
                ]"
                variant="solo"
                type="number"
              >
              </v-text-field>
            </v-col>
            <v-col
              v-if="mytoken && irukkaStatus == 'Active'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                No of Female Vacancies
                <span style="color: red">*</span>
              </p>
              <v-text-field
                ref="noOfFemaleVacancies"
                v-model="noOfFemaleVacancies"
                :model-value="validateNoOfFemaleVacancies()"
                :rules="[
                  required('No of Female Vacancies', noOfFemaleVacancies),
                  minMaxNumberValidation(
                    'No of Female Vacancies',
                    noOfFemaleVacancies,
                    1,
                    noOfVacancies - noOfMaleVacancies
                  ),
                ]"
                variant="solo"
                type="number"
              >
              </v-text-field>
            </v-col>
          </v-row>
        </div>

        <div class="ma-4">
          <div
            class="d-flex pt-8 align-center text-h6 text-grey-darken-1 font-weight-bold"
          >
            <v-progress-circular
              model-value="100"
              color="primary"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            Job Requirements
          </div>
          <v-row class="pt-2">
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                Key Skills<span style="color: red">*</span>
                <v-tooltip
                  text="Eg.. Communication, Interpersonal etc..."
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      class="ml-1"
                      v-bind="props"
                      size="small"
                      color="blue"
                    >
                      fas fa-info-circle
                    </v-icon>
                  </template>
                </v-tooltip>
              </p>
              <v-text-field
                ref="keySkills"
                v-model="input"
                @update:modelValue="showAddIcon"
                variant="solo"
                :rules="[
                  alphaNumSpaceNewLineWithElevenSymbolValidation(input),
                  required('Key Skills', skillSet[0]),
                ]"
                @keydown.enter.prevent="addChip"
              >
                <template v-slot:default>
                  <v-icon v-if="showIcon" @click="addChip" size="x-small"
                    >fas fa-plus</v-icon
                  >
                  <v-chip
                    v-for="(chip, index) in skillSet"
                    append-icon="fas fa-times-circle"
                    :key="index"
                    class="ma-1"
                    @click="removeChip(index)"
                  >
                    {{ chip }}
                  </v-chip>
                </template></v-text-field
              >
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1 ml-3">
                Experience Range(In Years)<span style="color: red">*</span>
              </p>
              <v-range-slider
                v-model="rangeExperience"
                :max="40"
                :min="0"
                :step="1"
                strict
                thumb-size="15"
                track-size="2"
                color="orange"
                hide-details
                class="align-center"
              >
                <template v-slot:prepend>
                  <v-text-field
                    ref="expRangeStart"
                    v-model="rangeExperience[0]"
                    :model-value="findMinRangeExperience()"
                    hide-details
                    single-line
                    type="number"
                    variant="solo"
                    step="1"
                    thumb-label
                    style="width: 70px"
                  ></v-text-field>
                </template>
                <template v-slot:append>
                  <v-text-field
                    ref="expRangeEnd"
                    v-model="rangeExperience[1]"
                    :model-value="findMaxRangeExperience()"
                    hide-details
                    single-line
                    type="number"
                    variant="solo"
                    style="width: 70px"
                  ></v-text-field>
                </template>
              </v-range-slider>
              <div
                v-if="experienceRangeErrorMessage"
                class="text-caption ml-4"
                style="color: #b00020"
              >
                {{ experienceRangeErrorMessage }}
              </div>
            </v-col>
            <v-col
              v-if="labelList[288]?.Field_Visiblity == 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[288].Field_Alias
                }}<span
                  v-if="labelList[288].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>

              <CustomSelect
                ref="expectedQualification"
                :items="dropdownQualifications"
                item-title="Course_Name"
                item-value="Course_Id"
                :isLoading="isCourseListLoading"
                :isAutoComplete="true"
                :select-properties="{
                  multiple: true,
                  chips: true,
                  clearable: true,
                  closableChips: true,
                }"
                :itemSelected="selectedQualificationList"
                variant="solo"
                :rules="[
                  labelList[288].Mandatory_Field == 'Yes'
                    ? selectedQualificationList
                      ? required(
                          `${labelList[288].Field_Alias}`,
                          selectedQualificationList[0]
                        )
                      : required(
                          `${labelList[288].Field_Alias}`,
                          selectedQualificationList
                        )
                    : true,
                ]"
                @selected-item="selectedQualificationList = $event"
              ></CustomSelect>
            </v-col>
            <!-- <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                Expected Work Permits
                <span style="color: red">*</span>
              </p>
              <CustomSelect
              ref="workPermits"
                :items="dropdownWorkPermits"
                item-title="Work_Authorization_Name"
                item-value="Work_Authorization_Id"
                :isLoading="checkForDropDownLoading()"
                :isAutoComplete="true"
                :select-properties="{
                  multiple: true,
                  chips: true,
                  clearable: true,
                  closableChips: true,
                }"
                :rules="
                  selectedWorkPermits
                    ? [
                        required(
                          'Expected Work Permits',
                          selectedWorkPermits[0]
                        ),
                      ]
                    : [required('Expected Work Permits', selectedWorkPermits)]
                "
                variant="solo"
                :itemSelected="selectedWorkPermits"
                @selected-item="selectedWorkPermits = $event"
              ></CustomSelect>
            </v-col> -->
            <!-- <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                Other Work Permits
                <span v-if="!isOthersSelected" style="color: red">*</span>
              </p>
              <v-text-field
              ref="otherWorkPermit"
                v-model="otherWorkPermits"
                :rules="
                  !isOthersSelected
                    ? [
                        required('Other Work Permits', otherWorkPermits),
                        alphaNumSpaceWithFourSymbolValidation(
                          'Other Work Permits',
                          otherWorkPermits
                        ),
                      ]
                    : ''
                "
                :disabled="isOthersSelected"
                variant="solo"
              >
              </v-text-field>
            </v-col> -->
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                Rounds<span
                  v-if="showJobRoundsTab.toLowerCase() === 'no'"
                  style="color: red"
                  >*</span
                >
              </p>
              <CustomSelect
                ref="rounds"
                :items="dropdownRounds"
                item-title="Round_Name"
                item-value="Round_Id"
                :isLoading="checkForDropDownLoading()"
                :isAutoComplete="true"
                :select-properties="{
                  multiple: true,
                  chips: true,
                  clearable: true,
                  closableChips: true,
                }"
                variant="solo"
                :rules="
                  showJobRoundsTab.toLowerCase() === 'no'
                    ? selectedRounds
                      ? [required('Rounds', selectedRounds[0])]
                      : [required('Rounds', selectedRounds)]
                    : [true]
                "
                :itemSelected="selectedRounds"
                @selected-item="selectedRounds = $event"
              ></CustomSelect>
            </v-col>
            <!-- Job Requirements Field -->
            <v-col
              cols="12"
              md="4"
              sm="6"
              v-if="
                labelList[327] &&
                labelList[327].Field_Visiblity?.toLowerCase() === 'yes'
              "
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[327]?.Field_Alias
                }}<span
                  v-if="
                    labelList[327]?.Mandatory_Field?.toLowerCase() === 'yes'
                  "
                  style="color: red"
                  >*</span
                >
              </p>

              <CustomSelect
                ref="jobRequirements"
                v-model="selectedJobRequirements"
                :rules="[
                  labelList[327]?.Mandatory_Field?.toLowerCase() === 'yes'
                    ? required(
                        labelList[327]?.Field_Alias,
                        selectedJobRequirements
                      )
                    : true,
                ]"
                :items="listJobRequirements"
                variant="solo"
                :isAutoComplete="true"
                itemTitle="Required_Certification"
                itemValue="Required_Certification_Id"
                clearable
                :isLoading="dropdownLoading"
                :item-selected="selectedJobRequirements"
                @selected-item="selectedJobRequirements = $event"
              />
            </v-col>
          </v-row>
        </div>
        <div class="ma-4">
          <div
            class="d-flex pt-8 align-center text-h6 text-grey-darken-1 font-weight-bold"
          >
            <v-progress-circular
              model-value="100"
              color="light-blue-darken-1"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            Benefits & Experience
          </div>
          <v-row class="pt-2">
            <v-col
              v-if="labelList[289]?.Field_Visiblity == 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[289].Field_Alias
                }}<span
                  v-if="labelList[289].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <CustomSelect
                ref="currency"
                :isAutoComplete="true"
                :items="dropdownCurrencey"
                item-title="Currency_Name"
                item-value="Currency_Id"
                :isLoading="checkForDropDownLoading()"
                variant="solo"
                clearable
                :itemSelected="selectedCurrencey"
                :rules="[
                  labelList[289].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[289].Field_Alias}`,
                        selectedCurrencey
                      )
                    : true,
                ]"
                @selected-item="selectedCurrencey = $event"
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1 ml-3">
                Salary Range<span style="color: red">*</span>
              </p>
              <v-range-slider
                v-model="range"
                :max="99999999"
                :min="0"
                :step="1"
                strict
                thumb-size="15"
                track-size="2"
                color="orange"
                hide-details
                class="align-center"
              >
                <template v-slot:prepend>
                  <v-text-field
                    ref="salaryStart"
                    v-model="range[0]"
                    :model-value="findMinRangeSalary()"
                    hide-details
                    single-line
                    variant="solo"
                    style="width: 100px"
                    type="number"
                  ></v-text-field>
                </template>
                <template v-slot:append>
                  <v-text-field
                    ref="salaryEnd"
                    v-model="range[1]"
                    :model-value="findMaxRangeSalary()"
                    hide-details
                    single-line
                    variant="solo"
                    type="number"
                    style="width: 130px"
                  ></v-text-field>
                </template>
              </v-range-slider>
              <div
                v-if="salaryRangeErrorMessage"
                class="text-caption ml-4"
                style="color: #b00020"
              >
                {{ salaryRangeErrorMessage }}
              </div>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                Pay Type<span style="color: red">*</span>
              </p>
              <CustomSelect
                ref="payType"
                :items="payTypeList"
                item-title="Pay_Type_Name"
                item-value="Pay_Type_Name"
                variant="solo"
                :rules="[required('PayType', selectedPayType)]"
                :isAutoComplete="true"
                clearable
                :itemSelected="selectedPayType"
                @selected-item="selectedPayType = $event"
              ></CustomSelect>
            </v-col>
          </v-row>
        </div>

        <div class="ma-4">
          <div
            class="d-flex pt-8 align-center text-h6 text-grey-darken-1 font-weight-bold"
          >
            <v-progress-circular
              model-value="100"
              color="green-darken-2"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            Additional Details
          </div>
          <v-row class="pt-2">
            <VueTelInput
              v-show="false"
              :autoDefaultCountry="true"
              @country-changed="getCountryCode($event)"
            ></VueTelInput>
            <v-col cols="12" md="4" sm="6">
              <div class="d-flex">
                <p class="text-subtitle-1 text-grey-darken-1">
                  Cooling Period (in months)<span style="color: red">*</span>
                </p>
                <v-tooltip
                  text="Allow applicant to apply for the same job after the cooling period"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      class="ml-1"
                      v-bind="props"
                      size="small"
                      color="blue"
                    >
                      fas fa-info-circle
                    </v-icon>
                  </template>
                </v-tooltip>
              </div>
              <v-text-field
                ref="coolingPeriod"
                class="mr-1"
                v-model="coolingPeriod"
                :model-value="validateCoolingPeriod()"
                variant="solo"
                :rules="[
                  required('Cooling Period', coolingPeriod),
                  minMaxNumberValidation(
                    'Cooling Period',
                    coolingPeriod,
                    0.1,
                    99999999999.0
                  ),
                ]"
                type="number"
              ></v-text-field>
            </v-col>
            <!-- <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">Agency Involved</p>
              <v-text-field
              ref="agency"
                v-model="agencyInvolved"
                :counter="100"
                :rules="
                  agencyInvolved
                    ? [
                        maxLengthValidation(
                          'Agency Involved',
                          agencyInvolved,
                          100
                        ),
                        alphaNumSpaceWithFourSymbolValidation(
                          'Agency Involved',
                          agencyInvolved
                        ),
                      ]
                    : ''
                "
                variant="solo"
              ></v-text-field>
            </v-col> -->
            <!-- <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                Job Duration (In Months)
              </p>
              <v-text-field
              ref="jobDuration"
                v-model="jobDuration"
                :model-value="validateJobDuration()"
                :rules="
                  jobDuration
                    ? [
                        minMaxNumberValidation(
                          'Job Duration',
                          jobDuration,
                          1,
                          9999
                        ),
                      ]
                    : ''
                "
                variant="solo"
                type="number"
              ></v-text-field>
            </v-col> -->
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">Field Work</p>
              <v-switch
                color="primary"
                class="mt-4"
                v-model="travelRequired"
                :true-value="1"
                :false-value="0"
              ></v-switch>
            </v-col>
            <v-col
              v-if="labelList[278] && labelList[278].Field_Visiblity === 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[278].Field_Alias
                }}<span
                  v-if="labelList[278].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <CustomSelect
                ref="client"
                :items="dropDownClient"
                item-title="Company_Name"
                item-value="Client_Id"
                :isLoading="dropDownlLoading"
                :isAutoComplete="true"
                variant="solo"
                :itemSelected="selectedClient"
                :rules="[
                  labelList[278].Mandatory_Field == 'Yes'
                    ? required(labelList[278].Field_Alias, selectedClient)
                    : true,
                ]"
                @selected-item="selectedClient = $event"
              ></CustomSelect>
            </v-col>
            <v-col
              cols="12"
              md="6"
              lg="4"
              v-if="labelList[291]?.Field_Visiblity == 'Yes'"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[291].Field_Alias
                }}<span
                  style="color: red"
                  v-if="labelList[291].Mandatory_Field == 'Yes'"
                  >*</span
                >
              </p>
              <CustomSelect
                v-if="labelList[291].Predefined == 'Yes'"
                v-model="selectedManagerId"
                :isLoading="dropdownListLoading"
                :rules="
                  labelList[291].Mandatory_Field == 'Yes'
                    ? [required(labelList[291].Field_Alias, selectedManagerId)]
                    : [true]
                "
                :items="managerList"
                item-title="Manager_Name"
                item-value="Manager_Id"
                variant="solo"
                :isAutoComplete="true"
                :isRequired="true"
                :itemSelected="selectedManagerId"
                @selected-item="selectedManagerId = $event"
              ></CustomSelect>
              <v-text-field
                v-else
                v-model="selectedManager"
                :rules="
                  labelList[291].Mandatory_Field == 'Yes'
                    ? [required(labelList[291].Field_Alias, selectedManager)]
                    : [true]
                "
                variant="solo"
              ></v-text-field>
            </v-col>
            <v-col
              v-if="labelList[256] && labelList[256].Field_Visiblity === 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[256].Field_Alias
                }}<span
                  v-if="labelList[256].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <CustomSelect
                ref="replacementFor"
                :items="dropdownReplacement"
                item-title="UserName"
                item-value="UserId"
                :isLoading="isReplacementListLoading"
                :isAutoComplete="true"
                :select-properties="{
                  multiple: true,
                  chips: true,
                  clearable: true,
                  closableChips: true,
                }"
                variant="solo"
                :rules="[
                  labelList[256].Mandatory_Field == 'Yes'
                    ? required(labelList[256].Field_Alias, selectedReplacement)
                    : true,
                ]"
                :itemSelected="selectedReplacement"
                :disabled="disableReplacementFor"
                @selected-item="selectedReplacement = $event"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="mytoken && irukkaStatus == 'Active'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">Industry</p>
              <CustomSelect
                ref="industry"
                :items="dropdownIndustry"
                item-title="name"
                item-value="industryTypeId"
                :isLoading="isIndustryListLoading"
                :isAutoComplete="true"
                variant="solo"
                :itemSelected="selectedIndustry"
                @selected-item="selectedIndustry = $event"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="mytoken && irukkaStatus == 'Active'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">Category</p>
              <CustomSelect
                ref="category"
                :items="dropdownCategory"
                item-title="name"
                item-value="categoryId"
                :isLoading="isCategoryListLoading"
                :isAutoComplete="true"
                variant="solo"
                :itemSelected="selectedCategory"
                @selected-item="selectedCategory = $event"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="mytoken && irukkaStatus == 'Active'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">Subcategory</p>
              <CustomSelect
                ref="subCategory"
                :items="dropdownSubCategory"
                item-title="name"
                item-value="categoryId"
                :isLoading="isSubCategoryListLoading"
                :isAutoComplete="true"
                variant="solo"
                :itemSelected="selectedSubCategory"
                @selected-item="selectedSubCategory = $event"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[259] && labelList[259].Field_Visiblity === 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[259].Field_Alias
                }}<span
                  v-if="labelList[259].Mandatory_Field == 'Yes'"
                  style="color: red"
                  >*</span
                >
              </p>
              <CustomSelect
                ref="reasonForVacancy"
                :items="dropdownReasonForVacancy"
                :isLoading="dropdownLoading"
                item-title="Vacancy_Reason"
                item-value="Vacancy_Reason"
                :isAutoComplete="true"
                :rules="[
                  labelList[259].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[259].Field_Alias}`,
                        selectedReasonForVacancy
                      )
                    : true,
                ]"
                :select-properties="{
                  clearable: true,
                  closableChips: true,
                }"
                variant="solo"
                :itemSelected="selectedReasonForVacancy"
                :disabled="disableReasonForVacancy"
                @selected-item="selectedReasonForVacancy = $event"
              ></CustomSelect>
            </v-col>
            <v-col v-if="isOtherVacancyReasonSelected" cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                Others<span
                  v-if="isOtherVacancyReasonSelected"
                  style="color: red"
                  >*</span
                >
              </p>
              <v-text-field
                ref="otherReasons"
                v-model="otherReasonForOpening"
                :counter="500"
                :rules="
                  isOtherVacancyReasonSelected
                    ? [
                        required(
                          'Other reasons for vacancy',
                          otherReasonForOpening
                        ),
                        minLengthValidation(
                          'Reason For Opening',
                          otherReasonForOpening,
                          3
                        ),
                        maxLengthValidation(
                          'Reason For Opening',
                          otherReasonForOpening,
                          500
                        ),
                        alphaNumSpaceNewLineWithElevenSymbolValidation(
                          otherReasonForOpening
                        ),
                      ]
                    : ''
                "
                :disabled="isOthersSelectedForVacancyReason"
                variant="solo"
              >
              </v-text-field>
            </v-col>
          </v-row>
        </div>
        <v-row class="mt-10"> </v-row>
        <v-row> </v-row>
        <div class="d-flex justify-center py-8">
          <v-btn
            class="primary px-8 mr-2"
            variant="outlined"
            @click="openCancelWarningModal = true"
          >
            <span class="primary">Cancel</span>
          </v-btn>
          <v-btn type="submit" variant="elevated" class="px-8 primary"
            ><span class="primary">Submit</span></v-btn
          >
        </div>
      </v-card>
    </div>
  </v-form>
  <AppLoading v-if="isAppLoading"></AppLoading>
  <AppWarningModal
    v-if="openCancelWarningModal"
    :open-modal="openCancelWarningModal"
    confirmation-heading="Are you sure to close this form?"
    icon-name="far fa-times-circle"
    @close-warning-modal="openCancelWarningModal = false"
    @accept-modal="this.$emit('submit-job-post-form', this.fillForm, 'close')"
  ></AppWarningModal>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          class="mt-n5 secondary"
          variant="text"
          @click="closeValidationAlert()"
        >
          <span class="primary">Close</span>
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>

<script>
import axios from "axios";
// import VueGoogleAutocomplete from "vue-google-autocomplete";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import moment from "moment";
import validationRules from "../../../mixins/validationRules.js";
import { GET_INTEGRATION_STATUS } from "@/graphql/settings/irukka-integration/irukkaRegistrationQueries.js";
import { RETRIEVE_ORGANISATION_GROUP_LIST } from "@/graphql/corehr/organisationGroupQueries";
import { GET_LINKEDIN_JOB_OPTIONS } from "@/graphql/settings/Integration/publishJobToLinkedin";
import Config from "../../../config.js";
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";
import {
  GET_USER_LIST,
  ADD_JOB_POST,
  GET_COURSE_LIST,
  LIST_JOB_TITLE,
  GET_CUSTOM_GROUP_COVERAGE,
} from "@/graphql/settings/irukka-integration/jobPostFormQueries.js";
import { getCustomFieldName } from "@/helper";
import {
  LIST_CITIES_NO_AUTH,
  GROUP_LIST_IN_JOB_POST,
} from "@/graphql/dropDownQueries.js";
import { VueTelInput } from "vue-tel-input";

export default {
  name: "AddJobPost",
  components: {
    CustomSelect,
    VueTelInput,
    // VueGoogleAutocomplete,
  },
  mixins: [validationRules],
  data() {
    return {
      postingDateMenu: false,
      formattedPostingDate: "",
      closingDateMenu: false,
      formattedClosingDate: "",
      joiningDateMenu: false,
      formattedJoiningDate: "",
      irukkaStatus: "",
      categoryName: "",
      SubCategoryName: "",
      industryName: "",
      fieldForce: 0,
      currentTabItem: "tab-0",
      mainTabList: ["Job Post", "Approvals"],
      showPreForm: false,
      jobTitle: "",
      selectedPriority: null,
      selectedStatus: null,
      selectedManagerId: null,
      selectedManager: "",
      dropDownStatus: [],
      dropdownWorkSchecule: [],
      postingDate: new Date(),
      selectedFunctionalArea: null,
      selectedCustomGroup: null,
      selectedServiceProvider: null,
      dropdownFunctionalArea: [],
      dropdownCustomGroup: [],
      isCustomGroupListLoading: false,
      dropDownServiceProvider: [],
      selectedWorkSchedule: null,
      selectedCurrencey: null,
      dropdownCurrencey: [],
      selectedJobType: null,
      dropdownJobType: [],
      selectedDesignation: null,
      dropdownDesignation: [],
      fillForm: true,
      dropdownPaymentType: [],
      selectedJobLocation: null,
      selectedJobLocationType: null,
      selectedWorkplaceType: null,
      noOfVacancies: null,
      noOfMaleVacancies: null,
      noOfFemaleVacancies: null,
      dropdownJobLocation: [],
      dropdownJobLocationType: ["City", "Municipality", "Province"],
      selectedSkills: null,
      dropdownSkills: [],
      // range is used to position the slider dots in this range initially
      range: [0, 99999999],
      rangeExperience: [0, 40],
      selectedCategory: null,
      dropdownCategory: [],
      selectedIndustry: null,
      dropdownIndustry: [],
      selectedCityId: null,
      selectedCountryCode: "",
      selectedStateId: null,
      cityList: [],
      stateList: [],
      cityListLoading: false,
      groupList: [],
      groupListLoading: false,
      managerList: [],
      selectedGroupId: null,
      industryTypeLoading: true,
      categoryTypeLoading: true,
      dropdownListLoading: true,
      otherWorkPermits: "",
      selectedSubCategory: null,
      dropdownSubCategory: [],
      closingDate: null,
      expectedJoiningDate: null,
      address: "",
      latitude: "",
      longitude: "",
      pincode: "",
      travelRequired: 0,
      dropdownPriority: ["Low", "Medium", "High"],
      dropdownExperienceLevel: [],
      selectedExperienceLevel: null,
      jobDuration: 0,
      selectedReplacement: null,
      dropdownReplacement: [],
      input: "",
      skillSet: [],
      selectedQualificationList: null,
      dropdownQualifications: [],
      jobTitles: [],
      selectedWorkPermits: null,
      selectedOrgGroup: null,
      selectedReasonForVacancy: null,
      dropdownWorkPermits: [],
      dropdownOrgGroup: [],
      dropdownReasonForVacancy: [],
      dropdownLoading: false,
      selectedJobRequirements: null,
      listJobRequirements: [],
      isOtherVacancyReasonSelected: false,
      selectedRounds: null,
      coolingPeriod: null,
      agencyInvolved: "",
      selectedClient: null,
      dropDownClient: [],
      dropdownRounds: [],
      otherReasonForOpening: "",
      jobDescription: "",
      descriptionErrorMsg: "",
      dropdownData: null,
      formattedExpectedJoiningDate: null,
      googleval: "",
      replacementListLoading: true,
      isCourseListLoading: false,
      isJobTitleListLoading: false,
      isReplacementListLoading: false,
      isOrgGroupLoading: false,
      isIndustryListLoading: false,
      isCategoryListLoading: false,
      isSubCategoryListLoading: false,
      mytoken: "",
      variablesObject: {},
      validationMessages: [],
      showValidationAlert: false,
      experienceRangeErrorMessage: "",
      salaryRangeErrorMessage: "",
      rangeError: false,
      hasContent: false,
      showIcon: false,
      openCancelWarningModal: false,
      isAppLoading: false,
      dropDownlLoading: false,
      workPlaceOptions: [],
      isWorkplaceTypeLoading: false,
      customGroupCoverage: null,
      showJobRoundsTab: "No",
      payTypeList: [],
      selectedPayType: null,
      contentLength: 0,
      designationListLoading: false,
      searchString: "",
      jobPositionSummary: null,
      isPositionRecordSelected: false,
      maxNoOfVacancies: null,
      groupCode: null,
      divisionCode: null,
      disableDivisionField: false,
      disableDesignation: false,
      disableReasonForVacancy: false,
      disableReplacementFor: false,
      coreGroupCode: null,
      disableCoreGroup: false,
      designationName: "",
      groupName: "",
      isFormDirty: true,
    };
  },
  props: {
    wholeJobPostData: {
      type: Object,
      required: true,
    },
    jobPostId: {
      type: Object,
      required: true,
    },
    selectedWorkflow: {
      type: Number,
      required: true,
    },
    selectedEventId: {
      type: String,
      required: true,
    },
    selectedPositionRecord: {
      type: Object,
      required: false,
    },
    requestType: {
      type: String,
      required: false,
    },
    jobDescriptionData: {
      type: String,
      required: false,
    },
    orgGroupCode: {
      type: String,
      required: false,
    },
    availableVacancies: {
      type: Number,
      required: true,
    },
  },
  watch: {
    // extracting the category-name with the help of categoryId from
    // dropdownCategory
    selectedCategory(val) {
      let selectedCategory = this.dropdownCategory.find(
        (cat) => cat.categoryId === val
      );
      if (selectedCategory) {
        this.categoryName = selectedCategory.name;
        this.fetchSubCategoryTypeList(this.selectedCategory);
      }
    },
    selectedSubCategory(val) {
      let selectedSubCategory = this.dropdownSubCategory.find(
        (sub) => sub.categoryId === val
      );
      if (selectedSubCategory) {
        this.SubCategoryName = selectedSubCategory.name;
      }
    },
    selectedIndustry(val) {
      let selectedIndustry = this.dropdownIndustry.find(
        (ind) => ind.industryTypeId === val
      );
      if (selectedIndustry) {
        this.industryName = selectedIndustry.name;
        this.fetchCategoryTypeList(this.selectedIndustry);
      }
    },
    selectedReasonForVacancy(val) {
      if (val && val.toLowerCase() == "others") {
        this.isOtherVacancyReasonSelected = true;
      } else {
        this.isOtherVacancyReasonSelected = false;
      }
    },
    isManualForPermanentAddress() {
      this.$refs.googleval.clear();
    },
    selectedCityId(val) {
      let filterCity = this.cityList.filter((el) => el.City_Id == val);
      if (filterCity && filterCity[0]) {
        this.selectedCountryCode = filterCity[0].Country_Code;
        if (filterCity[0].State_Id) {
          this.selectedStateId = filterCity[0].State_Id;
        }
      }
    },

    postingDate(val) {
      if (val) {
        this.postingDateMenu = false;
        let dateValue = this.formatDate(val);
        this.formattedPostingDate = dateValue;
      }
    },
    closingDate(val) {
      if (val) {
        this.closingDateMenu = false;
        let dateValue = this.formatDate(val);
        this.formattedClosingDate = dateValue;
      }
    },
    expectedJoiningDate(val) {
      if (val) {
        this.joiningDateMenu = false;
        let dateValue = this.formatDate(val);
        this.formattedJoiningDate = dateValue;

        this.formattedExpectedJoiningDate = new Date(val);
        let currentTime = new Date();

        // Set the specific date's hours, minutes, and seconds to the current time
        this.formattedExpectedJoiningDate.setHours(currentTime.getHours());
        this.formattedExpectedJoiningDate.setMinutes(currentTime.getMinutes());
        this.formattedExpectedJoiningDate.setSeconds(currentTime.getSeconds());
        this.formattedExpectedJoiningDate.setMilliseconds(
          currentTime.getMilliseconds()
        );
      }
    },
    selectedDesignation(val) {
      this.designationName = this.getDesignationNameById(val);
    },
    selectedGroupId(val) {
      this.groupName = this.getGroupNameById(val);
    },
  },
  computed: {
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    isOthersSelected() {
      if (this.selectedWorkPermits) {
        for (var i = 0; i < this.selectedWorkPermits.length; i++) {
          if (this.selectedWorkPermits[i] == 3) {
            //this.othersChosen = true;
            return false;
          }
        }
        return true;
      }
      return true;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    minDate() {
      if (this.postingDate) {
        return moment(this.postingDate).format("YYYY-MM-DD");
      }
      return null;
    },
    noDataText() {
      if (this.designationListLoading) {
        return "Loading...";
      } else if (
        !this.designationListLoading &&
        this.dropdownDesignation.length == 0 &&
        this.searchString.length >= 3
      ) {
        return "no data found";
      } else {
        return "Type minimum 3 characters to list";
      }
    },
    isInputValid() {
      const rules = [
        this.alphaNumSpaceNewLineWithElevenSymbolValidation(this.input),
        this.required("Key Skills", this.skillSet[0]),
      ];
      return rules[0] === true ? true : false;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {
    this.initQuillEditor();
    this.loadAuthToken();
    this.fetchIntegrationStatus();
    this.retrieveDropDownDetails();
    this.fetchOrganizationGroups();
    this.fetchGetCourseList();
    this.listJobTitle();
    this.fetchGetUsers();
    if (this.mytoken) {
      this.fetchGetIndustryTypeDetail();
    }
    this.closingDateformat();
    this.fetchWorkplaceType();
    this.getCustomGroupCoverage();
    this.listCustomGroup();
    this.retrieveCities();
    this.retrieveGroups();
    this.getDropdownDetails();
    this.fetchDropdownData();
    if (this.postingDate) {
      this.formattedPostingDate = this.formatDate(this.postingDate);
    }
    if (this.closingDate) {
      this.formattedClosingDate = this.formatDate(this.closingDate);
    }
    if (this.expectedJoiningDate) {
      this.formattedJoiningDate = this.formatDate(this.expectedJoiningDate);
    }
    if (
      this.selectedPositionRecord &&
      Object.keys(this.selectedPositionRecord).length
    ) {
      this.isPositionRecordSelected = true;
      this.jobTitle = this.selectedPositionRecord.Position_Title
        ? this.selectedPositionRecord.Position_Title
        : this.selectedPositionRecord.Pos_Name;
      this.noOfVacancies = this.availableVacancies;
      this.maxNoOfVacancies = this.noOfVacancies;
      if (this.selectedPositionRecord?.Pos_Code) {
        this.getDesignationId(this.selectedPositionRecord.Pos_Code);
      }
      this.selectedJobType = parseInt(
        this.selectedPositionRecord.Employee_Type
      );
      this.groupCode = this.selectedPositionRecord.Group_Code;
      if (this.selectedPositionRecord?.Replacement_For) {
        this.selectedReasonForVacancy =
          this.selectedPositionRecord.Replacement_For;
        this.disableReasonForVacancy = true;
      }
      // Will be used later
      // if (this.selectedPositionRecord?.Reason_For_Replacement) {
      //   this.selectedReplacement =
      //     this.selectedPositionRecord.Reason_For_Replacement;
      //   this.disableReplacementFor = true;
      // }
      this.quill.root.innerHTML = this.jobDescriptionData;
      this.coreGroupCode = this.orgGroupCode;
      if (this.selectedPositionRecord?.Division_Code) {
        this.divisionCode = this.selectedPositionRecord.Division_Code;
      } else if (this.selectedPositionRecord?.Department_Code) {
        this.divisionCode = this.selectedPositionRecord.Department_Code;
      } else if (this.selectedPositionRecord?.Section_Code) {
        this.divisionCode = this.selectedPositionRecord.Section_Code;
      }
    } else if (this.wholeJobPostData) {
      this.selectedFunctionalArea = this.wholeJobPostData.Functional_Area_Id;
      this.selectedOrgGroup = this.wholeJobPostData.Organization_Group_Id;
      this.jobTitle = this.wholeJobPostData.Job_Post_Name;
      this.noOfVacancies = this.wholeJobPostData.No_Of_Vacancies;
      this.selectedDesignation = this.wholeJobPostData.Designation_Id;
      this.selectedJobType = this.wholeJobPostData.Job_Type_Id;
      this.quill.root.innerHTML = this.wholeJobPostData.Job_Description
        ? this.convertEmojiCodepointsToEmojis(
            this.wholeJobPostData.Job_Description
          )
        : "";
      this.selectedGroupId = this.wholeJobPostData.Organization_Structure_Id;
    }
  },
  methods: {
    async getDropdownDetails() {
      let vm = this;
      vm.dropdownLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;
      await vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            formId: 15,
            key: [
              "Experience_Level",
              "Required_Certification",
              "Vacancy_Reason",
              "pay_type",
            ],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            tempData.forEach((item) => {
              if (item.tableKey === "Experience_Level") {
                vm.dropdownExperienceLevel = item.data;
                vm.selectedExperienceLevel = vm.dropdownExperienceLevel?.length
                  ? vm.dropdownExperienceLevel[0]?.Experience_Level
                  : null;
              } else if (item.tableKey === "Required_Certification") {
                vm.listJobRequirements = item.data;
              } else if (item.tableKey === "Vacancy_Reason") {
                vm.dropdownReasonForVacancy = item.data;
              } else if (item.tableKey === "pay_type") {
                vm.payTypeList = item.data;
                vm.selectedPayType = vm.payTypeList?.length
                  ? vm.payTypeList[0]?.Pay_Type_Name
                  : null;
              }
            });
          } else {
            let err = res.data.retrieveDropdownDetails.errorCode;
            vm.handleGetDropdownDetails(err);
          }
          vm.dropdownLoading = false;
        })
        .catch((err) => {
          vm.dropdownLoading = false;
          vm.handleGetDropdownDetails(err);
        });
    },
    handleGetDropdownDetails(err) {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Job Post",
        isListError: false,
      });
    },
    getCountryCode(countryCode) {
      if (countryCode && countryCode.iso2) {
        if (countryCode.iso2.toLowerCase() == "in") {
          this.selectedCurrencey = 72;
        } else if (countryCode.iso2.toLowerCase() == "ph") {
          this.selectedCurrencey = 121;
        } else if (countryCode.iso2.toLowerCase() == "id") {
          this.selectedCurrencey = 73;
        } else if (countryCode.iso2.toLowerCase() == "th") {
          this.selectedCurrencey = 151;
        }
      } else {
        this.selectedCurrencey = null;
      }
    },
    getCustomFieldName,
    getOrganizationGroupIdByCode(code, data) {
      const result = data.find(
        (item) => item.organizationGroupCode === code.toString()
      );
      return result ? result.organizationGroupId : null;
    },
    showAddIcon() {
      this.showIcon = !!this.input.trim();
    },
    addChip() {
      if (this.isInputValid && this.input.trim()) {
        this.skillSet.push(this.input.trim());
        this.input = "";
        this.showIcon = false;
      }
    },
    removeChip(index) {
      this.skillSet.splice(index, 1);
    },
    initQuillEditor() {
      this.quill = new Quill(this.$refs.editor, {
        theme: "snow",
      });
      // Set initial font size
      this.setEditorFontSize("16px");
      this.hasContent = !!this.quill.getText().trim();

      // Listen for editor text change events
      this.quill.on("text-change", () => {
        this.hasContent = !!this.quill.getText().trim();
        this.contentLength = this.quill.getLength();
        if (!this.hasContent || this.contentLength <= 1) {
          this.descriptionErrorMsg = "Description is required";
        } else if (this.contentLength <= 100) {
          this.descriptionErrorMsg = "Description minimum length 100";
        } else if (this.contentLength > 15000) {
          this.descriptionErrorMsg = "Description maximum length 15000";
        } else {
          this.descriptionErrorMsg = "";
        }
      });
    },
    setEditorFontSize(fontSize) {
      const editorElement = this.$refs.editor.querySelector(".ql-editor");
      if (editorElement) {
        editorElement.style.fontSize = fontSize;
      }
    },
    loadAuthToken() {
      this.mytoken = window.$cookies.get("irukkaAuthToken");
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    checkStatus() {
      if (this.selectedWorkflow == null) {
        this.selectedStatus = 5;
      } else {
        this.selectedStatus = 1;
      }
    },
    async fetchIntegrationStatus() {
      let vm = this;
      await vm.$apollo
        .query({
          query: GET_INTEGRATION_STATUS,
          variables: {
            form_Id: 15,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.jobBoardIntegrationStatus &&
            response.data.jobBoardIntegrationStatus.getStatus &&
            response.data.jobBoardIntegrationStatus.getStatus.length > 0
          ) {
            this.irukkaStatus =
              response.data.jobBoardIntegrationStatus.getStatus[0].Integration_Status;
          }
        })
        .catch((err) => {
          vm.handleRetrieveIntegrationStatusError(err);
        });
    },
    handleRetrieveIntegrationStatusError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "fetching",
          form: "Integration status",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    findMinRangeExperience() {
      if (this.rangeExperience[0] < 0) {
        this.rangeExperience[0] = 0;
        return this.rangeExperience[0];
      }
      if (this.rangeExperience[1] < 0) {
        this.rangeExperience[1] = 0;
        return this.rangeExperience;
      } else {
        return this.rangeExperience[0];
      }
    },
    findMaxRangeExperience() {
      let minExp = this.rangeExperience[0].toString();
      let maxExp = this.rangeExperience[1].toString();
      if (!minExp || !maxExp) {
        this.experienceRangeErrorMessage = "Experience is required.";
      } else {
        let res = parseInt(maxExp) - parseInt(minExp);
        if (res < 0) {
          this.experienceRangeErrorMessage =
            "Minimum experience should not be greater than maximum experience.";
          if (this.rangeExperience[0] <= 40) {
            return this.rangeExperience[1];
          }
          this.rangeExperience[0] = 40;
          return this.rangeExperience[0];
        }
        if (this.rangeExperience[1] > 40) {
          this.rangeExperience[1] = 40;
          return this.rangeExperience[1];
        } else {
          this.experienceRangeErrorMessage = ""; // Clear error message if valid
          return this.rangeExperience[1];
        }
      }
    },
    findMinRangeSalary() {
      if (this.range[0] < 0) {
        this.range[0] = 0;
        return this.range[0];
      }
      if (this.range[1] < 0) {
        this.range[1] = 0;
        return this.range;
      } else {
        return this.range[0];
      }
    },
    findMaxRangeSalary() {
      let minSal = this.range[0].toString();
      let maxSal = this.range[1].toString();
      if (!minSal || !maxSal) {
        this.salaryRangeErrorMessage = "Salary is required";
      } else {
        let res = parseInt(maxSal) - parseInt(minSal);
        if (res < 0) {
          this.salaryRangeErrorMessage =
            "Minimum salary should not be greater than maximum salary.";
          if (this.range[0] <= 99999999) {
            return this.range[1];
          }
          this.range[0] = 99999999;
          return this.range[0];
        }
        if (this.range[1] > 99999999) {
          this.range[1] = 99999999;
          return this.range[1];
        } else {
          this.salaryRangeErrorMessage = "";
          return this.range[1];
        }
      }
    },
    setAddress(addressData, test) {
      this.address = test.formatted_address;
      this.latitude = addressData.latitude;
      this.longitude = addressData.longitude;
      this.pincode = addressData.postal_code;
    },
    async validateJobPostForm() {
      // checking whether the fields of form are valid or not
      const { valid } = await this.$refs.jobPostForm.validate();
      // submit the form only if all the fields are filled
      this.rangeError = false;
      if (this.salaryRangeErrorMessage || this.experienceRangeErrorMessage) {
        this.rangeError = true;
      }
      let joiningDateValidation =
        this.labelList[211].Mandatory_Field == "Yes"
          ? this.expectedJoiningDate
            ? true
            : false
          : true;
      if (
        valid &&
        joiningDateValidation &&
        this.closingDate &&
        !this.rangeError &&
        this.hasContent
      ) {
        if (this.contentLength <= 1) {
          this.descriptionErrorMsg = "Description is required";
        } else if (this.contentLength <= 15000 && this.contentLength >= 100) {
          this.descriptionErrorMsg = "";
          this.fillForm = false;
          this.postJob();
        } else {
          this.hasContent = false;
        }
      } else {
        if (this.contentLength <= 1) {
          this.descriptionErrorMsg = "Description is required";
        }
        // Check the validity of each field
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            let allTrue = field.rules.every((value) => value === true);
            if (field.rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });
        // Log or handle the invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              let selectFields = [
                "priority",
                "jobTitle",
                "status",
                "serviceProvider",
                "department",
                "customGroup",
                "position",
                "workSchedule",
                "jobLocation",
                "jobLocationType",
                "workplaceType",
                "empType",
                "expectedQualification",
                "workPermits",
                "rounds",
                "currency",
                "client",
                "replacementFor",
                "orgGroup",
                "industry",
                "category",
                "subCategory",
                "reasonForVacancy",
                "state",
                "city",
                "group",
                "jobRequirements",
              ];
              if (selectFields.includes(firstErrorField)) {
                fieldRef.onFocusCustomSelect();
              } else {
                // except for select
                fieldRef.focus();
              }
              if (fieldRef.$el) {
                const rect = fieldRef.$el.getBoundingClientRect();
                window.scrollTo({
                  top: (window.scrollY + rect.top) * 0.4, // Adjust as needed
                  behavior: "smooth",
                });
              }
            }
          });
        }
      }
    },
    onChangeLocation(value) {
      this.selectedJobLocation = value;
      let selectedLocation = this.dropdownJobLocation.filter(
        (el) => el.Location_Id == value
      );
      if (
        selectedLocation &&
        selectedLocation[0] &&
        selectedLocation[0].Currency_Id
      ) {
        this.selectedCurrencey = selectedLocation[0].Currency_Id;
      }
    },
    async retrieveDropDownDetails() {
      this.dropDownlLoading = true;
      this.dropdownData = [];
      await this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then(async (dropDownist) => {
          if (
            dropDownist &&
            dropDownist.data &&
            dropDownist.data.getDropDownBoxDetails
          ) {
            this.dropdownData = dropDownist.data.getDropDownBoxDetails;
            this.dropdownRounds = this.sortByField(
              this.dropdownData["rounds"],
              "Round_Name"
            );
            this.dropdownWorkSchecule = this.sortByField(
              this.dropdownData["workSchedules"],
              "Title"
            );
            if (this.dropdownWorkSchecule.length == 1) {
              this.selectedWorkSchedule =
                this.dropdownWorkSchecule[0].WorkSchedule_Id;
            }
            this.dropdownFunctionalArea = this.sortByField(
              this.dropdownData["departments"],
              "Department_Name"
            );
            if (this.divisionCode) {
              this.selectedFunctionalArea = await this.getDepartmentIdByCode(
                this.divisionCode,
                this.dropdownFunctionalArea
              );
              this.disableDivisionField = this.selectedFunctionalArea
                ? true
                : false;
            }
            this.dropDownClient = this.sortByField(
              this.dropdownData["clients"],
              "Company_Name"
            );
            this.dropdownCurrencey = this.sortByField(
              this.dropdownData["currency"],
              "Currency_Name"
            );
            this.dropdownJobType = this.sortByField(
              this.dropdownData["employeeType"],
              "Employee_Type"
            );
            this.dropdownJobLocation = this.sortByField(
              this.dropdownData["locations"],
              "Location_Name"
            );
            this.dropdownWorkPermits = this.sortByField(
              this.dropdownData["workAuthorizations"],
              "Work_Authorization_Name"
            );
            this.dropDownStatus = this.dropdownData["status"];
            this.checkStatus();
            this.dropDownServiceProvider = this.sortByField(
              this.dropdownData["serviceProvider"],
              "Service_Provider_Name"
            );
            this.fieldForce = this.dropdownData["fieldForce"];
            this.preFillJobPostForm();
          }
          this.dropDownlLoading = false;
        })
        .catch((err) => {
          this.dropDownlLoading = false;
          this.handleDropdownDataError(err);
        });
    },
    async fetchGetCourseList() {
      let vm = this;
      this.isCourseListLoading = true;
      await vm.$apollo
        .query({
          query: GET_COURSE_LIST,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.getCourseList) {
            this.dropdownQualifications = this.sortByField(
              response.data.getCourseList.Courses,
              "Course_Name"
            );
          } else {
            this.dropdownQualifications = [];
          }
          this.isCourseListLoading = false;
        })
        .catch(() => {
          this.dropdownQualifications = [];
          this.isCourseListLoading = false;
        });
    },
    async listJobTitle() {
      let vm = this;
      this.isJobTitleListLoading = true;
      await vm.$apollo
        .query({
          query: LIST_JOB_TITLE,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: { Form_Id: 15 },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.jobTitleList &&
            response.data.jobTitleList.jobTitleResult &&
            response.data.jobTitleList.jobTitleResult.length
          ) {
            this.jobTitles = this.sortByField(
              response.data.jobTitleList.jobTitleResult,
              "Pos_Name"
            );
          } else {
            this.jobTitles = [];
          }
          this.isJobTitleListLoading = false;
        })
        .catch(() => {
          this.jobTitles = [];
          this.isJobTitleListLoading = false;
        });
    },
    async listCustomGroup() {
      let vm = this;
      vm.isCustomGroupListLoading = true;
      await this.$store
        .dispatch("listCustomGroupBasedOnFormName", {
          formName: "Job Posts",
        })
        .then((groupList) => {
          if (groupList && groupList.length) {
            this.dropdownCustomGroup = groupList;
          } else {
            this.dropdownCustomGroup = [];
          }
          this.isCustomGroupListLoading = false;
        })
        .catch(() => {
          this.isCustomGroupListLoading = false;
          this.dropdownCustomGroup = [];
        });
    },
    async getCustomGroupCoverage() {
      let vm = this;
      await vm.$apollo
        .query({
          query: GET_CUSTOM_GROUP_COVERAGE,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.recruitmentSetting &&
            response.data.recruitmentSetting.settingResult &&
            response.data.recruitmentSetting.settingResult.length
          ) {
            this.customGroupCoverage =
              response.data.recruitmentSetting.settingResult[0].Coverage;
            this.showJobRoundsTab =
              response.data.recruitmentSetting.settingResult[0].Show_Job_Rounds_Tab;
          }
        })
        .catch((err) => {
          this.handleGetCustomGroupCoverageError(err);
        });
    },
    handleGetCustomGroupCoverageError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "getting",
        form: " job post",
        isListError: false,
      });
    },
    async fetchGetUsers() {
      let vm = this;
      this.isReplacementListLoading = true;
      await vm.$apollo
        .query({
          query: GET_USER_LIST,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.getUsers) {
            this.dropdownReplacement = response.data.getUsers.users;
            this.replacementListLoading = false;
          } else {
            this.dropdownReplacement = [];
          }
          this.isReplacementListLoading = false;
        })
        .catch(() => {
          this.dropdownReplacement = [];
          this.isReplacementListLoading = false;
        });
    },
    async postJob() {
      let vm = this;
      vm.isAppLoading = true;
      (vm.variablesObject = {
        mppPositionType: vm.isPositionRecordSelected ? vm.requestType : null,
        positionRequestId: vm.selectedPositionRecord
          ? vm.requestType?.toLowerCase() === "recruitment request"
            ? vm.selectedPositionRecord.Recruitment_Id
            : vm.selectedPositionRecord.Position_Request_Id
          : null,
        clientId: vm.selectedClient,
        jobPostName: vm.jobTitle,
        functionalArea: vm.selectedFunctionalArea,
        serviceProviderId: vm.selectedServiceProvider,
        designation: vm.selectedDesignation,
        minWorkExperience: parseFloat(this.rangeExperience[0]),
        maxWorkExperience: parseFloat(this.rangeExperience[1]),
        jobType: vm.selectedJobType,
        paymentType: vm.selectedWorkSchedule,
        currency: vm.selectedCurrencey,
        minPaymentFrequency: parseFloat(this.range[0]),
        maxPaymentFrequency: parseFloat(this.range[1]),
        noOfVacancies: parseInt(this.noOfVacancies),
        noOfMaleVacancies: null,
        noOfFemaleVacancies: null,
        jobDescription: this.replaceEmojisWithCodepoints(),
        closingDate: moment(vm.closingDate).isValid()
          ? moment(vm.closingDate).utc().format("YYYY-MM-DD")
          : null,
        reasonForOpening: vm.selectedReasonForVacancy,
        expectedJoiningDate: moment(vm.formattedExpectedJoiningDate).isValid()
          ? moment(vm.formattedExpectedJoiningDate).utc().format("YYYY-MM-DD")
          : null,
        priority: vm.selectedPriority,
        experienceLevel: vm.selectedExperienceLevel,
        travelRequired: vm.travelRequired,
        agencyInvolved: vm.agencyInvolved,
        jobDuration: parseInt(vm.jobDuration) ? parseInt(vm.jobDuration) : 0,
        jobPostWorkflow: vm.selectedWorkflow,
        keySkills: [2],
        // workAuthorization: this.selectedWorkPermits,
        // otherWorkAuthorization: this.otherWorkPermits,
        organizationGroupId: this.selectedOrgGroup,
        otherReasonForOpening: this.otherReasonForOpening,
        jobLocations: this.selectedJobLocation,
        jobLocationType: this.selectedJobLocationType,
        WorkPlaceType: this.selectedWorkplaceType,
        customGroupId: this.selectedCustomGroup,
        rounds: this.selectedRounds,
        replacementFor: this.selectedReplacement,
        qualification: this.selectedQualificationList,
        coolingPeriod: parseFloat(this.coolingPeriod),
        status: this.selectedStatus,
        postingDate: moment(this.postingDate).isValid()
          ? moment(this.postingDate).utc().format("YYYY-MM-DD")
          : null,
        Address: this.address,
        No_Of_Male_Vacancies: parseInt(this.noOfMaleVacancies),
        No_Of_Female_Vacancies: parseInt(this.noOfFemaleVacancies),
        Category_Id: this.selectedCategory,
        Category_Name: this.categoryName,
        Subcategory_Id: this.selectedSubCategory,
        Subcategory_Name: this.SubCategoryName,
        Industry_Id: this.selectedIndustry,
        Industry_Name: this.industryName,
        Skill_Set: this.skillSet,
        Latitude: this.latitude ? this.latitude.toString() : "",
        Longitude: this.longitude ? this.longitude.toString() : "",
        Pincode: this.pincode ? this.pincode.toString() : "",
        Event_Id: this.selectedEventId,
        addedBy: vm.loginEmployeeId,
        countryCode: vm.selectedCountryCode,
        requiredCertificationId: vm.selectedJobRequirements
          ? parseInt(vm.selectedJobRequirements)
          : null,
        stateId: vm.selectedStateId,
        cityId: vm.selectedCityId,
        organizationStructureId: vm.selectedGroupId,
        hiringManagerId: vm.selectedManagerId,
        hiringManagerName: vm.selectedManager,
        payType: vm.selectedPayType,
      }),
        await vm.$apollo
          .mutate({
            mutation: ADD_JOB_POST,
            variables: this.variablesObject,
            client: "apolloClientA",
          })
          .then(() => {
            vm.isAppLoading = false;
            let snackbarData = {
              isOpen: true,
              message: "Job posted successfully.",
              type: "success",
            };
            this.showAlert(snackbarData);
            this.$emit("submit-job-post-form", this.fillForm, "sumbit");
          })
          .catch((err) => {
            vm.isAppLoading = false;
            vm.handleAddJobPostError(err);
          });
    },
    validateCoolingPeriod() {
      if (this.coolingPeriod <= 0) {
        this.coolingPeriod = null;
        return this.coolingPeriod;
      } else {
        return this.coolingPeriod;
      }
    },
    validateNoOfVacancies() {
      if (this.noOfVacancies <= 0) {
        this.noOfVacancies = null;
        return this.noOfVacancies;
      }
      if (this.maxNoOfVacancies && this.maxNoOfVacancies < this.noOfVacancies) {
        this.noOfVacancies = this.maxNoOfVacancies;
        return this.noOfVacancies;
      } else {
        this.noOfVacancies = Math.floor(Number(this.noOfVacancies)).toString();
        return this.noOfVacancies;
      }
    },
    validateNoOfMaleVacancies() {
      if (this.noOfMaleVacancies <= 0) {
        this.noOfMaleVacancies = null;
        return this.noOfMaleVacancies;
      } else {
        this.noOfMaleVacancies = Math.floor(
          Number(this.noOfMaleVacancies)
        ).toString();
        return this.noOfMaleVacancies;
      }
    },
    validateNoOfFemaleVacancies() {
      if (this.noOfFemaleVacancies <= 0) {
        this.noOfFemaleVacancies = null;
        return this.noOfFemaleVacancies;
      } else {
        this.noOfFemaleVacancies = Math.floor(
          Number(this.noOfFemaleVacancies)
        ).toString();
        return this.noOfFemaleVacancies;
      }
    },
    validateJobDuration() {
      if (this.jobDuration <= 0) {
        this.jobDuration = null;
        return this.jobDuration;
      } else {
        this.jobDuration = Math.floor(Number(this.jobDuration)).toString();
        return this.jobDuration;
      }
    },
    handleAddJobPostError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "adding",
          form: "Jobpost",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    async fetchGetIndustryTypeDetail() {
      this.isIndustryListLoading = true;
      try {
        const url = Config.irukkaUrl + "/industry";
        let headers = {
          "login-type": "Cognito-Authentication",
          "Partner-Id": "82b03fe2-2f77-477d-8cfb-5d3e58b094e3",
          Authorization: this.mytoken,
        };
        const response = await axios.get(url, { headers });
        this.dropdownIndustry = response.data.payload;
        this.isIndustryListLoading = false;
      } catch (error) {
        this.dropdownIndustry = [];
        this.isIndustryListLoading = false;
      }
    },
    async fetchCategoryTypeList(IndustryId) {
      this.isCategoryListLoading = true;
      try {
        const url = Config.irukkaUrl + "/parent/category/" + IndustryId;
        let headers = {
          "login-type": "Cognito-Authentication",
          "Partner-Id": "82b03fe2-2f77-477d-8cfb-5d3e58b094e3",
          Authorization: this.mytoken,
        };
        const response = await axios.get(url, { headers });
        this.dropdownCategory = response.data.payload;
        this.isCategoryListLoading = false;
      } catch (error) {
        this.dropdownCategory = [];
        this.isCategoryListLoading = false;
      }
    },
    async fetchSubCategoryTypeList(categoryId) {
      this.isSubCategoryListLoading = true;
      try {
        const url = Config.irukkaUrl + "/child/category/" + categoryId;
        let headers = {
          "login-type": "Cognito-Authentication",
          "Partner-Id": "82b03fe2-2f77-477d-8cfb-5d3e58b094e3",
          Authorization: this.mytoken,
        };
        const response = await axios.get(url, { headers });
        this.dropdownSubCategory = response.data.payload;
        this.isSubCategoryListLoading = false;
      } catch (error) {
        this.dropdownSubCategory = [];
        this.isSubCategoryListLoading = false;
      }
    },
    //retrive Organization Group data
    fetchOrganizationGroups() {
      let vm = this;
      vm.isOrgGroupLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_ORGANISATION_GROUP_LIST,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
          variables: {
            formId: 15,
          },
        })
        .then(async (response) => {
          if (
            response.data &&
            response.data.listOrganizationGroup &&
            !response.data.listOrganizationGroup.errorCode
          ) {
            const { organizationGroupObject } =
              response.data.listOrganizationGroup;
            if (organizationGroupObject) {
              const orgGroupListData =
                organizationGroupObject.organizationGroupList
                  ? organizationGroupObject.organizationGroupList
                  : [];
              vm.dropdownOrgGroup = orgGroupListData.filter(
                (item) => item.status.toLowerCase() === "active"
              );
              vm.dropdownOrgGroup = this.sortByField(
                vm.dropdownOrgGroup,
                "organizationGroup"
              );
              if (this.coreGroupCode) {
                this.selectedOrgGroup = await this.getOrganizationGroupIdByCode(
                  this.coreGroupCode,
                  this.dropdownOrgGroup
                );
                this.disableCoreGroup = this.selectedOrgGroup ? true : false;
              }
            } else {
              this.dropdownOrgGroup = [];
            }
          } else {
            this.dropdownOrgGroup = [];
          }
          vm.isOrgGroupLoading = false;
        })
        .catch(() => {
          vm.isOrgGroupLoading = false;
          vm.dropdownOrgGroup = [];
        });
    },
    handleDropdownDataError() {
      this.dropdownRounds = [];
      this.dropdownWorkSchecule = [];
      this.dropdownFunctionalArea = [];
      this.dropdownCurrencey = [];
      this.dropdownJobType = [];
      this.dropdownJobLocation = [];
      this.dropdownWorkPermits = [];
      this.dropDownStatus = [];
      this.dropDownClient = [];
    },
    checkForDropDownLoading() {
      if (this.dropDownlLoading == false) {
        return false;
      } else {
        return true;
      }
    },
    closingDateformat() {
      const currentDate = moment();

      // Add 30 days to the current date
      this.closingDate = currentDate.add(30, "days");
      if (this.closingDate) {
        this.closingDate = new Date(this.closingDate);
      }
    },
    sortByField(data, fieldName) {
      return data.sort((a, b) => {
        const nameA = a[fieldName].toLowerCase();
        const nameB = b[fieldName].toLowerCase();

        if (nameA < nameB) {
          return -1;
        }
        if (nameA > nameB) {
          return 1;
        }
        return 0;
      });
    },
    fetchWorkplaceType() {
      let vm = this;
      vm.isWorkplaceTypeLoading = true;
      vm.$apollo
        .query({
          query: GET_LINKEDIN_JOB_OPTIONS,
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getDropDownForLinkedIn
          ) {
            let options = response.data.getDropDownForLinkedIn;
            if (options.workPlaceType && options.workPlaceType.length > 0) {
              this.workPlaceOptions = options.workPlaceType;
              this.selectedWorkplaceType =
                this.workPlaceOptions.length > 1
                  ? this.workPlaceOptions[1].Code
                  : null;
            }
          }
          vm.isWorkplaceTypeLoading = false;
        })
        .catch(() => {
          this.isWorkplaceTypeLoading = false;
          this.workPlaceOptions = [];
        });
    },
    preFillJobPostForm() {
      if (this.wholeJobPostData) {
        this.selectedPriority = this.wholeJobPostData.Priority;
        this.selectedExperienceLevel = this.wholeJobPostData.Experience_Level;
        this.postingDate = new Date();
        this.selectedWorkSchedule = this.wholeJobPostData.Payment_Type_Id;
        this.selectedPayType = this.wholeJobPostData.Pay_Type;
        this.selectedJobLocation = this.wholeJobPostData.JobLocations.map(
          (item) => {
            return item.Location_Id;
          }
        );
        this.skillSet = this.wholeJobPostData.Skill_Set;
        this.input = " ";
        this.selectedCityId = this.wholeJobPostData.City_Id;
        this.selectedStateId = this.wholeJobPostData.State_Id;
        this.selectedJobRequirements =
          this.wholeJobPostData.Required_Certification_Id;
        this.selectedServiceProvider =
          this.wholeJobPostData.Service_Provider_Id;
        this.selectedQualificationList =
          this.wholeJobPostData.Qualification.map((item) => {
            return item.Qualification_Id;
          });
        if (
          this.wholeJobPostData.Designation_Id &&
          this.wholeJobPostData.Designation
        )
          this.dropdownDesignation = [
            {
              Designation_Id: this.wholeJobPostData.Designation_Id,
              Designation_Name: this.wholeJobPostData.Designation,
            },
          ];
        // this.selectedWorkPermits = this.wholeJobPostData.WorkAuthorization.map(
        //   (item) => {
        //     return item.Work_Authorization_Id;
        //   }
        // );
        // this.otherWorkPermits = this.wholeJobPostData.OtherWorkAuthorization
        //   ? this.wholeJobPostData.OtherWorkAuthorization
        //   : "";
        this.selectedReasonForVacancy =
          this.wholeJobPostData.Reason_For_Opening;
        this.otherReasonForOpening =
          this.wholeJobPostData.Other_Reason_For_Opening;
        this.selectedRounds = this.wholeJobPostData.Rounds.map((item) => {
          return item.Round_Id;
        });
        this.selectedCurrencey = this.wholeJobPostData.Currency_Id;
        this.coolingPeriod = this.wholeJobPostData.Cooling_Period;
        this.agencyInvolved = this.wholeJobPostData.Agency_Involved;
        this.jobDuration = this.wholeJobPostData.Job_Duration
          ? this.wholeJobPostData.Job_Duration
          : null;
        this.selectedClient = this.wholeJobPostData.Client_Id;
        this.selectedReplacement = this.wholeJobPostData.ReplacementFor.map(
          (item) => {
            return item.Employee_Id;
          }
        );
        this.rangeExperience[0] = this.wholeJobPostData.Min_Work_Experience
          ? this.wholeJobPostData.Min_Work_Experience
          : 0;
        this.rangeExperience[1] = this.wholeJobPostData.Max_Work_Experience
          ? this.wholeJobPostData.Max_Work_Experience
          : 40;
        this.range[0] = this.wholeJobPostData.Min_Payment_Frequency
          ? this.wholeJobPostData.Min_Payment_Frequency
          : 0;
        this.range[1] = this.wholeJobPostData.Max_Payment_Frequency
          ? this.wholeJobPostData.Max_Payment_Frequency
          : 99999999;
        this.travelRequired = this.wholeJobPostData.Travel_Required;
        this.selectedJobLocationType = this.wholeJobPostData.jobLocationType;
        this.selectedWorkplaceType = this.wholeJobPostData.workPlaceType;
        this.selectedCustomGroup = this.wholeJobPostData.customGroupId;
        // this.$refs.googleval.autocompleteText = this.wholeJobPostData.Address;
        // this.address = this.$refs.googleval.autocompleteText;
        this.selectedCategory =
          this.mytoken && this.irukkaStatus == "Active"
            ? this.wholeJobPostData.Category_Id
            : null;
        this.categoryName =
          this.mytoken && this.irukkaStatus == "Active"
            ? this.wholeJobPostData.Category_Name
            : null;
        this.selectedSubCategory =
          this.mytoken && this.irukkaStatus == "Active"
            ? this.wholeJobPostData.Subcategory_Id
            : null;
        this.SubCategoryName =
          this.mytoken && this.irukkaStatus == "Active"
            ? this.wholeJobPostData.Subcategory_Name
            : null;
        this.selectedIndustry =
          this.mytoken && this.irukkaStatus == "Active"
            ? this.wholeJobPostData.Industry_Id
            : null;
        this.industryName =
          this.mytoken && this.irukkaStatus == "Active"
            ? this.wholeJobPostData.Industry_Name
            : null;
        this.noOfMaleVacancies =
          this.mytoken && this.irukkaStatus == "Active"
            ? this.wholeJobPostData.No_Of_Male_Vacancies
            : null;
        this.noOfFemaleVacancies =
          this.mytoken && this.irukkaStatus == "Active"
            ? this.wholeJobPostData.No_Of_Female_Vacancies
            : null;
        this.selectedManagerId = this.wholeJobPostData.Hiring_Manager_Id;
        if (this.selectedManagerId) {
          this.selectedManager = this.managerList.filter((item) => {
            if (item.Manager_Id == this.selectedManagerId) {
              return item.Manager_Name;
            }
          });
        } else {
          this.selectedManager = this.wholeJobPostData.Hiring_Manager_Name;
        }
      }
    },
    retrieveCities() {
      let vm = this;
      vm.cityListLoading = true;
      vm.$apollo
        .query({
          query: LIST_CITIES_NO_AUTH,
          client: "apolloClientAS",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getCityListWithState &&
            !response.data.getCityListWithState.errorCode
          ) {
            const { cityDetails } = response.data.getCityListWithState;
            vm.cityList = cityDetails;
            // Create an array to store unique states
            let uniqueStates = [];

            // Iterate through each city object
            cityDetails.forEach((city) => {
              let stateId = city.State_Id;
              let stateName = city.State_Name;

              // Check if the state is already in the uniqueStates array
              let exists = uniqueStates.some(
                (state) => state.State_Id === stateId
              );

              // If not, add it to the array
              if (!exists) {
                uniqueStates.push({
                  State_Id: stateId,
                  State_Name: stateName,
                });
              }
            });
            vm.stateList = uniqueStates;
          }
          vm.cityListLoading = false;
        })
        .catch(() => {
          vm.cityListLoading = false;
        });
    },
    retrieveGroups() {
      let vm = this;
      vm.groupListLoading = true;
      vm.$apollo
        .query({
          query: GROUP_LIST_IN_JOB_POST,
          client: "apolloClientA",
          variables: {
            Form_Id: 15,
            conditions: [
              {
                key: "Org_Level",
                operator: "=",
                value: "GRP",
              },
            ],
          },
        })
        .then(async (response) => {
          if (
            response.data &&
            response.data.jobTitleList &&
            !response.data.jobTitleList.errorCode
          ) {
            const { jobTitleResult } = response.data.jobTitleList;
            vm.groupList = jobTitleResult;
            if (this.groupCode) {
              this.selectedGroupId =
                await this.getOrganizationStructureIdByPosCode(
                  this.groupCode,
                  this.groupList
                );
            }
          }
          vm.groupListLoading = false;
        })
        .catch(() => {
          vm.groupListLoading = false;
        });
    },
    async fetchDropdownData() {
      this.dropdownListLoading = true;
      await this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const { managers } = res.data.getDropDownBoxDetails;
            this.managerList = managers;
          }
          this.dropdownListLoading = false;
        })
        .catch(() => {
          this.dropdownListLoading = false;
        });
    },

    callDesignationList(searchString) {
      this.searchString = searchString;
      if (searchString.length >= 3 && !this.selectedDesignation) {
        this.fetchDesignationList(searchString);
      }
    },

    async fetchDesignationList(searchString) {
      this.designationListLoading = true;
      await this.$store
        .dispatch("getDesignationList", {
          status: "",
          searchString: searchString,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            const { designationResult } = res.data.getDesignationDetails;
            this.dropdownDesignation = designationResult;
          }
          this.designationListLoading = false;
        })
        .catch(() => {
          this.designationListLoading = false;
          this.dropdownDesignation = [];
        });
    },
    async getDesignationId(code) {
      this.selectedDesignation = await this.getDesignationIdByCode(code);
      if (this.selectedDesignation) {
        this.disableDesignation = true;
      }
    },
    async getDesignationIdByCode(code) {
      await this.fetchDesignationList(code.trim());
      const result = this.dropdownDesignation.find(
        (item) => item.Designation_Code === code
      );
      return result ? result.Designation_Id : null;
    },
    getOrganizationStructureIdByPosCode(posCode, groupList) {
      const result = groupList.find((job) => job.Pos_Code === posCode);
      return result ? result.Organization_Structure_Id : null;
    },
    getDepartmentIdByCode(departmentCode, departmentDetails) {
      const result = departmentDetails.find(
        (dept) => dept.Department_Code === departmentCode
      );
      return result ? result.Department_Id : null;
    },
    getDesignationNameById(designationId) {
      const designation = this.dropdownDesignation.find(
        (item) => item.Designation_Id === designationId
      );
      return designation ? designation.Designation_Name : null;
    },
    getGroupNameById(groupId) {
      const group = this.groupList.find(
        (item) => item.Organization_Structure_Id === groupId
      );
      return group ? group.Pos_Name : null;
    },
    convertEmojiCodepointsToEmojis(text) {
      return text.replace(/\[EMOJI:([0-9a-f-]+)\]/gi, (match, codePoints) => {
        // Split by dash if there are multiple code points
        const codePointArray = codePoints.split("-");

        // Convert each hex code point back to a character and join them
        const emoji = codePointArray
          .map((hex) => String.fromCodePoint(parseInt(hex, 16)))
          .join("");

        return emoji;
      });
    },
    replaceEmojisWithCodepoints() {
      const text = this.quill.root.innerHTML;
      // This regex catches both basic emojis and those with variation selectors
      return text.replace(/(\p{Emoji}[\u{FE00}-\u{FE0F}]?)/gu, (match) => {
        const codePoints = Array.from(match)
          .map((ch) => ch.codePointAt(0).toString(16))
          .join("-");
        return `[EMOJI:${codePoints}]`;
      });
    },
  },
};
</script>

<style scoped>
.chips-container {
  position: relative;
}

.chips {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}
.google-auto-complete-address-field {
  width: 100% !important;
}
.google-auto-complete-address-field:focus {
  outline: none !important;
}
.cardColor {
  background-color: #fdfeff;
}
.quill-editor {
  height: 400px;
}
.tooltip-wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
}

.custom-tooltip {
  visibility: hidden;
  background-color: #333;
  color: #fff;
  text-align: center;
  padding: 5px;
  border-radius: 4px;
  font-size: 14px;
  position: absolute;
  bottom: 100%; /* Position above the CustomSelect */
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.2s;
  white-space: normal;
}

.tooltip-wrapper:hover .custom-tooltip {
  visibility: visible;
  opacity: 1;
}
</style>
