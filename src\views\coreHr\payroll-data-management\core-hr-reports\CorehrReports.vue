<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isFilter="false"
                :is-search="true"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            button-text="Retry"
            @button-click="fetchList()"
          />

          <AppFetchErrorScreen
            v-else-if="originalList?.length === 0"
            key="no-data-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row style="background: white" class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      color="transparent"
                      rounded="lg"
                      variant="flat"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="fetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="itemList?.length == 0"
            key="no-results-screen"
            :main-title="`There are no ${landedFormName} matched for the selected filters/searches.`"
            image-name="common/no-records"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="windowWidth <= 960 ? 'small' : 'default'"
                      @click="resetFilter('grid')"
                    >
                      <span class="primary">Reset Filter/Search </span>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <div v-else>
            <div v-if="reportCategories?.length > 0">
              <template
                v-for="(reportList, index) in reportCategories"
                :key="index"
              >
                <v-row v-if="reportList.reports?.length" class="mx-12 my-4">
                  <v-col cols="12" class="body-1 text-primary font-weight-bold">
                    {{ checkNullValue(reportList.header) }}
                  </v-col>
                  <v-col
                    v-for="(report, i) in reportList.reports"
                    :key="i + report.Rep_Title"
                    xl="3"
                    lg="3"
                    md="4"
                    sm="6"
                    cols="12"
                    class="pa-2"
                  >
                    <v-hover>
                      <template #default="{ hover }">
                        <v-card
                          class="imgHover pa-2 rounded-lg"
                          height="100%"
                          @click="actionOnReport(report)"
                        >
                          <v-card-title
                            :class="{ 'common-box-shadow': hover }"
                            class="d-flex align-center"
                            min-height="65"
                          >
                            <v-tooltip
                              :text="report?.Report_Description"
                              location="top"
                              max-width="500"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                                  v-bind="props"
                                >
                                  {{ checkNullValue(report.Rep_Title) }}
                                </div>
                              </template>
                            </v-tooltip>
                            <v-spacer />
                            <v-icon
                              class="fas fa-file-export"
                              size="17"
                              color="primary"
                            >
                              fas fa-file-export
                            </v-icon>
                          </v-card-title>
                        </v-card>
                      </template>
                    </v-hover>
                  </v-col>
                </v-row>
              </template>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
    <TimeOffReportsModel
      v-if="showReportsInfo"
      :report-data="selectedItem"
      @close-model="closeAllForms()"
    />
  </div>
</template>
<script>
const { defineAsyncComponent } = require("vue");
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const TimeOffReportsModel = defineAsyncComponent(() =>
  import("@/views/my-team/time-off/time-off-reports/TimeOffReportsModel.vue")
);
import { checkNullValue } from "@/helper";
export default {
  name: "CorehrReports",
  components: {
    EmployeeDefaultFilterMenu,
    TimeOffReportsModel,
  },
  data() {
    return {
      originalList: [],
      itemList: [],
      selectedItem: null,
      showReportsInfo: false,
      // error/loading
      errorContent: "",
      isErrorInList: false,
      listLoading: false,
      // Leaves
      currentTabItem: "",
    };
  },
  computed: {
    landedFormName() {
      return this.accessRights(362)?.customFormName || "Reports";
    },
    baseUrl() {
      // return "https://capricetest.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
    reportCategories() {
      // Group items by Report_Header
      const groupedByHeader = {};

      // If itemList is empty, return an empty array
      if (!this.itemList || this.itemList.length === 0) {
        return [];
      }

      // Group items by their Report_Header
      this.itemList.forEach((item) => {
        const header = item.Report_Header || "Reports";
        if (!groupedByHeader[header]) {
          groupedByHeader[header] = [];
        }
        groupedByHeader[header].push(item);
      });

      // Convert the grouped object to an array of category objects
      return Object.entries(groupedByHeader).map(([header, reports]) => ({
        header,
        reports,
      }));
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    salaryPayslipFormAccess() {
      let formAccessRights = this.accessRights(38);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    formAccess() {
      let formAccessRights = this.accessRights(362);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"] &&
        this.salaryPayslipFormAccess
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    coreHrPayrollDataManagementFormAcess() {
      return this.$store.getters.coreHrPayrollDataManagementFormAcess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.coreHrPayrollDataManagementFormAcess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText = `There are no ${this.landedFormName.toLowerCase()} for the selected filters/searches.`;
      }
      return msgText;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },
  methods: {
    checkNullValue,
    actionOnReport(report = "") {
      this.selectedItem = report;
      this.showReportsInfo = true;
    },
    closeAllForms() {
      this.showReportsInfo = false;
      this.selectedItem = null;
    },
    async fetchList() {
      let vm = this;
      vm.isErrorInList = false;
      vm.errorContent = "";
      vm.listLoading = true;
      try {
        const apiObj = {
          url: vm.baseUrl + "reports/hr-reports/list-report-details",
          type: "POST",
          dataType: "json",
          data: {
            formId: [38],
          },
        };
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          vm.itemList = response?.reportDetails || [];
          vm.originalList = response?.reportDetails || [];
        } else {
          let snackbarData = {
            isOpen: true,
            type: response?.type || "warning",
            message:
              response?.msg ||
              "Something went wrong. Please try after some time.",
          };
          vm.itemList = [];
          vm.originalList = [];
          vm.showAlert(snackbarData);
        }
      } catch (err) {
        vm.isErrorInList = true;
        vm.errorContent = err;
        vm.showAlert({
          isOpen: true,
          type: "warning",
          message: err || "Something went wrong. Please try again later.",
        });
      } finally {
        vm.listLoading = false;
      }
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    resetFilter() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.listLoading = true;
        const { formAccess } = this.coreHrPayrollDataManagementFormAcess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/core-hr/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/core-hr/" + clickedForm.url;
        }
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style scoped>
.container {
  padding: 3.7em 0em 0em 0em;
}
.leaves-card {
  padding: 0em 0em 0em 0em;
}
/* Zoom In #1 */
.imgHover img {
  -webkit-transform: scale(1);
  transform: scale(1);
  -webkit-transition: 0.3s ease-in-out;
  transition: 0.3s ease-in-out;
}
.imgHover:hover img {
  -webkit-transform: scale(1.3);
  transform: scale(1.3);
}
@media screen and (max-width: 805px) {
  .container {
    padding: 4em 1em 0em 1em;
  }
  .leaves-card {
    padding: 0em 0em 0em 0em;
  }
}
</style>
