<template>
  <div class="text-center">
    <v-overlay
      v-if="overlay"
      v-model="overlay"
      class="d-flex justify-end overlay"
      persistent
      @click:outside="closeWindow()"
    >
      <template v-slot:default>
        <v-card
          rounded="lg"
          :style="
            isMobileView
              ? 'width:90vw; height: 100vh;'
              : 'width:35vw; height: 100vh;'
          "
        >
          <v-card-title
            class="d-flex justify-space-between align-center bg-primary"
          >
            <div class="text-h6">Move to Other Talent Pool</div>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="closeWindow(true)"
              color="white"
            ></v-btn>
          </v-card-title>

          <v-card-text v-if="isLoading" class="d-flex justify-center">
            <AppLoading />
          </v-card-text>

          <v-card-text v-else class="overflow-y-auto" style="max-height: 85vh">
            <v-form ref="ArchiveForm" @submit.prevent="">
              <v-row class="px-sm-4 px-md-6 pt-sm-4">
                <v-col
                  cols="12"
                  sm="12"
                  lg="12"
                  md="12"
                  xl="12"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                >
                  <CustomSelect
                    :items="itemTalentList"
                    label="Talent Pool"
                    itemValue="talentPoolId"
                    itemTitle="talentPool"
                    variant="solo"
                    :isAutoComplete="true"
                    :isRequired="true"
                    clearable
                    :rules="[required('Talent Pool', selectedTalentPoolId)]"
                    :itemSelected="selectedTalentPoolId"
                    @selected-item="selectedTalentPoolId = $event"
                    @update:model-value="deductFormChange()"
                  ></CustomSelect>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>

          <!-- Cancel and Submit buttons -->
          <v-col cols="12" class="d-flex justify-end pr-4">
            <div
              class="d-flex justify-end pa-2 position-absolute"
              style="bottom: 0"
            >
              <v-btn
                rounded="lg"
                variant="outlined"
                class="mr-4"
                @click="closeWindow(true)"
              >
                Cancel
              </v-btn>
              <!-- Conditional Next/Submit button -->
              <div class="mr-1">
                <v-btn
                  v-bind="isFormDirty ? '' : props"
                  rounded="lg"
                  variant="elevated"
                  color="primary"
                  @click="validateForm()"
                >
                  {{ buttonText }}
                </v-btn>
              </div>
            </div>
          </v-col>
        </v-card>
      </template>
    </v-overlay>
  </div>
</template>

<script>
import { checkNullValue } from "@/helper";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import {
  GET_TALENT_POOL_LIST,
  MOVE_TO_ANOTHER_TALENT_POOL,
} from "@/graphql/recruitment/recruitmentQueries.js";

export default {
  name: "MoveToOtherTalentPoolForm",
  components: {
    CustomSelect,
  },
  props: {
    candidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    candidateId: {
      type: Number,
      required: true,
    },
    candidateIdSelected: {
      type: Number,
      required: true,
    },
    jobTitle: {
      default: "",
      type: String,
    },
    selectedCandidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    originalStatusList: {
      type: Array,
      required: true,
    },
    currentTalentPoolId: {
      type: Number,
      required: true,
    },
  },
  mixins: [validationRules],
  emits: ["close-talent-pool-overlay-form", "refetch-talent-pool-list"],

  data: () => ({
    isFormDirty: false,
    showConfirmation: false,
    overlay: true,
    isLoading: false,
    notificationTime: null,
    itemTalentList: [],
    selectedTalentPoolId: null,
    archiveStatusId: null,
    buttonText: "Submit",
  }),

  mounted() {
    this.fetchTalentPoolList();
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgDetails() {
      return this.$store.state.orgDetails;
    },
    companyName() {
      const { organizationName } = this.$store.state.orgDetails;
      return organizationName;
    },
    loginEmployeeDetails() {
      return this.$store.state.orgDetails.userDetails;
    },
  },
  methods: {
    checkNullValue,
    deductFormChange() {
      this.isFormDirty = true;
    },
    async validateForm() {
      let { valid } = await this.$refs.ArchiveForm.validate();
      if (valid) {
        this.moveCandidateToTalentPool();
      }
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    closeWindow(isSuccess) {
      this.$emit("close-talent-pool-overlay-form", isSuccess);
      this.overlay = true;
    },
    fetchTalentPoolList() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_TALENT_POOL_LIST,
          client: "apolloClientAY",
          variables: {
            formId: 297,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getTalentPoolList &&
            !response.data.getTalentPoolList.errorCode
          ) {
            const filteredData =
              response.data.getTalentPoolList.talentPoolListData;
            vm.itemTalentList = filteredData.filter(
              (record) => record.talentPoolId !== this.currentTalentPoolId
            );
            vm.isLoading = false;
          } else {
            vm.itemTalentList = [];
            vm.selectedTalentPoolId = null;
            vm.isLoading = false;
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleTalentPoolListError(err);
        });
    },
    handleTalentPoolListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "talent pool list",
        isListError: false,
      });
    },
    moveCandidateToTalentPool() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: MOVE_TO_ANOTHER_TALENT_POOL,
          client: "apolloClientAV",
          variables: {
            candidateId: parseInt(vm.candidateId),
            formId: 297,
            talentPoolId: vm.selectedTalentPoolId,
          },
          fetchPolicy: "no-cache",
        })
        .then(() => {
          vm.isLoading = false;
          vm.closeWindow();
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: "Job candidate moved to other talent pool successfully.",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-talent-pool-list");
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.overlay = true;
          vm.handleMoveCandidateToTalentPoolError(err);
        });
    },
    handleMoveCandidateToTalentPoolError(err = "") {
      this.closeWindow();
      this.$emit("refetch-talent-pool-list");
      this.isLoading = false;
      this.trackingStatusLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "adding",
        form: "candidate to talent pool",
        isListError: false,
      });
    },
  },
};
</script>

<style scoped>
.overlay {
  height: 100% !important;
}
</style>
