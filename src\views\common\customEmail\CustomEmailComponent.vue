<template>
  <div class="mt-3">
    <v-overlay
      v-if="isOverlay"
      v-model="showOverlay"
      class="d-flex justify-end overlay-content-parent"
      @click:outside="this.$emit('custom-email-cancel')"
    >
      <template v-slot:default>
        <div class="overlay-card">
          <div class="overlay-body pa-3">
            <v-form ref="customEmailForm">
              <div>
                <v-text-field
                  v-model="senderName"
                  counter="100"
                  variant="solo"
                  :rules="[
                    required('Sender Name', senderName),
                    validateWithRulesAndReturnMessages(
                      senderName,
                      'senderName',
                      'Sender Name'
                    ),
                  ]"
                  ><template v-slot:label
                    ><span>Sender Name</span>
                    <span class="ml-1" style="color: red">*</span></template
                  ></v-text-field
                >
                <v-text-field
                  v-model="subject"
                  variant="solo"
                  :rules="[
                    required('Subject', subject),
                    validateWithRulesAndReturnMessages(
                      subject,
                      'subjectContent',
                      'Subject'
                    ),
                  ]"
                  ref="subject"
                  ><template v-slot:label
                    ><span>Subject</span>
                    <span class="ml-1" style="color: red">*</span></template
                  ></v-text-field
                >
                <v-autocomplete
                  v-if="
                    typeOfTemplate &&
                    typeOfTemplate.toLowerCase() !== 'hiringforecastsettings' &&
                    this.noCustomTemplate
                  "
                  v-model="emailsToSend"
                  color="secondary"
                  :items="templateEmail"
                  :label="
                    emailsToSend.length > 0 || ccEmailRecievers.length > 0
                      ? 'To'
                      : ''
                  "
                  readonly
                  multiple
                  solo
                  chips
                  :closable-chips="closeableChips"
                  :single-line="
                    emailsToSend.length > 0 || ccEmailRecievers.length > 0
                      ? false
                      : true
                  "
                ></v-autocomplete>
                <div v-else>
                  <div
                    v-if="
                      !isEditable && emailsToSend && emailsToSend?.length > 4
                    "
                    class="mb-6"
                  >
                    <v-card class="pa-5">
                      <v-row class="d-flex align-center">
                        <div
                          v-for="Emp_Detail in emailsToSend.slice(0, 4)"
                          :key="Emp_Detail"
                          class="py-1 mx-1"
                        >
                          <v-chip size="small"> {{ Emp_Detail }} </v-chip>
                        </div>
                        <v-chip
                          size="small"
                          color="primary"
                          v-if="emailsToSend.length > 4"
                          style="cursor: pointer; font-weight: bold"
                          @click="isEditable = !isEditable"
                        >
                          {{ emailsToSend.length - 4 }} + more
                        </v-chip>
                      </v-row>
                    </v-card>
                  </div>
                  <v-autocomplete
                    v-else-if="emailsToSend?.length > 0"
                    v-model="emailsToSend"
                    color="secondary"
                    :label="emailsToSend.length > 0 ? 'To' : ''"
                    menu-icon=""
                    readonly
                    multiple
                    solo
                    chips
                    :closable-chips="closeableChips"
                    item-title="Emp_Detail"
                    item-value="Emp_Detail"
                    :single-line="emailsToSend.length > 0 ? false : true"
                  >
                    <template v-slot:append>
                      <v-slide-x-reverse-transition
                        mode="out-in"
                        v-if="emailsToSend && emailsToSend.length > 4"
                      >
                        <v-icon
                          color="primary"
                          size="20"
                          @click="isEditable = !isEditable"
                        >
                          fas fa-times
                        </v-icon>
                      </v-slide-x-reverse-transition>
                    </template></v-autocomplete
                  >
                </div>
                <v-autocomplete
                  v-if="emailCCField?.length > 0"
                  v-model="emailCCField"
                  menu-icon=""
                  color="secondary"
                  label="Cc"
                  readonly
                  multiple
                  solo
                  chips
                ></v-autocomplete>
                <v-autocomplete
                  v-if="emailToField?.length > 0"
                  v-model="emailToField"
                  menu-icon=""
                  color="secondary"
                  label="Bcc"
                  readonly
                  multiple
                  solo
                  chips
                ></v-autocomplete>
              </div>
              <div
                class="text-subtitle-1 text-grey-darken-4 pb-2"
                v-if="
                  typeOfTemplate &&
                  typeOfTemplate.toLowerCase() === 'hiringforecastsettings'
                "
              >
                Do you want to add additional email?
                <CustomSelect
                  label="Additional email address"
                  v-model="selectedAdditionalEmail"
                  :itemSelected="selectedAdditionalEmail"
                  :items="additionEmail"
                  item-value="Emp_Email"
                  item-title="Emp_Detail"
                  :selectProperties="{
                    multiple: true,
                    chips: true,
                    closableChips: true,
                    clearable: true,
                  }"
                  :single-line="true"
                  placeholder="Additional email address"
                  :isAutoComplete="true"
                  variant="solo"
                ></CustomSelect>
              </div>
              <v-autocomplete
                v-if="
                  !this.noCustomTemplate && selectedAdditionalEmail?.length > 0
                "
                v-model="selectedAdditionalEmail"
                color="secondary"
                label="Additional email address"
                readonly
                multiple
                solo
                chips
              ></v-autocomplete>
              <div class="d-flex mt-n6">
                <p>
                  <span class="v-label"> Add additional external emails</span>
                </p>
              </div>
              <v-text-field
                ref="additionalExternalEmails"
                v-model="input"
                @update:modelValue="showAddIcon"
                variant="solo"
                :rules="[
                  input
                    ? validateWithRulesAndReturnMessages(
                        input,
                        'empEmail',
                        'Additional External Emails'
                      )
                    : true,
                ]"
                @keydown.enter.prevent="addChip"
              >
                <template v-slot:default>
                  <v-icon v-if="showIcon" @click="addChip" size="x-small"
                    >fas fa-plus</v-icon
                  >
                  <v-chip
                    v-for="(chip, index) in additionalExternalEmails"
                    append-icon="fas fa-times-circle"
                    :key="index"
                    class="ma-1"
                    @click="removeChip(index)"
                    >{{ chip }}</v-chip
                  >
                </template>
              </v-text-field>

              <VueEditor
                v-model="htmlContent"
                :editor-options="editorOptions"
                @updated-content="handleUpdatedContent($event)"
              />
            </v-form>
          </div>
          <v-card class="overlay-footer py-2" elevation="16">
            <div class="d-flex justify-space-between w-100">
              <div>
                <v-btn
                  v-if="typeOfTemplate == 'hiringForecastSettings'"
                  class="ml-5 secondary"
                  variant="text"
                  elevation="2"
                  rounded="lg"
                  @click="this.$emit('custom-email-cancel')"
                >
                  <span class="primary"> Cancel </span>
                </v-btn>
              </div>
              <div>
                <v-btn
                  class="mr-5 secondary"
                  variant="text"
                  elevation="2"
                  @click="
                    typeOfTemplate == 'hiringForecastSettings'
                      ? this.$emit('without-custom-email')
                      : this.$emit('custom-email-cancel')
                  "
                  rounded="lg"
                >
                  <span class="primary">{{
                    typeOfTemplate == "hiringForecastSettings"
                      ? "Skip & Continue"
                      : "Cancel"
                  }}</span>
                </v-btn>
                <v-btn
                  class="secondary"
                  variant="elevated"
                  rounded="lg"
                  :disabled="
                    typeOfTemplate &&
                    typeOfTemplate.toLowerCase() === 'hiringforecastsettings'
                      ? emailsToSend &&
                        emailsToSend.length === 0 &&
                        selectedAdditionalEmail &&
                        selectedAdditionalEmail.length === 0
                      : false
                  "
                  @click="validateCustomEmailForm()"
                >
                  <span class="primary"> {{ submitText }} </span>
                </v-btn>
              </div>
            </div>
          </v-card>
          <AppLoading v-if="isLoading"></AppLoading>
        </div>
      </template>
    </v-overlay>
    <v-form v-else ref="customEmailForm">
      <div>
        <v-text-field
          v-model="senderName"
          counter="100"
          variant="solo"
          :rules="[
            required('Sender Name', senderName),
            validateWithRulesAndReturnMessages(
              senderName,
              'senderName',
              'Sender Name'
            ),
          ]"
          ><template v-slot:label
            ><span>Sender Name</span>
            <span class="ml-1" style="color: red">*</span></template
          ></v-text-field
        >
        <v-text-field
          v-model="subject"
          variant="solo"
          :rules="[
            required('Subject', subject),
            validateWithRulesAndReturnMessages(
              subject,
              'subjectContent',
              'Subject'
            ),
          ]"
          ref="subject"
          ><template v-slot:label
            ><span>Subject</span>
            <span class="ml-1" style="color: red">*</span></template
          ></v-text-field
        >
      </div>
      <div
        v-if="!isEditable && emailsToSend && emailsToSend?.length > 4"
        class="mb-6"
      >
        <v-card class="pa-5">
          <v-row class="d-flex align-center">
            <div
              v-for="Emp_Detail in emailsToSend.slice(0, 4)"
              :key="Emp_Detail"
              class="py-1 mx-1"
            >
              <v-chip size="small"> {{ Emp_Detail }} </v-chip>
            </div>
            <v-chip
              size="small"
              color="primary"
              v-if="emailsToSend.length > 4"
              style="cursor: pointer; font-weight: bold"
              @click="isEditable = !isEditable"
            >
              {{ emailsToSend.length - 4 }} + more
            </v-chip>
          </v-row>
        </v-card>
      </div>
      <v-autocomplete
        v-else-if="emailsToSend?.length > 0"
        v-model="emailsToSend"
        color="secondary"
        :label="emailsToSend.length > 0 ? 'To' : ''"
        menu-icon=""
        readonly
        multiple
        solo
        chips
        :closable-chips="closeableChips"
        item-title="Emp_Detail"
        item-value="Emp_Detail"
        :single-line="emailsToSend.length > 0 ? false : true"
      >
        <template v-slot:append>
          <v-slide-x-reverse-transition
            mode="out-in"
            v-if="emailsToSend && emailsToSend.length > 4"
          >
            <v-icon color="primary" size="20" @click="isEditable = !isEditable">
              fas fa-times
            </v-icon>
          </v-slide-x-reverse-transition>
        </template></v-autocomplete
      >
      <v-autocomplete
        v-if="emailCCField?.length > 0"
        v-model="emailCCField"
        menu-icon=""
        color="secondary"
        label="Cc"
        readonly
        multiple
        solo
        chips
      ></v-autocomplete>
      <v-autocomplete
        v-if="emailToField?.length > 0"
        v-model="emailToField"
        menu-icon=""
        color="secondary"
        label="Bcc"
        readonly
        multiple
        solo
        chips
      ></v-autocomplete>
      <v-autocomplete
        v-if="!this.noCustomTemplate && selectedAdditionalEmail?.length > 0"
        v-model="selectedAdditionalEmail"
        color="secondary"
        label="Additional email address"
        readonly
        multiple
        solo
        chips
      ></v-autocomplete>
      <div class="d-flex">
        <p>
          <span class="v-label"> Add additional external emails</span>
        </p>
      </div>
      <v-text-field
        ref="additionalExternalEmails"
        v-model="input"
        @update:modelValue="showAddIcon"
        variant="solo"
        :rules="[
          input
            ? validateWithRulesAndReturnMessages(
                input,
                'empEmail',
                'Additional External Emails'
              )
            : true,
        ]"
        @keydown.enter.prevent="addChip"
      >
        <template v-slot:default>
          <v-icon v-if="showIcon" @click="addChip" size="x-small"
            >fas fa-plus</v-icon
          >
          <v-chip
            v-for="(chip, index) in additionalExternalEmails"
            append-icon="fas fa-times-circle"
            :key="index"
            class="ma-1"
            @click="removeChip(index)"
            >{{ chip }}</v-chip
          >
        </template>
      </v-text-field>
      <VueEditor
        v-model="htmlContent"
        :editor-options="editorOptions"
        @updated-content="handleUpdatedContent($event)"
      />
      <AppLoading v-if="isLoading"></AppLoading>
    </v-form>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>

<script>
import VueEditor from "./VueEditor.vue";
import moment from "moment";
import validationRules from "@/mixins/validationRules";
import { SEND_CUSTOM_EMAIL } from "@/graphql/recruitment/recruitmentQueries.js";
import {
  recruitmentEmailTemplates,
  replacementTags,
} from "./recruitmentEmailTemplates";
import { LIST_EMAIL_TEMPLATE_PLACEHOLDER_VALUES } from "@/graphql/settings/email-template/emailTemplateQueries.js";
export default {
  name: "CustomEmailComponent",
  components: {
    VueEditor,
  },
  emits: ["custom-email-sent", "custom-email-cancel", "without-custom-email"],
  mixins: [validationRules],
  props: {
    typeOfTemplate: {
      type: String,
      required: true,
    },
    typeOfSchedule: {
      type: String,
      required: true,
    },
    templateEmail: {
      type: Array,
      required: true,
    },
    templateData: {
      type: Object,
      required: true,
    },
    isOverlay: {
      type: Boolean,
      default: false,
    },
    sendDateAlone: {
      type: [Boolean, Number],
      default: false,
    },
    selectedCandidateId: {
      type: String,
      required: false,
    },
    formId: {
      type: Number,
      required: true,
    },
    submitText: {
      type: String,
      default: "Send Email",
    },
    emailFullData: {
      type: Array,
      required: false,
      default: () => {
        return [];
      },
    },
    additionalEmail: {
      type: Array,
      required: false,
      default: () => {
        return [];
      },
    },
    emailRecievers: {
      type: Array,
      required: false,
      default: () => {
        return [];
      },
    },
    closeableChips: {
      type: Boolean,
      default: false,
    },
    toCCExchange: {
      type: Boolean,
      required: false,
      default: () => false,
    },
    ccEmailRecievers: {
      type: Array,
      required: false,
      default: () => {
        return [];
      },
    },
    notificationTimeNow: {
      type: Boolean,
      default: true,
    },
    emailTemplateList: {
      type: Array,
      required: true,
      default: () => {
        return [];
      },
    },
    selectedEmailTemplate: {
      type: Number,
      required: true,
    },
    noCustomTemplate: {
      type: Boolean,
      default: false,
    },
  },
  data: () => ({
    isLoading: false,
    subject: "",
    htmlContent: "",
    editorOptions: {
      theme: "snow",
    },
    showOverlay: false,
    emailsToSend: [],
    emailToField: [],
    emailCCField: [],
    showValidationAlert: false,
    validationMessages: [],
    tempEmailList: [],
    additionEmail: [],
    selectedAdditionalEmail: null,
    isEditable: false,
    emailTemplatedData: null,
    placeholderValues: null,
    templateContent: "",
    subjectContent: "",
    mergedEmails: [],
    documentList: [],
    fileObjects: [],
    noPlaceholderFound: false,
    senderName: "",
    isContentPresent: false,
    input: "",
    additionalExternalEmails: [],
    showIcon: false,
    candidateResume: "",
    coverLetter: [],
  }),
  watch: {
    additionalEmail: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length) {
          this.additionEmail = newVal;
        }
      },
    },
  },
  computed: {
    calendarStartTime() {
      if (
        this.templateData &&
        this.templateData.Start_Time &&
        this.templateData.Start_Time.length
      ) {
        let startTime =
          moment(this.templateData.Date).format("YYYY-MM-DD") +
          " " +
          this.templateData.Start_Time;
        const localTime = moment(startTime, "YYYY-MM-DD HH:mm");
        const utcTime = localTime.utc().format("YYYY-MM-DD HH:mm");
        return utcTime;
      }
      return "";
    },
    isInputValid() {
      const rules = [
        this.validateWithRulesAndReturnMessages(
          this.input,
          "empEmail",
          "Additional External Emails"
        ),
      ];
      return rules[0] === true ? true : false;
    },
    calendarEndTime() {
      if (
        this.templateData &&
        this.templateData.End_Time &&
        this.templateData.End_Time.length
      ) {
        let endTime =
          moment(this.templateData.Date).format("YYYY-MM-DD") +
          " " +
          this.templateData.End_Time;
        const localTime = moment(endTime, "YYYY-MM-DD HH:mm");
        const utcTime = localTime.utc().format("YYYY-MM-DD HH:mm");
        return utcTime;
      }
      return "";
    },
    typeSchedule() {
      if (this.typeOfSchedule) {
        if (this.typeOfSchedule.toLowerCase() === "noncalendar") {
          return "candidateScheduled";
        } else if (
          this.typeOfSchedule.toLowerCase() === "sunfishdeploymentemail"
        ) {
          return "sunfishdeploymentemail";
        } else if (
          this.typeOfSchedule.toLowerCase() === "newhirenotification"
        ) {
          return "newHireNotification";
        } else {
          return "interviewerScheduled";
        }
      }
      return "interviewerScheduled";
    },
    formattedFileName() {
      return (fileName) => {
        if (fileName) {
          return fileName.split("?")[3];
        }
      };
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
  },
  async mounted() {
    if (this.isOverlay) {
      this.showOverlay = true;
    }
    if (this.noCustomTemplate) {
      if (this.emailRecievers) {
        let emailTo = this.emailRecievers
          .flatMap((email) => email.split(","))
          .filter((email) => email.trim() !== "");
        this.emailToField = [...new Set(emailTo)];
      }
      if (this.templateEmail) {
        let allEmails = this.templateEmail
          .flatMap((email) => email.split(","))
          .filter((email) => email.trim() !== "");
        this.emailsToSend = [...new Set(allEmails)];
        this.tempEmailList = this.emailsToSend;
      }
      if (this.ccEmailRecievers) {
        let emailCC = this.ccEmailRecievers
          .flatMap((email) => email.split(","))
          .filter((email) => email.trim() !== "");
        this.emailCCField = [...new Set(emailCC)];
      }
      if (
        this.emailFullData &&
        this.typeOfTemplate &&
        this.typeOfTemplate.toLowerCase() === "hiringforecastsettings"
      ) {
        this.emailsToSend = this.emailFullData.map((email) => email.Emp_Detail);
        this.tempEmailList = this.emailFullData;
      }
      this.formEmail();
    } else {
      this.emailTemplatedData = this.getTemplateById(
        this.selectedEmailTemplate,
        this.emailTemplateList
      );
      this.templateContent = this.emailTemplatedData.Template_Content;
      this.subjectContent = this.emailTemplatedData.Subject_Content;
      this.fetchEmailTemplatePlaceholderValues();
    }
    this.getDropdownDetails();
  },
  methods: {
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async validateCustomEmailForm() {
      if (this.additionalExternalEmails?.length) {
        this.mergedEmails = [
          ...this.mergedEmails,
          ...this.additionalExternalEmails,
        ];
      }
      [
        this.emailsToSend,
        this.emailToField,
        this.mergedEmails,
        this.selectedAdditionalEmail,
      ] = this.removeDuplicateEmails([
        this.emailsToSend,
        this.emailToField,
        this.mergedEmails,
        this.selectedAdditionalEmail,
      ]);
      const { valid } = await this.$refs.customEmailForm.validate();
      if (valid && !this.noPlaceholderFound && this.isContentPresent) {
        //Check if there is data in text editor
        if (this.htmlContent && this.htmlContent.length) {
          if (this.notificationTimeNow) {
            await this.sendCustomEmail();
          } else {
            this.$emit("custom-email-sent", this.htmlContent);
          }
          return true;
        } else {
          let snackbarData = {
            isOpen: true,
            message:
              "Text editor is empty. Please provide some text before proceeding.",
            type: "warning",
          };
          this.showAlert(snackbarData);
          return false;
        }
      } else if (this.noPlaceholderFound) {
        let snackbarData = {
          isOpen: true,
          message:
            "Some placeholders are not replaced, kindly replace or remove them before proceeding.",
          type: "warning",
        };
        this.showAlert(snackbarData);
        this.noPlaceholderFound = false;
        return false;
      } else {
        return false;
      }
    },
    showAddIcon() {
      this.showIcon = !!this.input.trim();
    },
    addChip() {
      if (this.isInputValid && this.input.trim()) {
        this.additionalExternalEmails.push(this.input.trim());
        this.input = "";
        this.showIcon = false;
      }
    },
    removeChip(index) {
      this.additionalExternalEmails.splice(index, 1);
    },
    // Function to replace tags with corresponding values
    replaceTags(template, replacementTags, templateData) {
      for (const [tag, replacement] of Object.entries(replacementTags)) {
        if (
          template.includes(tag) &&
          templateData.hasOwnProperty(replacement)
        ) {
          // Check if tag exists in template and replacement data exists
          template = template.replace(
            new RegExp("\\" + tag, "g"),
            templateData[replacement]
          );
        }
      }
      return template;
    },
    replaceTemplateKeys(template, values) {
      return template.replace(/{{(.*?)}}/g, (match, key) => {
        if (key in values) {
          return values[key] ? values[key] : "";
        }
        this.noPlaceholderFound = true;
        return match;
      });
    },

    formEmail() {
      if (this.noCustomTemplate) {
        if (recruitmentEmailTemplates[this.typeOfTemplate]) {
          this.subject = this.replaceTags(
            recruitmentEmailTemplates[this.typeOfTemplate].subject,
            replacementTags,
            this.templateData
          );
          this.htmlContent = this.replaceTags(
            recruitmentEmailTemplates[this.typeOfTemplate].body,
            replacementTags,
            this.templateData
          );
        }
      } else {
        if (
          this.templateContent?.length &&
          this.placeholderValues &&
          Object.keys(this.placeholderValues).length
        ) {
          this.placeholderValues.venue = this.templateData?.Venue_Link
            ? `<a href="${this.templateData?.Venue_Link}">${this.templateData?.Venue}</a></p>`
            : this.templateData?.Venue
            ? this.templateData?.Venue
            : "";
          this.placeholderValues.interviewDate = this.templateData?.Date
            ? this.templateData?.Date
            : "";
          this.placeholderValues.interviewStartTime =
            this.templateData?.Date && this.templateData?.Start_Time
              ? this.templateData?.Date + " " + this.templateData?.Start_Time
              : "";
          this.placeholderValues.interviewEndTime = this.templateData?.End_Time
            ? " to " + this.templateData?.End_Time + " (24hr Format)"
            : "";
          this.placeholderValues.calendarLink = this.templateData?.Calendar_Link
            ? `<a href="${this.templateData?.Calendar_Link}">Calendar Link</a>`
            : "";
          this.placeholderValues.assessmentLink =
            this.typeOfTemplate?.toLowerCase() === "candidateassessment" &&
            this.templateData?.Calendar_Link
              ? `<a href="${this.templateData?.Calendar_Link}">Assessment Link</a>`
              : "";
          this.placeholderValues.archiveReason = this.templateData
            ?.Archive_Reason
            ? this.templateData?.Archive_Reason
            : "";
          this.placeholderValues.candidateProfileLink = `<a href="${this.$store.getters.baseUrl}v3/recruitment/job-candidates?candidateId=${this.selectedCandidateId}">Candidate Profile</a>`;
          this.placeholderValues.onboardingUrl = this.templateData?.Url
            ? `<a href="${this.templateData?.Url}">Onboarding Url</a>`
            : "";
          this.placeholderValues.companyLogo = this.placeholderValues
            ?.companyLogoUrl
            ? `<div style="font-size:1px;line-height:25px"> </div><img alt="Image" border="0" class="fixedwidth" src="${this.placeholderValues?.companyLogoUrl}" style="text-decoration: none; -ms-interpolation-mode: bicubic; border: 0; height: auto; width: 100%; max-width: 208px; display: block;" title="Image" width="208"/>`
            : "";
          const attachmentsHTML = this.fileObjects
            .map(
              (file) =>
                `<a href="${file.s3Link}" target="_blank">${file.fileName}</a>`
            )
            .join("");
          this.templateContent = this.templateContent.replace(
            "<ul> attachmentsList </ul>",
            attachmentsHTML
          );
          this.htmlContent = this.replaceTemplateKeys(
            this.templateContent,
            this.placeholderValues
          );
        }
        if (
          this.subjectContent?.length &&
          this.placeholderValues &&
          Object.keys(this.placeholderValues).length
        ) {
          this.subject = this.replaceTemplateKeys(
            this.subjectContent,
            this.placeholderValues
          );
        }
      }
    },
    async sendCustomEmail(customData = null, url) {
      let vm = this;
      vm.isLoading = true;
      if (!customData) {
        customData = {
          formId: vm.formId,
          typeOfInterview: "onlineinterview",
          typeOfSchedule: vm.typeSchedule,
          bccEmails: vm.noCustomTemplate
            ? vm.toCCExchange
              ? vm.emailRecievers
              : vm.emailsToSend
            : vm.emailToField,
          toMailIds: vm.noCustomTemplate
            ? vm.toCCExchange
              ? vm.emailsToSend
              : vm.emailRecievers
            : vm.emailsToSend,
          ccEmails: vm.noCustomTemplate ? vm.ccEmailRecievers : vm.mergedEmails,
          subject: vm.subject,
          htmlContent: vm.convertAllQuillAlignments(vm.htmlContent),
          startDateTime: vm.calendarStartTime,
          endDateTime: vm.calendarEndTime,
          location: vm.templateData?.Location,
          description: null,
        };
      } else {
        if (recruitmentEmailTemplates[customData.typeOfTemplate]) {
          customData.subject = vm.replaceTags(
            recruitmentEmailTemplates[customData.typeOfTemplate].subject,
            replacementTags,
            customData.templateData
          );
          let htmlContent = vm.replaceTags(
            recruitmentEmailTemplates[customData.typeOfTemplate].body,
            replacementTags,
            customData.templateData
          );
          customData.htmlContent = vm.convertAllQuillAlignments(htmlContent);
        }
        if (customData?.templateData?.Date) {
          if (vm.sendDateAlone || !vm.templateData.Start_Time) {
            customData.startDateTime = moment(vm.templateData.Date).format(
              "YYYY-MM-DD"
            );
          } else if (vm.templateData.Start_Time) {
            let startTime =
              moment(this.templateData.Date).format("YYYY-MM-DD") +
              " " +
              this.templateData.Start_Time;
            const localTime = moment(startTime, "YYYY-MM-DD HH:mm");
            const utcTime = localTime.utc().format("YYYY-MM-DD HH:mm");
            customData.startDateTime = utcTime;
          }
        }
        if (customData?.templateData?.Date) {
          if (vm.sendDateAlone || !vm.templateData.End_Time) {
            customData.endDateTime = moment(vm.templateData.Date).format(
              "YYYY-MM-DD"
            );
          } else if (vm.templateData.End_Time) {
            let endTime =
              moment(this.templateData.Date).format("YYYY-MM-DD") +
              " " +
              this.templateData.End_Time;
            const localTime = moment(endTime, "YYYY-MM-DD HH:mm");
            const utcTime = localTime.utc().format("YYYY-MM-DD HH:mm");
            customData.endDateTime = utcTime;
          }
        }
      }
      customData["templateName"] = vm.noCustomTemplate
        ? vm.templateData?.emailTemplateType || null
        : null;
      customData["candidateId"] = vm.selectedCandidateId
        ? Array.isArray(vm.selectedCandidateId)
          ? vm.selectedCandidateId[0]
          : parseInt(vm.selectedCandidateId)
        : null;
      customData["senderName"] = vm.senderName;
      if (
        vm.typeOfTemplate &&
        vm.typeOfTemplate.toLowerCase() === "hiringforecastsettings"
      ) {
        customData["bccEmails"] = [];
        vm.tempEmailList.forEach((email) => {
          if (vm.emailsToSend.includes(email.Emp_Detail)) {
            customData["bccEmails"].push(email.Emp_Email);
          }
        });
        if (vm.selectedAdditionalEmail && vm.selectedAdditionalEmail.length) {
          customData["bccEmails"] = [
            ...new Set([
              ...customData["bccEmails"],
              ...vm.selectedAdditionalEmail,
            ]),
          ];
        }
      }
      if (url) {
        let htmlContent = customData.htmlContent.replace("[Meeting Link]", url);
        customData.htmlContent = vm.convertAllQuillAlignments(htmlContent);
      }
      await vm.$apollo
        .mutate({
          mutation: SEND_CUSTOM_EMAIL,
          variables: customData,
          client: "apolloClientAQ",
        })
        .then(() => {
          vm.isLoading = false;
          vm.$emit("custom-email-sent", vm.htmlContent);
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleError(err);
        });
    },
    handleError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          isListError: false,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.showValidationAlert = true;
          this.$emit("custom-email-cancel");
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    getTemplateById(templateId, templatesArray) {
      return templatesArray.find(
        (template) => template.Template_Id === templateId
      );
    },
    async fetchEmailTemplatePlaceholderValues() {
      let vm = this;
      vm.isLoading = true;
      vm.candidateResume = "";
      vm.coverLetter = [];
      await vm.$apollo
        .query({
          query: LIST_EMAIL_TEMPLATE_PLACEHOLDER_VALUES,
          variables: {
            candidateId: Array.isArray(vm.selectedCandidateId)
              ? vm.selectedCandidateId[0]
              : parseInt(vm.selectedCandidateId),
            templateId: vm.selectedEmailTemplate,
            accessformId: vm.formId,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then(async (response) => {
          if (
            response?.data &&
            response.data.listEmailTemplatePlaceHolderValues
          ) {
            vm.mergedEmails = [];
            let emailList =
              response.data.listEmailTemplatePlaceHolderValues?.emails;
            vm.emailsToSend = emailList?.toEmails;
            vm.tempEmailList = vm.emailsToSend;
            vm.emailToField = emailList.bccEmails;
            vm.emailCCField = emailList.ccEmails;
            vm.selectedAdditionalEmail = emailList.additionalEmails;
            let toEmails = this.emailTemplatedData?.To_Emails;
            let ccEmails = this.emailTemplatedData?.CC_Emails;
            let bccEmails = this.emailTemplatedData?.Bcc_Emails;
            let additionalEmails = this.emailTemplatedData?.Additional_Emails;
            if (this.templateData?.Panel_Members_Emails) {
              // For future use
              // vm.candidateResume = this.templateData?.Resume;
              // vm.coverLetter = this.templateData?.Other_Attachments;
              if (toEmails?.includes("Panel Member")) {
                this.emailsToSend.push(
                  ...this.templateData.Panel_Members_Emails
                );
              }
              if (ccEmails?.includes("Panel Member")) {
                this.emailCCField.push(
                  ...this.templateData.Panel_Members_Emails
                );
              }
              if (bccEmails?.includes("Panel Member")) {
                this.emailToField.push(
                  ...this.templateData.Panel_Members_Emails
                );
              }
              if (additionalEmails?.includes("Panel Member")) {
                this.selectedAdditionalEmail.push(
                  ...this.templateData.Panel_Members_Emails
                );
              }
            }
            vm.mergedEmails = [
              ...(vm.emailCCField.length > 0 ? vm.emailCCField : []),
              ...(vm.selectedAdditionalEmail.length > 0
                ? vm.selectedAdditionalEmail
                : []),
            ];
            vm.placeholderValues =
              response.data.listEmailTemplatePlaceHolderValues?.jobCandidate;
            let mailAttachments = this.emailTemplatedData?.Attachments;
            let attachmentList = mailAttachments
              ? JSON.parse(mailAttachments)
              : [];
            let candidateResumeDetails = [];
            let formattedList = [];
            let coverLetterDetails = [];
            if (this.candidateResume) {
              candidateResumeDetails = [
                {
                  folderName: "resume",
                  fileName: this.candidateResume,
                },
              ];
            }
            if (this.coverLetter?.length) {
              coverLetterDetails = this.coverLetter.map((fileName) => ({
                folderName: "otherAttachment",
                fileName: fileName.File_Name,
              }));
            }
            formattedList = attachmentList.map((fileName) => ({
              folderName: "Email Template Document Upload",
              fileName: fileName,
            }));
            this.documentList = [
              ...candidateResumeDetails,
              ...coverLetterDetails,
              ...formattedList,
            ];
            if (this.documentList?.length) {
              this.templateContent +=
                "<br><p><strong>Attachments:</strong></p><ul> attachmentsList </ul><p><br></p>";
              for (let doc in this.documentList) {
                await this.retrieveFileContents(doc);
              }
            }
            vm.formEmail();
          }
          vm.isLoading = false;
        })
        .catch(() => {
          vm.emailsToSend = [];
          vm.tempEmailList = [];
          vm.emailToField = [];
          vm.emailCCField = [];
          vm.selectedAdditionalEmail = [];
          vm.mergedEmails = [];
          vm.isLoading = false;
        });
    },
    async retrieveFileContents(doc) {
      let vm = this;
      vm.isLoading = true;
      let fileName = "";
      fileName = this.formattedFileName(this.documentList[doc]?.fileName);
      if (
        this.documentList[doc]?.folderName === "otherAttachment" ||
        this.documentList[doc]?.folderName === "resume"
      ) {
        const baseName =
          this.documentList[doc]?.folderName === "resume"
            ? "resume"
            : "cover_letter";
        fileName = [
          baseName,
          this.templateData.First_Name,
          this.templateData.Last_Name,
          this.selectedCandidateId,
        ]
          .filter(Boolean)
          .join("_");
      }
      let fullFilePath =
        this.domainName +
        (this.documentList[doc]?.folderName?.toLowerCase() !==
        "email template document upload"
          ? "_"
          : "") + // Add "_" only if folderName is "resume" or "otherAttachment"
        "/" +
        this.orgCode +
        "/" +
        this.documentList[doc]?.folderName +
        "/" +
        this.documentList[doc]?.fileName;
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fullFilePath,
          action: "download",
          type: "documents",
        })
        .then((presignedUrl) => {
          this.fileObjects.push({
            fileFormat: this.documentList[doc]?.fileName,
            fileName: fileName,
            s3Link: presignedUrl, // Assigning S3 link dynamically
          });
          vm.isLoading = false;
        })
        .catch(() => {
          vm.isLoading = false;
        });
    },
    removeDuplicateEmails(emailGroups) {
      let uniqueEmails = new Set();

      return emailGroups.map((group) => {
        return group?.filter((email) => {
          if (uniqueEmails.has(email)) {
            return false; // Remove duplicates
          }
          uniqueEmails.add(email);
          return true;
        });
      });
    },
    async getDropdownDetails() {
      let vm = this;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;

      await vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            formId: vm.formId,
            key: ["email_notification_setting"],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            const [senderName] = tempData.map((item) => item?.data || []);
            vm.senderName = vm.emailTemplatedData?.Sender_Name || null;
            let senderNameArray = senderName;
            let emailSenderName = senderNameArray?.length
              ? senderNameArray[0].Sender_Name
              : "HRAPP Notfication";
            vm.senderName = vm.senderName || emailSenderName;
          } else {
            vm.senderName = "HRAPP Notfication";
          }
        })
        .catch(() => {
          vm.senderName = "HRAPP Notfication";
        });
    },
    /**
     * Converts Quill editor HTML to email-compatible HTML
     * Fixes formatting issues like misaligned lists and removes editor artifacts
     * @param {string} html - Raw HTML content from Quill editor
     * @returns {string} - Cleaned and email-compatible HTML
     */
    convertAllQuillAlignments(html) {
      // Safety check: Return original content if input is invalid
      if (!html || typeof html !== "string") {
        return html;
      }

      // Create a working copy of the HTML content
      let converted = html;

      // ==========================================
      // STEP 1: CLEAN UP RICH TEXT EDITOR ARTIFACTS
      // ==========================================
      converted = converted
        // Remove empty Quill UI spans that cause display issues in emails
        // Example: <span class="ql-ui" contenteditable="false"></span> → (removed)
        .replace(/<span class="ql-ui"[^>]*><\/span>/g, "")

        // Remove Quill UI spans with content (rare but can happen)
        // Example: <span class="ql-ui">some content</span> → (removed)
        .replace(/<span class="ql-ui"[^>]*>.*?<\/span>/g, "")

        // Remove contenteditable attributes that are not needed in emails
        // Example: contenteditable="false" → (removed)
        .replace(/contenteditable="[^"]*"/g, "")

        // Clean up any extra whitespace
        .trim();

      // ==========================================
      // STEP 2: CONVERT PARAGRAPH ALIGNMENTS
      // ==========================================
      // Convert Quill CSS classes to inline styles for email compatibility
      converted = converted
        // Convert center-aligned paragraphs
        // FROM: <p class="ql-align-center">
        // TO:   <p style="text-align: center; margin:0;">
        .replace(
          /<p class="ql-align-center">/g,
          '<p style="text-align: center; margin:0;">'
        )

        // Convert right-aligned paragraphs
        // FROM: <p class="ql-align-right">
        // TO:   <p style="text-align: right; margin:0;">
        .replace(
          /<p class="ql-align-right">/g,
          '<p style="text-align: right; margin:0;">'
        )

        // Convert justified paragraphs
        // FROM: <p class="ql-align-justify">
        // TO:   <p style="text-align: justify; margin:0;">
        .replace(
          /<p class="ql-align-justify">/g,
          '<p style="text-align: justify; margin:0;">'
        )

        // Convert left-aligned paragraphs
        // FROM: <p class="ql-align-left">
        // TO:   <p style="text-align: left; margin:0;">
        .replace(
          /<p class="ql-align-left">/g,
          '<p style="text-align: left; margin:0;">'
        );

      // ==========================================
      // STEP 3: FIX LIST STRUCTURE (SMART CONVERSION)
      // ==========================================
      // Only convert <ol> to <ul> if it contains bullet list items
      // This prevents converting actual numbered lists to bullet lists

      // Find all <ol>...</ol> blocks and check their content
      converted = converted.replace(
        /<ol>([\s\S]*?)<\/ol>/g,
        (match, content) => {
          // Check if this <ol> contains bullet list items
          if (content.includes('data-list="bullet"')) {
            // This <ol> contains bullet items, convert to <ul>
            // FROM: <ol>...bullet items...</ol>
            // TO:   <ul style="...">...bullet items...</ul>
            return (
              '<ul style="text-align: center; margin: 15px auto; padding-left: 15px; max-width: 600px; list-style-position: outside;">' +
              content +
              "</ul>"
            );
          } else {
            // This <ol> contains actual ordered items, keep as <ol> but add styling
            // FROM: <ol>...ordered items...</ol>
            // TO:   <ol style="...">...ordered items...</ol>
            return (
              '<ol style="text-align: center; margin: 15px auto; padding-left: 15px; max-width: 600px; list-style-position: outside;">' +
              content +
              "</ol>"
            );
          }
        }
      );

      // ==========================================
      // STEP 4: FIX LIST ITEM ALIGNMENTS
      // ==========================================
      // Convert list items to use proper inline styles
      // Key fix: Use text-align: left for list items to prevent bullets/numbers from appearing on extreme left
      converted = converted

        // ---- BULLET LIST ITEMS ----
        // Handle center-aligned bullet list items
        // FROM: <li data-list="bullet" class="ql-align-center">
        // TO:   <li style="text-align: left; margin: 8px 0; padding: 4px 0;">
        // Note: We use 'left' alignment to fix the bullet positioning issue
        .replace(
          /<li data-list="bullet" class="ql-align-center">/g,
          '<li style="text-align: left; margin: 8px 0; padding: 4px 0;">'
        )

        // Handle right-aligned bullet list items
        // FROM: <li data-list="bullet" class="ql-align-right">
        // TO:   <li style="text-align: right; margin: 8px 0; padding: 4px 0;">
        .replace(
          /<li data-list="bullet" class="ql-align-right">/g,
          '<li style="text-align: right; margin: 8px 0; padding: 4px 0;">'
        )


        // Handle left-aligned bullet list items
        // FROM: <li data-list="bullet" class="ql-align-left">
        // TO:   <li style="text-align: left; margin: 8px 0; padding: 4px 0;">
        .replace(
          /<li data-list="bullet" class="ql-align-left">/g,
          '<li style="text-align: left; margin: 8px 0; padding: 4px 0;">'
        )

        // Handle any remaining bullet list items (catch-all)
        // This covers cases where alignment class might be missing
        // FROM: <li data-list="bullet" [any other attributes]>
        // TO:   <li style="text-align: left; margin: 8px 0; padding: 4px 0;">
        .replace(
          /<li data-list="bullet"[^>]*>/g,
          '<li style="text-align: left; margin: 8px 0; padding: 4px 0;">'
        )

        // ---- ORDERED LIST ITEMS ----
        // Handle center-aligned ordered list items
        // FROM: <li data-list="ordered" class="ql-align-center">
        // TO:   <li style="text-align: left; margin: 8px 0; padding: 4px 0;">
        // Note: We use 'left' alignment to fix the number positioning issue
        .replace(
          /<li data-list="ordered" class="ql-align-center">/g,
          '<li style="text-align: left; margin: 8px 0; padding: 4px 0;">'
        )

        // Handle right-aligned ordered list items
        // FROM: <li data-list="ordered" class="ql-align-right">
        // TO:   <li style="text-align: right; margin: 8px 0; padding: 4px 0;">
        .replace(
          /<li data-list="ordered" class="ql-align-right">/g,
          '<li style="text-align: right; margin: 8px 0; padding: 4px 0;">'
        )
        .replace(
          /<li data-list="ordered" class="ql-align-justify">/g,
          '<li style="text-align: justify; margin: 8px 0; padding: 4px 0;">'
        )

        // Handle left-aligned ordered list items
        // FROM: <li data-list="ordered" class="ql-align-left">
        // TO:   <li style="text-align: left; margin: 8px 0; padding: 4px 0;">
        .replace(
          /<li data-list="ordered" class="ql-align-left">/g,
          '<li style="text-align: left; margin: 8px 0; padding: 4px 0;">'
        )

        // Handle any remaining ordered list items (catch-all)
        // This covers cases where alignment class might be missing
        // FROM: <li data-list="ordered" [any other attributes]>
        // TO:   <li style="text-align: left; margin: 8px 0; padding: 4px 0;">
        .replace(
          /<li data-list="ordered"[^>]*>/g,
          '<li style="text-align: left; margin: 8px 0; padding: 4px 0;">'
        );

      // Return the fully processed and email-compatible HTML
      return converted;
    },
    handleUpdatedContent(isContentPresent) {
      this.isContentPresent = isContentPresent;
    },
  },
};
</script>
<style scoped>
.overlay-content-parent > .v-overlay__content {
  width: 40%;
}
@media screen and (max-width: 960px) {
  .overlay-content-parent > .v-overlay__content {
    width: 100%;
  }
}

.overlay-card {
  height: 100%;
  width: 100%;
  background: white;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.overlay-body {
  height: calc(100vh - 90px);
  overflow-y: scroll !important;
  overflow: hidden;
}
.overlay-footer {
  height: 7%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
}
:deep(.ql-snow .ql-tooltip) {
  left: 0px !important;
}
:deep(.ql-editor) {
  min-height: 300px !important;
}
</style>
