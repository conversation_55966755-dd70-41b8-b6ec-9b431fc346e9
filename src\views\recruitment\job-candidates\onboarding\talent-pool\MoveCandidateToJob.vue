<template>
  <div class="text-center">
    <v-overlay
      v-if="overlay"
      v-model="overlay"
      class="d-flex justify-end overlay"
      persistent
      @click:outside="closeWindow()"
    >
      <template v-slot:default>
        <v-card
          rounded="lg"
          :style="
            isMobileView
              ? 'width:100vw; height: 100vh;'
              : 'width:35vw; height: 100vh;'
          "
        >
          <!-- Card Title -->
          <v-card-title
            class="d-flex justify-space-between align-center bg-primary"
          >
            <div class="text-h6">Move Candidate to Job</div>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="closeWindow(true)"
              color="white"
            ></v-btn>
          </v-card-title>

          <!-- Loading Spinner -->
          <v-card-text v-if="isLoading" class="d-flex justify-center">
            <AppLoading />
          </v-card-text>

          <!-- Form Content -->
          <v-card-text v-else class="overflow-y-auto" style="max-height: 85vh">
            <v-form
              ref="MoveCandidateForm"
              @submit.prevent="moveCandidateToJobPost()"
            >
              <v-row class="px-sm-4 px-md-6 pt-sm-4">
                <!-- Job Select Dropdown -->
                <v-col cols="12" class="pl-sm-4 pl-md-6" style="height: 100px">
                  <CustomSelect
                    :items="jobPostList"
                    v-model="selectedJobPostId"
                    label="Job Title"
                    :isLoading="isJobPostListLoading"
                    itemValue="Job_Post_Id"
                    itemTitle="Job_Post_Name"
                    :isAutoComplete="true"
                    ref="selectedJobPostId"
                    :isRequired="true"
                    :rules="[required('Job Title', selectedJobPostId)]"
                  ></CustomSelect>
                </v-col>

                <!-- New Stage Select Dropdown -->
                <v-col cols="12" class="pl-sm-4 pl-md-6" style="height: 100px">
                  <CustomSelect
                    :items="stageList"
                    :itemSelected="selectedStage"
                    label="Stages"
                    :isloading="stageLoading"
                    itemValue="value"
                    itemTitle="value"
                    ref="selectedStage"
                    :isAutoComplete="true"
                    :isRequired="true"
                    :rules="[required('Stages', selectedStage)]"
                    @selected-item="onSelectStage($event)"
                  ></CustomSelect>
                </v-col>

                <!-- Status Select Dropdown -->
                <v-col cols="12" class="pl-sm-4 pl-md-6">
                  <CustomSelect
                    :items="candidateStatusList"
                    label="Status"
                    itemValue="Id"
                    itemTitle="Status"
                    ref="newStatus"
                    :isRequired="true"
                    :isAutoComplete="true"
                    :itemSelected="newStatus"
                    @selected-item="newStatus = $event"
                    :rules="[required('Status', newStatus)]"
                  ></CustomSelect>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>

          <v-col cols="12" class="d-flex justify-end pr-4">
            <div
              class="d-flex justify-end pa-2 position-absolute"
              style="bottom: 0"
            >
              <v-btn
                rounded="lg"
                variant="outlined"
                class="mr-4"
                @click="closeWindow(true)"
              >
                Cancel
              </v-btn>

              <div class="mr-1">
                <v-btn
                  rounded="lg"
                  variant="elevated"
                  color="primary"
                  v-if="!showSubmitButton"
                  @click="validateForm()"
                >
                  Submit
                </v-btn>
              </div>
            </div>
          </v-col>
        </v-card>
      </template>
    </v-overlay>
    <v-card-text v-if="isLoading" class="d-flex justify-center">
      <AppLoading />
    </v-card-text>
  </div>
</template>

<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import {
  LIST_JOB_POSTS,
  GET_STATUS_LIST,
  MOVE_TO_JOB_POST,
} from "@/graphql/recruitment/recruitmentQueries.js";

export default {
  name: "MoveCandidateToJob",
  components: {
    CustomSelect,
  },
  props: {
    candidateDetails: {
      type: Object,
      default: () => ({}),
    },
    candidateId: {
      type: Number,
      required: true,
    },
    candidateIdSelected: {
      type: Number,
      required: true,
    },
    jobTitle: {
      default: "",
      type: String,
    },
    selectedCandidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    currentTalentPoolId: {
      type: Number,
      required: true,
    },
  },
  mixins: [validationRules],
  emits: ["form-updated", "refetch-talent-pool-list"],

  data: () => ({
    overlay: true,
    isLoading: false,
    selectedJobPostId: null,
    selectedStage: null,
    newStatus: null, // Added newStatus variable
    jobPostList: [],
    stageList: [],
    candidateStatusList: [],
    flowList: [],
    stageLoading: false,
    isJobPostListLoading: false,
  }),

  watch: {
    candidateDetails(val) {
      this.selectedStage = val.Hiring_Stage;
      this.candidateStatusList = this.flowList[val.Hiring_Stage];
    },
  },

  mounted() {
    this.retrieveJobPosts();
    this.getStageList();
  },

  methods: {
    getStageList() {
      this.stageLoading = true;
      this.$apollo
        .query({
          query: GET_STATUS_LIST,
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
          variables: {
            formId: 297,
            conditions: [
              {
                key: "Visible_During_Job_Transaction",
                value: ["Yes"],
              },
            ],
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getAtsStatusList &&
            response.data.getAtsStatusList.statusList
          ) {
            let tempData = response.data.getAtsStatusList.statusList;
            let groupedByStage = tempData.reduce((acc, item) => {
              if (!acc[item.Stage]) {
                acc[item.Stage] = [];
                this.stageList[item.Stage_Id - 1] = {
                  id: item.Stage_Id,
                  value: item.Stage,
                };
              }
              acc[item.Stage].push(item);
              return acc;
            }, {});
            this.flowList = groupedByStage;
            this.candidateStatusList = groupedByStage["Sourced"] || []; // Ensuring fallback in case "Sourced" is not available
          } else {
            this.stageList = [];
          }
          this.stageLoading = false;
        })
        .catch((err) => {
          this.stageLoading = false;
          this.handleRetrieveHiringFlow(err);
        });
    },
    handleRetrieveHiringFlow(err = "") {
      this.stageLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "stages list",
        isListError: false,
      });
    },
    retrieveJobPosts() {
      let vm = this;
      vm.isJobPostListLoading = true;
      vm.$apollo
        .query({
          query: LIST_JOB_POSTS,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            searchString: "",
            designation: null,
            functionalArea: null,
            jobType: null,
            closingDate: "",
            status: null,
            location: [],
            isDropDownCall: 1,
            skills: [],
            qualification: [],
            action: "add",
            formId: 16,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listJobPost &&
            !response.data.listJobPost.errorCode.length
          ) {
            vm.jobPostList = response.data.listJobPost.JobpostDetails;
          }
          vm.isJobPostListLoading = false;
        })
        .catch(() => {
          vm.isJobPostListLoading = false;
          vm.jobPostList = [];
        });
    },

    async validateForm() {
      let { valid } = await this.$refs.MoveCandidateForm.validate();
      if (valid) {
        this.moveCandidateToJobPost();
      }
    },

    async moveCandidateToJobPost() {
      let vm = this;
      try {
        vm.isLoading = true;
        const response = await vm.$apollo.mutate({
          mutation: MOVE_TO_JOB_POST,
          client: "apolloClientAV",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: vm.candidateId,
            jobPostId: vm.selectedJobPostId,
            status: vm.newStatus,
            formId: 297,
            talentPoolId: vm.currentTalentPoolId,
          },
        });

        if (response && response.data && response.data.moveToJobPost) {
          const { errorCode, validationError } = response.data.moveToJobPost;

          if (!errorCode && !validationError) {
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Candidate moved successfully.",
            };
            vm.showAlert(snackbarData);
            vm.overlay = false;
            vm.$emit("refetch-talent-pool-list");
            vm.$emit("form-updated");
            vm.closeWindow(true);
          } else {
            vm.handleMoveCandidateError();
          }
        } else {
          vm.handleMoveCandidateError();
        }
      } catch (err) {
        vm.handleMoveCandidateError(err);
      } finally {
        vm.isLoading = false;
        this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      }
    },

    handleMoveCandidateError(err = "") {
      this.isLoading = false;
      this.overlay = false;
      this.closeWindow();
      this.$emit("refetch-talent-pool-list");
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "moving candidate",
        form: "talent pool",
        isListError: false,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    closeWindow(isSuccess) {
      this.$emit("close-move-candidate-window", isSuccess);
      this.overlay = true;
    },
    onSelectStage(stage) {
      // this.$refs.candidateStatusRef.selectedItem = null;
      this.selectedStage = stage;
      this.candidateStatusList = this.flowList[stage];
      this.newStatus = null;
    },
  },
};
</script>

<style scoped>
.overlay {
  height: 100% !important;
}

.status-container {
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
