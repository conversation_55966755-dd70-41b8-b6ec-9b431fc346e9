<template>
  <div>
    <v-dialog
      :model-value="modelValue"
      fullscreen
      scrollable
      transition="dialog-bottom-transition"
    >
      <v-card>
        <v-toolbar>
          <CustomSelect
            v-model="carouselIndex"
            class="mx-2 mt-5"
            :items="sectionTitles"
            itemTitle="title"
            itemValue="value"
            label="Changes"
            :isRequired="true"
            :itemSelected="carouselIndex"
            :disableBreak="true"
          ></CustomSelect>
          <v-spacer></v-spacer>
          <v-btn color="success" @click="$emit('approve-reject', 'approve')"
            ><v-icon size="18" class="mt-1 mr-1">fas fa-check-circle</v-icon
            ><span v-if="!isMobileView">Approve</span></v-btn
          >
          <v-btn color="error" @click="$emit('approve-reject', 'reject')"
            ><v-icon size="18" class="mt-1 mr-1">fas fa-times-circle</v-icon
            ><span v-if="!isMobileView">Reject</span></v-btn
          >
          <v-btn icon @click="$emit('update:modelValue', false)">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-toolbar>
        <v-carousel
          v-model="carouselIndex"
          :hide-delimiters="!isMobileView"
          progress="primary"
          :show-arrows="!isMobileView ? 'hover' : false"
          height="90vh"
          class="pa-2"
        >
          <v-carousel-item v-if="personalDetails">
            <div style="height: 100%; overflow-y: auto">
              <PersonalDetails
                ref="editPersonalDetails"
                :personalDetailsData="personalDetails.New_Data"
                :oldPersonalDetailsData="personalDetails.Old_Data"
              />
            </div>
          </v-carousel-item>
          <v-carousel-item v-if="jobDetails">
            <div style="height: 100%; overflow-y: auto">
              <JobDetails
                ref="editJobDetails"
                :jobDetailsData="jobDetails.New_Data"
                :oldJobDetailsData="jobDetails.Old_Data"
              />
            </div>
          </v-carousel-item>
        </v-carousel>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
import PersonalDetails from "../../employee-profile/profile-details/personal/details/PersonalDetails.vue";
import JobDetails from "../../employee-profile/profile-details/job/details/JobDetails.vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
export default {
  name: "EmployeeApprovalChanges",
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    instanceData: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    PersonalDetails,
    JobDetails,
    CustomSelect,
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  mounted() {
    let requestItems = this.instanceData.Request_Items;
    for (let index = 0; index < requestItems.length; index++) {
      const element = requestItems[index];
      if (element.Table_Name?.toLowerCase() === "emp_personal_info") {
        this.personalDetails = {
          New_Data: JSON.parse(element.New_Data),
          Old_Data: JSON.parse(element.Old_Data),
        };
        this.sectionTitles.push({
          title: "Personal Details",
          value: index,
        });
      }
      if (element.Table_Name?.toLowerCase() === "emp_job") {
        this.jobDetails = {
          New_Data: JSON.parse(element.New_Data),
          Old_Data: JSON.parse(element.Old_Data),
        };
        this.sectionTitles.push({
          title: "Job Details",
          value: index,
        });
      }
    }
  },
  data() {
    return {
      carouselIndex: 0,
      sectionTitles: [],
      personalDetails: null,
      jobDetails: null,
    };
  },
};
</script>

<style scoped>
:deep(.v-window__controls .v-btn--icon) {
  opacity: 0.5;
  transition: opacity 0.2s;
  background-color: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
  min-width: 0 !important;
  width: auto !important;
  height: auto !important;
  border-radius: 0 !important;
}

:deep(.v-window__controls .v-btn--icon:hover),
:deep(.v-window__controls .v-btn--icon:focus) {
  opacity: 1;
  background-color: transparent !important;
  box-shadow: none !important;
}

:deep(.v-window__controls .v-btn--icon .v-icon) {
  color: #260029 !important; /* Or your preferred color */
  font-size: 2rem; /* Optional: adjust icon size */
}
</style>
