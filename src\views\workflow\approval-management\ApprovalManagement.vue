<template>
  <div>
    <AppTopBarTab
      :tabs-list="mainTabList"
      :tab-count="tabCountList"
      :current-tab="currentTabItem"
      :center-tab="true"
      @tab-clicked="onTabChange($event)"
      :showBottomSheet="
        !listLoading &&
        ((approvalListBackup.length > 0 &&
          approvalType !== 'Approval History') ||
          (approvalHistoryListBackup.length > 0 &&
            approvalType === 'Approval History'))
      "
    >
      <template #topBarContent>
        <v-row
          v-show="
            (approvalListBackup.length > 0 &&
              approvalType !== 'Approval History') ||
            (approvalHistoryListBackup.length > 0 &&
              approvalType === 'Approval History')
          "
          justify="center"
        >
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end"
              :isFilter="false"
            ></EmployeeDefaultFilterMenu>
            <FormFilter
              :items="
                approvalType === 'Approval History'
                  ? approvalHistoryListBackup
                  : approvalListBackup
              "
              :reset-filter-count="resetFilterCount"
              :apply-filter-count="applyFilterCount"
              :filterFormId="filterFormId"
              :approval-type="approvalType"
              @reset-filter="resetFilter()"
              @apply-filter="applyFilter($event)"
              @selected-approval-by="filteredApprovalBy = $event"
            ></FormFilter>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
    <v-container v-if="!isLoading" fluid class="approvals-container">
      <v-window
        v-model="currentTabItem"
        id="approvals-tab"
        v-if="approvalManagementFormAccess"
      >
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <div v-else-if="isErrorInList">
            <AppFetchErrorScreen
              :image-name="
                leaveWorkflowEnabled && filterFormId === 31
                  ? 'common/human-error-image'
                  : 'layout/access-denied'
              "
              :content="errorContent"
              icon-name="fas fa-redo-alt"
              :button-text="showRetryBtn ? 'Retry' : ''"
              @button-click="refetchUserTaskAndCountAPIs('errorRetry')"
            >
            </AppFetchErrorScreen>
          </div>
          <v-row v-else>
            <v-col :cols="showViewForm && windowWidth >= 1264 ? 5 : 12">
              <ListApprovals
                :items="
                  approvalType === 'Approval History'
                    ? approvalHistoryList
                    : approvalList
                "
                :isSmallTable="showViewForm && windowWidth >= 1264"
                :filterFormId="filterFormId"
                :approval-type="approvalType"
                :original-list="
                  approvalType === 'Approval History'
                    ? approvalHistoryListBackup
                    : approvalListBackup
                "
                :enforceLeaveApprovalComment="enforceLeaveApprovalComment"
                :leaveWorkflowEnabled="leaveWorkflowEnabled"
                :reimbursementWorkflowEnabled="reimbursementWorkflowEnabled"
                :lateAttendanceNoOnlySelected="lateAttendanceNoOnlySelected"
                :filteredApprovalDate="filteredApprovalDate"
                :invoiceApproveCount="invoiceApproveCount"
                :invoiceRejectCount="invoiceRejectCount"
                @show-invoice-modal="onShowInvoiceModal($event)"
                @show-timesheet-modal="onShowTimesheetModal($event)"
                @show-salary-revision-modal="onShowSalaryRevisionModal($event)"
                @on-change-approval-date="filteredApprovalDate = $event"
                @on-change-form="onChangeForm($event)"
                @on-status-update="refetchUserTaskAndCountAPIs('statusUpdate')"
                @reset-filter="resetFilterCount += 1"
                @on-select-item="openViewForm($event)"
                @open-task-level="openTaskLevelInViewForm($event)"
                @refetch-list="reloadGrid()"
                @reset-approval-date="setApprovalDateFilterRange()"
              ></ListApprovals>
            </v-col>
            <v-col v-if="showViewForm && windowWidth >= 1264" cols="7">
              <ViewApprovals
                :approval-details="selectedApprovalDetails"
                :filterFormId="filterFormId"
                :is-any-admin="checkAnyOneAdmin"
                :approval-type="approvalType"
                :show-task-level="showTaskLevel"
                @show-salary-revision-modal="onShowSalaryRevisionModal($event)"
                @show-invoice-modal="onShowInvoiceModal($event)"
                @show-timesheet-modal="onShowTimesheetModal($event)"
                @close-view-form="closeViewForm()"
                @update-success="handleEditSuccess()"
              ></ViewApprovals>
            </v-col>
          </v-row>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <v-dialog
      v-model="openFormInModal"
      width="900"
      @click:outside="closeViewForm()"
    >
      <ViewApprovals
        :approval-details="selectedApprovalDetails"
        :filterFormId="filterFormId"
        :is-any-admin="checkAnyOneAdmin"
        :approval-type="approvalType"
        :show-task-level="showTaskLevel"
        @show-salary-revision-modal="onShowSalaryRevisionModal($event)"
        @show-invoice-modal="onShowInvoiceModal($event)"
        @show-timesheet-modal="onShowTimesheetModal($event)"
        @close-view-form="closeViewForm()"
        @update-success="handleEditSuccess()"
      ></ViewApprovals>
    </v-dialog>
  </div>
  <ViewSalaryRevision
    v-if="showSalaryRevisionModal"
    :showForm="true"
    :formId="360"
    :payrollCurrency="payrollCurrency"
    formName="Salary Revision"
    :selected-item="selectedSalaryRevisionItem"
    @close-form="
      (showSalaryRevisionModal = false), (selectedSalaryRevisionItem = null)
    "
  />
  <ViewInvoiceDetails
    v-if="showInvoiceDetailsModal"
    :requestId="invoiceRequestId"
    :processInstanceId="invoiceProcessInstanceId"
    :approval-type="approvalType"
    @close-modal="closeInvoiceModal()"
    @approve-task="invoiceApproveCount += 1"
    @reject-task="invoiceRejectCount += 1"
    @approval-amount-updated="isApprovalAmountUpdated = true"
  ></ViewInvoiceDetails>
  <v-dialog
    v-model="showTimesheetDetailsModal"
    width="1400"
    @click:outside="showTimesheetDetailsModal = false"
    ><v-card>
      <v-card-title>
        <div class="d-flex" style="width: 100%">
          Timesheet Details
          <v-spacer></v-spacer>
          <v-icon color="primary" @click="showTimesheetDetailsModal = false"
            >fas fa-times</v-icon
          >
        </div>
      </v-card-title>
      <v-card-text
        :style="`
          max-height: calc(100vh - 240px);
          overflow: scroll;
        `"
      >
        <EmpTimeSheets
          :formId="23"
          :employeeId="selectedTimesheetEmpId"
          action="approval"
          :approvalStatus="selectedTimesheetStatus"
          :weekRangeSelected="timesheetWeekEndDate"
        ></EmpTimeSheets>
      </v-card-text>
    </v-card>
  </v-dialog>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import Config from "@/config.js";
import axios from "axios";
import moment from "moment";
// queries
import {
  GET_EMPLOYEE_GROUP_IDS,
  GET_SERVICE_PROVIDER_EMPLOYEES,
} from "@/graphql/workflow/approvalManagementQueries.js";
import { LIST_LEAVE_TYPES } from "@/graphql/dropDownQueries";
import { GET_LEAVE_SETTINGS } from "@/graphql/settings/core-hr/leaveSettingsQueries.js";
import { GET_REIMBURSEMENT_SETTINGS } from "@/graphql/settings/payroll/reimbursementSettingsQueries.js";
// components
import FormFilter from "./FormFilter.vue";
import ListApprovals from "./ListApprovals.vue";
const ViewApprovals = defineAsyncComponent(() => import("./ViewApprovals.vue"));
const ViewInvoiceDetails = defineAsyncComponent(() =>
  import("./ViewInvoiceDetails.vue")
);
const EmpTimeSheets = defineAsyncComponent(() =>
  import("../../employee-self-service/timesheets/Timesheets.vue")
);
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu";
import ViewSalaryRevision from "@/views/coreHr/payroll-data-management/salary-info/ViewForm.vue";

export default defineComponent({
  name: "ApprovalManagement",

  components: {
    EmployeeDefaultFilterMenu,
    ListApprovals,
    FormFilter,
    ViewApprovals,
    ViewInvoiceDetails,
    EmpTimeSheets,
    ViewSalaryRevision,
  },

  data() {
    return {
      // tab
      currentTabItem: "tab-0",
      mainTabList: [],
      isLoading: true,
      approvalList: [],
      approvalListBackup: [],
      approvalHistoryList: [],
      approvalHistoryListBackup: [],
      approvalHistoryRetrieved: [],
      approvalsRetrieved: [],
      approvalHistoryCallCount: 0,
      approvalListCallCount: 0,
      listLoading: false,
      filterFormId: null,
      approvalType: "",
      groupId: [],
      serviceProviderEmployees: ["-"],
      isGroupIdsFetched: false,
      isServiceProviderEmpsFetched: false,
      isErrorInList: false,
      errorContent: "",
      resetFilterCount: 0,
      applyFilterCount: 0,
      leaveTypeList: [],
      leaveWorkflowEnabled: false,
      enforceLeaveApprovalComment: false,
      reimbursementWorkflowEnabled: false,
      showRetryBtn: true,
      apiCallType: "",
      approvalApiLimit: 200,
      lateAttendanceNoOnlySelected: false,
      filteredApprovalBy: "Assigned For Me",
      filteredApprovalDate: "",
      showInvoiceDetailsModal: false,
      invoiceRequestId: 0,
      invoiceProcessInstanceId: "",
      isApprovalAmountUpdated: false,
      showTimesheetDetailsModal: false,
      timesheetWeekEndDate: "",
      selectedTimesheetEmpId: 0,
      selectedTimesheetStatus: "",
      showSalaryRevisionModal: false,
      selectedSalaryRevisionItem: null,
      // forms
      selectedApprovalDetails: {},
      showViewForm: false,
      openFormInModal: false,
      showTaskLevel: false,
      // counts
      groupDependencyApiCallCount: 0,
      taskCountDependApiCallCount: 0,
      userTaskCountApiCallCount: 0,
      myApprovalsCount: 0,
      groupApprovalsCount: 0,
      outstandingApprovalsCount: 0,
      settingsAPICallCount: 0,
      invoiceApproveCount: 0,
      invoiceRejectCount: 0,
      statusApprovalCount: 0,
    };
  },

  computed: {
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    approvalManagementFormAccess() {
      let formAccess = this.accessRights("approval-management");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights.view
      ) {
        return formAccess.accessRights;
      } else return false;
    },
    allAdminAccess() {
      return this.$store.getters.checkAndReturnOneOfAdminAccess;
    },
    checkAnyOneAdmin() {
      return this.allAdminAccess.isAnyOneFormHaveAccess;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    isServiceProviderAdmin() {
      let serviceProviderAdminAccess = this.accessRights(
        "service-provider-admin"
      );
      if (
        serviceProviderAdminAccess &&
        serviceProviderAdminAccess.accessRights &&
        serviceProviderAdminAccess.accessRights.update
      ) {
        return true;
      }
      return false;
    },
    tabCountList() {
      if (this.checkAnyOneAdmin) {
        return [
          this.myApprovalsCount,
          this.groupApprovalsCount,
          this.outstandingApprovalsCount,
          0,
        ];
      } else {
        return [this.myApprovalsCount, this.groupApprovalsCount, 0];
      }
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date, withTime = false) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        if (withTime) orgDateFormat = orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "-";
      };
    },
    formatDateTime() {
      return (date) => {
        if (!date) return "";
        if (moment(date).isValid()) {
          let orgDateFormat =
            this.$store.state.orgDetails.orgDateFormat + " HH:mm";
          return moment(date).format(orgDateFormat);
        }
        return "";
      };
    },
    filteredApprovalStartAndEndDate() {
      if (this.filteredApprovalDate) {
        if (this.filteredApprovalDate.includes("to")) {
          let splittedDate = this.filteredApprovalDate.split(" to ");
          return {
            startDate: splittedDate[0],
            endDate: splittedDate[1],
          };
        } else {
          return {
            startDate: this.filteredApprovalDate,
            endDate: this.filteredApprovalDate,
          };
        }
      } else {
        return {
          startDate: "",
          endDate: "",
        };
      }
    },
    formatTotalHours() {
      return (totalHours) => {
        if (!totalHours) {
          return "00 Hrs 00 Mins";
        }

        // Parse the total hours value (could be decimal or HH:MM format)
        let hours = 0;
        let minutes = 0;

        // Check if it's in HH:MM format
        if (typeof totalHours === "string" && totalHours.includes(":")) {
          [hours, minutes] = totalHours.split(":").map(Number);
        } else {
          // Assume it's a decimal value
          const totalHoursDecimal = parseFloat(totalHours) || 0;
          hours = Math.floor(totalHoursDecimal);
          minutes = Math.round((totalHoursDecimal - hours) * 60);
        }

        // Format as "XX Hours YY Minutes"
        return `${hours.toString().padStart(2, "0")} Hrs ${minutes
          .toString()
          .padStart(2, "0")} Mins`;
      };
    },
  },

  mounted() {
    this.setApprovalDateFilterRange();
    // check access for approval-management form
    if (this.approvalManagementFormAccess) {
      // check any one of employee/payroll/service-provider/ admin or super admin, if yes add 'outstanding approvals'
      if (this.checkAnyOneAdmin) {
        this.approvalType = "My Approvals";
        this.mainTabList = [
          "My Approvals",
          "Group Approvals",
          "Outstanding Approvals",
          "Approval History",
        ];
      } else {
        this.approvalType = "My Approvals";
        // not an admin but having view access
        this.mainTabList = [
          "My Approvals",
          "Group Approvals",
          "Approval History",
        ];
      }
      this.getLeaveSettings();
      this.getReimbursementSettings();
    } else {
      this.mainTabList = ["Approval Management"];
      this.isLoading = false;
    }
  },

  watch: {
    settingsAPICallCount(count) {
      if (count >= 2) {
        this.isLoading = false;
      }
    },
    groupDependencyApiCallCount(count) {
      if (count === 2 && this.approvalType === "Group Approvals") {
        this.retrieveUserTask("Group Approvals");
        this.isLoading = false;
      }
    },
    taskCountDependApiCallCount(count) {
      if (count >= 2) {
        this.retrieveMyAndGroupApprovalsCount();
        this.retrieveOutstandingApprovalsCount();
      }
    },
    userTaskCountApiCallCount(count) {
      if (count === 2) {
        this.isLoading = false;
      }
    },
    filterFormId() {
      this.refetchUserTaskAndCountAPIs("formChange");
    },
    approvalHistoryListBackup(items) {
      if (
        items &&
        items.length > 0 &&
        this.filteredApprovalBy === "Assigned For Me"
      ) {
        let allHistoryItems = items;
        if (this.approvalType === "Approval History") {
          this.approvalHistoryList = allHistoryItems.filter((item) => {
            return item.assignee == this.loginEmployeeId;
          });
        }
      }
    },
    filteredApprovalDate() {
      if (this.approvalType === "Approval History") {
        this.approvalHistoryList = [];
        this.approvalHistoryListBackup = [];
        this.approvalHistoryRetrieved = [];
        this.approvalHistoryCallCount = 0;
        this.retrieveUserTaskHistory();
      }
    },
  },

  errorCaptured(err, vm, info) {
    console.error("err", err);
    let url = window.location.href;
    let msg =
      "Something went wrong while loading the approval management form. Please try after some time.";
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },

  methods: {
    setApprovalDateFilterRange() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      let currentDate = moment().format(orgDateFormat);
      let currentDateMinus31Days = moment()
        .subtract("31", "days")
        .format(orgDateFormat);
      this.filteredApprovalDate = currentDateMinus31Days + " to " + currentDate;
    },
    onTabChange(tabName) {
      this.resetFormValues();
      if (tabName !== "Approval History") {
        this.leaveTypeList = [];
      }
      for (let tab = 0; tab < this.mainTabList.length; tab++) {
        if (this.mainTabList[tab] === tabName) {
          this.currentTabItem = "tab-" + tab;
          this.approvalType = tabName;
          this.refetchUserTaskAndCountAPIs("tabChange");
          break;
        }
      }
    },

    resetFormValues() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.resetFilterCount = 0;
      this.applyFilterCount = 0;
      this.approvalList = [];
      this.approvalListBackup = [];
      this.approvalHistoryList = [];
      this.approvalHistoryListBackup = [];
      this.approvalHistoryRetrieved = [];
      this.approvalsRetrieved = [];
      this.approvalHistoryCallCount = 0;
      this.approvalListCallCount = 0;
      this.isErrorInList = false;
      this.errorContent = "";
      this.userTaskCountApiCallCount = 0;
      this.closeViewForm();
    },

    openViewForm(selectedItem) {
      this.showTaskLevel = false;
      this.selectedApprovalDetails = selectedItem;
      if (this.windowWidth < 1264) {
        this.openFormInModal = true;
      } else {
        this.showViewForm = true;
      }
    },

    openTaskLevelInViewForm(selectedItem) {
      this.showTaskLevel = true;
      this.selectedApprovalDetails = selectedItem;
      if (this.windowWidth < 1264) {
        this.openFormInModal = true;
      } else {
        this.showViewForm = true;
      }
    },

    closeViewForm() {
      this.openFormInModal = false;
      this.showViewForm = false;
      this.showTaskLevel = false;
      this.selectedApprovalDetails = {};
    },

    closeInvoiceModal() {
      this.showInvoiceDetailsModal = false;
      if (this.isApprovalAmountUpdated) {
        this.refetchUserTaskAndCountAPIs("approvalAmountUpdated");
        this.isApprovalAmountUpdated = false;
      }
    },

    onChangeForm(formId) {
      this.resetFilterCount += 1;
      this.filterFormId = formId;
    },

    onShowInvoiceModal(params) {
      this.invoiceRequestId = params[0];
      this.invoiceProcessInstanceId = params[1];
      this.showInvoiceDetailsModal = true;
    },

    onShowTimesheetModal(params) {
      this.selectedTimesheetEmpId = params[0];
      this.selectedTimesheetStatus = params[2];
      let weekEndDate = params[1];
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      const weekStart = moment(weekEndDate)
        .clone()
        .startOf("week")
        .format(orgDateFormat);
      const weekEnd = moment(weekEndDate).format(orgDateFormat);
      this.timesheetWeekEndDate = weekStart + " to " + weekEnd;
      this.showTimesheetDetailsModal = true;
    },
    onShowSalaryRevisionModal(params) {
      if (!params?.instanceData?.id) return;
      params.Revision_Id = params?.instanceData?.id;
      params.Employee_Name = params?.employeeName;
      params.User_Defined_EmpId = params?.userDefinedEmpId;
      params.Effective_Month = this.convertToMonth(
        params?.instanceData?.salaryEffectiveMonth
      );
      this.selectedSalaryRevisionItem = params;
      this.showSalaryRevisionModal = true;
    },
    convertToMonth(value) {
      if (!value) return "";
      return moment(value, "M,YYYY").isValid()
        ? moment(value, "M,YYYY").format("MMM YYYY")
        : "";
    },

    resetFilter() {
      if (this.approvalType === "Approval History") {
        if (this.filteredApprovalBy === "Assigned For Me") {
          let allHistoryItems = this.approvalHistoryListBackup;
          this.approvalHistoryList = allHistoryItems.filter((item) => {
            return item.assignee == this.loginEmployeeId;
          });
        }
      } else {
        this.approvalList = this.approvalListBackup;
      }
      this.resetFilterCount = 0;
      this.applyFilterCount = 0;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },

    applyFilter(filterParams) {
      if (filterParams[1] === "manual") {
        this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      }
      if (this.approvalType === "Approval History") {
        this.approvalHistoryList = filterParams[0];
      } else {
        this.approvalList = filterParams[0];
      }
      this.lateAttendanceNoOnlySelected = filterParams[2];
    },

    getLeaveSettings() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_LEAVE_SETTINGS,
          variables: {
            formId: 184,
          },
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveLeaveSettings
          ) {
            const { leaveSettings } = response.data.retrieveLeaveSettings;
            vm.leaveWorkflowEnabled = leaveSettings.Enable_Workflow === "Yes";
            vm.enforceLeaveApprovalComment =
              leaveSettings.Enforce_Comment_For_Approval === "Yes";
            vm.handleLeaveSettingsApiRes();
          } else {
            vm.handleLeaveSettingsApiRes();
          }
        })
        .catch(() => {
          vm.handleLeaveSettingsApiRes();
        });
    },

    getReimbursementSettings() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_REIMBURSEMENT_SETTINGS,
          variables: {
            calledFrom: "workflow",
          },
          client: "apolloClientAI",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveReimbursementSettings
          ) {
            const { reimbursementSettings } =
              response.data.retrieveReimbursementSettings;
            vm.reimbursementWorkflowEnabled =
              reimbursementSettings[0].Enable_Workflow === "Yes";
            vm.handleReimbursementSettingsApiRes();
          } else {
            vm.handleReimbursementSettingsApiRes();
          }
        })
        .catch(() => {
          vm.handleReimbursementSettingsApiRes();
        });
    },

    handleReimbursementSettingsApiRes() {
      this.settingsAPICallCount += 1;
    },

    handleLeaveSettingsApiRes() {
      // no need to handle error we can consider hiding the leave form and present the resignation form
      let formIdInUrl = this.$route.query.form_id;
      if (formIdInUrl) {
        this.filterFormId = parseInt(formIdInUrl);
        this.filterFormId = this.filterFormId == 23 ? 268 : this.filterFormId;
      } else {
        if (this.leaveWorkflowEnabled) {
          this.filterFormId = 31; // leaves
        } else {
          this.filterFormId = 34; // resignation
        }
      }
      this.settingsAPICallCount += 1;
    },

    fetchGroupIds() {
      let vm = this;
      vm.$apollo
        .query({
          query: GET_EMPLOYEE_GROUP_IDS,
          variables: {
            employeeId: vm.loginEmployeeId,
          },
          client: "apolloClientA",
        })
        .then((response) => {
          if (response.data) {
            vm.groupId = response.data.getEmployeeGroupIds.GroupIdArray;
            vm.isGroupIdsFetched = true;
            vm.groupDependencyApiCallCount++;
            vm.taskCountDependApiCallCount++;
          } else {
            vm.groupDependencyApiCallCount++;
            vm.taskCountDependApiCallCount++;
          }
        })
        .catch(() => {
          vm.groupDependencyApiCallCount++;
          vm.taskCountDependApiCallCount++;
        });
    },

    fetchServiceProviderEmployees() {
      let vm = this;
      if (vm.isServiceProviderAdmin) {
        vm.$apollo
          .query({
            query: GET_SERVICE_PROVIDER_EMPLOYEES,
            variables: {
              employeeId: vm.loginEmployeeId,
            },
            client: "apolloClientA",
          })
          .then((response) => {
            if (response.data) {
              vm.serviceProviderEmployees =
                response.data.getServiceProviderEmployees.employeeId.length > 0
                  ? response.data.getServiceProviderEmployees.employeeId
                  : ["-"];
              vm.isServiceProviderEmpsFetched = true;
              vm.groupDependencyApiCallCount++;
              vm.taskCountDependApiCallCount++;
            } else {
              vm.groupDependencyApiCallCount++;
              vm.taskCountDependApiCallCount++;
            }
          })
          .catch(() => {
            vm.groupDependencyApiCallCount++;
            vm.taskCountDependApiCallCount++;
          });
      } else {
        vm.isServiceProviderEmpsFetched = true;
        vm.serviceProviderEmployees = ["-"];
        vm.groupDependencyApiCallCount++;
        vm.taskCountDependApiCallCount++;
      }
    },

    async fetchAllEmployeesList(approvalResponse, tabSelected) {
      let vm = this;
      await this.$store
        .dispatch("getAllEmployeesList", {
          formName: "Approval Management",
          isActiveOnly: 0,
        })
        .then((empData) => {
          if (empData && empData.length > 0) {
            vm.formApprovalListData(approvalResponse, tabSelected, empData);
          } else {
            vm.formApprovalListData(approvalResponse, tabSelected);
          }
        })
        .catch(() => {
          vm.formApprovalListData(approvalResponse, tabSelected);
        });
    },

    formApprovalListData(approvalResponse, tabSelected, empGroupData = null) {
      const approvals = empGroupData
        ? approvalResponse.map((item) => {
            if (
              this.filterFormId === 31 &&
              Array.isArray(item["moreDetails"]["alternatePerson"]) &&
              item["moreDetails"]["alternatePerson"].length > 0
            ) {
              let altPersonName = [];
              for (let altPerson of item["moreDetails"]["alternatePerson"]) {
                let filteredEmp = empGroupData.filter(
                  (el) => el.employee_id === parseInt(altPerson)
                );
                altPersonName.push(filteredEmp[0].employee_name);
              }
              item["moreDetails"]["alternatePerson"] =
                altPersonName.join(" , ");
            }
            for (let empGroup of empGroupData) {
              if (this.approvalType === "Approval History") {
                if (parseInt(item.completed_by) === empGroup.employee_id) {
                  item["approver"] = empGroup.employee_name;
                  item["approverUserDefinedEmpId"] =
                    empGroup.user_defined_empid;
                }
              } else {
                if (parseInt(item.assignee) === empGroup.employee_id) {
                  item["approver"] = empGroup.employee_name;
                  item["approverUserDefinedEmpId"] =
                    empGroup.user_defined_empid;
                }
              }
              if (
                this.filterFormId === 352 &&
                typeof item["alternatePerson"] === "number" &&
                item["alternatePerson"] === empGroup.employee_id
              ) {
                item["alternatePerson"] = empGroup.employee_name;
              } else if (
                this.filterFormId === 31 &&
                typeof item["moreDetails"]["alternatePerson"] === "number" &&
                item["moreDetails"]["alternatePerson"] === empGroup.employee_id
              ) {
                item["moreDetails"]["alternatePerson"] = empGroup.employee_name;
              }
              if (item["moreDetails"]["addedBy"] === empGroup.employee_id) {
                item["moreDetails"]["addedBy"] = empGroup.employee_name;
              }
              if (item["moreDetails"]["updatedBy"] === empGroup.employee_id) {
                item["moreDetails"]["updatedBy"] = empGroup.employee_name;
              }
              let empId = item.employeeDetails.employeeid
                ? item.employeeDetails.employeeid
                : item.instanceData.Employee_Id
                ? item.instanceData.Employee_Id
                : item.instanceData.employeeId;
              item["employeeId"] = empId;
              if (empId == empGroup.employee_id) {
                item["employeeName"] = empGroup.employee_name;
                item["userDefinedEmpId"] = empGroup.user_defined_empid;
                item["designation"] = empGroup.designation_name;
                item["department"] = empGroup.department_name;
                item["empType"] = empGroup.employee_type;
                item["location"] = empGroup.location_name;
                item["workSchedule"] = empGroup.work_schedule_name;
                item["businessUnit"] = empGroup.Business_Unit;
                item["serviceProvider"] = empGroup.Service_Provider_Name;
              }
            }
            return item;
          })
        : approvalResponse;
      if (this.filterFormId !== 31) {
        if (this.approvalType === "Approval History") {
          this.approvalHistoryRetrieved =
            this.approvalHistoryRetrieved.concat(approvals);
          this.approvalHistoryRetrieved =
            this.removeDuplicatesFromArrayOfObject(
              this.approvalHistoryRetrieved,
              "task_id"
            );
          this.approvalHistoryList = this.approvalHistoryRetrieved;
          this.approvalHistoryListBackup = this.approvalHistoryRetrieved;
        } else {
          if (this.approvalType === tabSelected) {
            this.approvalsRetrieved = this.approvalsRetrieved.concat(approvals);
            this.approvalsRetrieved = this.removeDuplicatesFromArrayOfObject(
              this.approvalsRetrieved,
              "task_id"
            );
            this.approvalList = this.approvalsRetrieved;
            this.approvalListBackup = this.approvalsRetrieved;
          }
        }
        this.applyFilterCount += 1;
        this.listLoading = false;
      } else {
        if (this.leaveTypeList.length > 0) {
          this.appendLeaveTypeName(approvals, tabSelected);
        } else {
          this.fetchLeaveTypes(approvals, tabSelected);
        }
      }
    },

    appendLeaveTypeName(approvals, tabSelected) {
      const modifiedApprovalList = approvals.map((item) => {
        let findLeaveType = this.leaveTypeList.filter(
          (el) => el.leaveTypeId === item.instanceData.leaveTypeId
        );
        item["leaveId"] = item.instanceData.leaveTypeId;
        item["leaveType"] =
          findLeaveType && findLeaveType.length > 0
            ? findLeaveType[0].leaveName
            : "";
        return item;
      });
      if (this.approvalType === "Approval History") {
        this.approvalHistoryRetrieved =
          this.approvalHistoryRetrieved.concat(modifiedApprovalList);
        this.approvalHistoryRetrieved = this.removeDuplicatesFromArrayOfObject(
          this.approvalHistoryRetrieved,
          "task_id"
        );
        this.approvalHistoryList = this.approvalHistoryRetrieved;
        this.approvalHistoryListBackup = this.approvalHistoryRetrieved;
        this.listLoading = false;
      } else {
        if (this.approvalType === tabSelected) {
          this.approvalsRetrieved =
            this.approvalsRetrieved.concat(modifiedApprovalList);
          this.approvalsRetrieved = this.removeDuplicatesFromArrayOfObject(
            this.approvalsRetrieved,
            "task_id"
          );
          this.approvalList = this.approvalsRetrieved;
          this.approvalListBackup = this.approvalsRetrieved;
          this.listLoading = false;
        }
      }
      this.applyFilterCount += 1;
    },

    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },

    fetchLeaveTypes(approvalListItems, tabSelected) {
      let vm = this;
      vm.$apollo
        .query({
          query: LIST_LEAVE_TYPES,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response && response.data && response.data.listLeaveTypes) {
            vm.leaveTypeList = response.data.listLeaveTypes.leaveTypeDetails
              ? response.data.listLeaveTypes.leaveTypeDetails
              : [];
          } else {
            vm.leaveTypeList = [];
          }
          vm.appendLeaveTypeName(approvalListItems, tabSelected);
        })
        .catch(() => {
          vm.leaveTypeList = [];
          if (approvalListItems.length > 0) {
            vm.appendLeaveTypeName(approvalListItems, tabSelected);
          }
        });
    },

    retrieveUserTask(approvalType) {
      let vm = this;
      try {
        // set loader only when this API is called first time
        if (vm.approvalListCallCount === 0) {
          vm.listLoading = true;
        }
        let myTaskFilter = [];
        if (vm.approvalType === "Group Approvals") {
          if (
            vm.isServiceProviderAdmin &&
            (vm.filterFormId === 31 || vm.filterFormId === 34)
          ) {
            myTaskFilter.push(
              {
                key: "custom_group_id",
                value: vm.groupId.length > 0 ? vm.groupId : ["-"],
                operator: "in",
                Employee_Id: vm.serviceProviderEmployees,
              },
              {
                key: "Employee_Id",
                value: vm.serviceProviderEmployees,
                operator: "in",
                condition: "and",
              }
            );
          } else {
            myTaskFilter.push({
              key: "custom_group_id",
              value: vm.groupId.length > 0 ? vm.groupId : ["-"],
              operator: "in",
            });
          }
        } else {
          myTaskFilter.push({
            key: "assignee",
            value: [
              vm.approvalType === "Outstanding Approvals"
                ? 0
                : vm.loginEmployeeId,
            ],
            operator:
              vm.approvalType === "Outstanding Approvals" ? "not in" : "in",
          });
        }
        myTaskFilter.push({
          key: "form_id",
          value: [vm.filterFormId],
          operator: "in",
          condition: "and",
        });
        let apiOffset = vm.approvalListCallCount * vm.approvalApiLimit;
        apiOffset = apiOffset > 0 ? apiOffset + 1 : apiOffset;
        axios
          .post(
            Config.workflowUrl + "/workflow/userTask",
            {
              filter: myTaskFilter,
              form_id: vm.filterFormId,
              limit: vm.approvalApiLimit,
              offset: apiOffset,
            },
            {
              headers: {
                org_code: vm.orgCode,
                employee_id: vm.loginEmployeeId,
                db_prefix: vm.domainName,
                Authorization: window.$cookies.get("accessToken")
                  ? window.$cookies.get("accessToken")
                  : "",
                refresh_token: window.$cookies.get("refreshToken")
                  ? window.$cookies.get("refreshToken")
                  : null,
                partnerid: window.$cookies.get("partnerid")
                  ? window.$cookies.get("partnerid")
                  : "-",
              },
            }
          )
          .then((response) => {
            if (response && response.data && response.data.message) {
              let approvalResponse = JSON.parse(response.data.message);
              if (
                (approvalResponse && approvalResponse.length > 0) ||
                vm.approvalsRetrieved.length > 0
              ) {
                approvalResponse = approvalResponse.map((item) => {
                  item["employeeName"] = item.employeeDetails.employeename;
                  item["userDefinedEmpId"] =
                    item.employeeDetails.userdefinedemployeeid;
                  item["approvalLevel"] = Math.round(
                    (item.completedTaskCount / item.totalTaskCount) * 100
                  );
                  // leave form fields
                  if (vm.filterFormId === 31) {
                    item.fileNames = item.instanceData.documentList;
                    item["startDate"] = item.instanceData.startDate
                      ? this.formatDate(item.instanceData.startDate)
                      : "-";
                    item["startDateInDateFormat"] = item.instanceData.startDate
                      ? new Date(item.instanceData.startDate)
                      : "-";
                    item["endDate"] = item.instanceData.endDate
                      ? this.formatDate(item.instanceData.endDate)
                      : "-";
                    item["endDateInDateFormat"] = item.instanceData.endDate
                      ? new Date(item.instanceData.endDate)
                      : "-";
                    item["totalDays"] = item.instanceData.totalDays;
                    item["totalDays"] = parseFloat(item["totalDays"]);
                    item["approvalStatus"] = item.instanceData.approvalStatus
                      ? item.instanceData.approvalStatus
                      : "";
                    item["earlyCheckout"] = parseInt(
                      item.instanceData.earlyCheckoutLeave
                    )
                      ? "Yes"
                      : "No";
                    item["attendanceShortage"] = item.instanceData
                      .Attendance_Shortage
                      ? item.instanceData.Attendance_Shortage
                      : "No";
                    item["autoLOP"] = item.instanceData.No_Attendance
                      ? item.instanceData.No_Attendance
                      : "No";
                    item["moreDetails"] = {
                      duration:
                        item.instanceData.duration == 0.5
                          ? "Half Day"
                          : "Full Day",
                      leavePeriod: item.instanceData.leavePeriod
                        ? item.instanceData.leavePeriod
                        : "-",
                      hours: item.instanceData.hours,
                      reason: item.instanceData.reason,
                      reasonType: item.instanceData.esicReason
                        ? item.instanceData.esicReason
                        : "-",
                      alternatePerson: item.instanceData.alternatePerson
                        ? item.instanceData.alternatePerson
                        : "-",
                      lateAttendance:
                        item.instanceData.lateAttendance === 1 ? "Yes" : "No",
                    };
                    if (
                      item.instanceData.lateAttendance === 1 &&
                      item.instanceData.lateAttendanceHours
                    ) {
                      if (item.instanceData.lateAttendanceHoursFromGrace) {
                        item["moreDetails"]["lateAttendanceHours"] =
                          "The employee checked in late by " +
                          item.instanceData.lateAttendanceHoursFromGrace +
                          "(HH:MM:SS) excluding the grace period of " +
                          item.instanceData.alwaysGrace +
                          "(HH:MM:SS).Including the grace period, the employee was delayed by " +
                          item.instanceData.lateAttendanceHours +
                          "(HH:MM:SS)";
                      } else {
                        item["moreDetails"]["lateAttendanceHours"] =
                          item.instanceData.lateAttendanceHours;
                      }
                    }
                    item["moreDetails"] = {
                      ...item["moreDetails"],
                      selfApply:
                        item.instanceData.isSelfApply === 1 ? "Yes" : "No",
                      comment: item.instanceData.comment
                        ? item.instanceData.comment
                        : "-",
                      leaveStatus: item.instanceData.approvalStatus
                        ? item.instanceData.approvalStatus
                        : "-",
                      addedBy: item.instanceData.addedBy
                        ? item.instanceData.addedBy
                        : "-",
                      addedOn: item.instanceData.addedOn
                        ? this.formatDate(item.instanceData.addedOn, true)
                        : "-",
                    };
                    if (
                      item.instanceData.updatedBy &&
                      item.instanceData.updatedOn
                    ) {
                      item["moreDetails"]["updatedBy"] =
                        item.instanceData.updatedBy;
                      item["moreDetails"]["updatedOn"] = item.instanceData
                        .updatedOn
                        ? this.formatDate(item.instanceData.updatedOn, true)
                        : "-";
                    }
                  }
                  // resignation form fields
                  else if (vm.filterFormId === 34) {
                    item["appliedDate"] = item.instanceData.appliedDate
                      ? this.formatDate(item.instanceData.appliedDate)
                      : "-";
                    item["appliedDateInDateFormat"] = item.instanceData
                      .appliedDate
                      ? new Date(item.instanceData.appliedDate)
                      : "-";
                    item["exitDate"] = item.instanceData.exitDate
                      ? this.formatDate(item.instanceData.exitDate)
                      : "-";
                    item["exitDateInDateFormat"] = item.instanceData.exitDate
                      ? new Date(item.instanceData.exitDate)
                      : "-";
                    item["approvalStatus"] = item.instanceData.approvalStatus
                      ? item.instanceData.approvalStatus
                      : "";
                    item["moreDetails"] = {
                      relievingReason: item.instanceData.relievingReasonComment
                        ? item.instanceData.relievingReasonComment
                        : "-",
                      reasonType: item.instanceData.esicReason
                        ? item.instanceData.esicReason
                        : "-",
                      resignationStatus: item.instanceData.approvalStatus
                        ? item.instanceData.approvalStatus
                        : "-",
                    };
                    if (item.instanceData.approvalStatus === "Withdrawn") {
                      item["moreDetails"]["withdrawnComment"] = item
                        .instanceData.withdrawnCancellationComment
                        ? item.instanceData.withdrawnCancellationComment
                        : "-";
                    } else if (
                      item.instanceData.approvalStatus === "Canceled"
                    ) {
                      item["moreDetails"]["cancellationComment"] = item
                        .instanceData.withdrawnCancellationComment
                        ? item.instanceData.withdrawnCancellationComment
                        : "-";
                    }
                    (item["moreDetails"]["addedBy"] = item.instanceData
                      .initiatorId
                      ? item.instanceData.initiatorId
                      : "-"),
                      (item["moreDetails"]["addedOn"] = item.instanceData
                        .addedOn
                        ? this.formatDate(item.instanceData.addedOn, true)
                        : "-");
                    if (
                      item.instanceData.updatedBy &&
                      item.instanceData.updatedOn
                    ) {
                      item["moreDetails"]["updatedBy"] =
                        item.instanceData.updatedBy;
                      item["moreDetails"]["updatedOn"] = item.instanceData
                        .updatedOn
                        ? this.formatDate(item.instanceData.updatedOn, true)
                        : "-";
                    }
                  }
                  // job post details
                  else if (vm.filterFormId === 15) {
                    item["jobTitle"] = item.instanceData.jobPostName;
                    item["postingDate"] = item.instanceData.postingDate
                      ? this.formatDate(item.instanceData.postingDate)
                      : "-";
                    item["postingDateInDateFormat"] = item.instanceData
                      .postingDate
                      ? new Date(item.instanceData.postingDate)
                      : "-";
                    item["closingDate"] = item.instanceData.closingDate
                      ? this.formatDate(item.instanceData.closingDate)
                      : "-";
                    item["closingDateInDateFormat"] = item.instanceData
                      .closingDate
                      ? new Date(item.instanceData.closingDate)
                      : "-";
                    item["priority"] = item.instanceData.priority;
                    item["approvalStatus"] = item.instanceData.approvalStatus
                      ? item.instanceData.approvalStatus
                      : "";
                    item["moreDetails"] = {
                      jobDescription: item.instanceData.jobDescription
                        ? item.instanceData.jobDescription
                        : "-",
                      noOfVacancies: item.instanceData.noOfVacancies,
                      reasonForOpening: item.instanceData.reasonForOpening
                        ? item.instanceData.reasonForOpening
                        : "-",
                      jobDuration: item.instanceData.jobDuration + " month(s)", // months
                      expectedJoiningDate: this.formatDate(
                        item.instanceData.expectedJoiningDate
                      ),
                      minWorkExperience: item.instanceData.minWorkExperience,
                      maxWorkExperience: item.instanceData.maxWorkExperience,
                      minPaymentFrequency:
                        item.instanceData.minPaymentFrequency,
                      maxPaymentFrequency:
                        item.instanceData.maxPaymentFrequency,
                      agencyInvolved: item.instanceData.agencyInvolved
                        ? item.instanceData.agencyInvolved
                        : "-",
                      coolingPeriod:
                        item.instanceData.coolingPeriod + " month(s)", // month
                      travelRequired: item.instanceData.travelRequired
                        ? "Yes"
                        : "No", // yes/no
                      addedBy: item.instanceData.addedBy
                        ? item.instanceData.addedBy
                        : "-",
                    };
                  }
                  // lop recovery
                  else if (vm.filterFormId === 253) {
                    item["lopRecoveryProcessingMonth"] = item.instanceData
                      .Salary_Payment_Month
                      ? this.convertMonthNumberToString(
                          item.instanceData.Salary_Payment_Month
                        )
                      : "-";
                    item["totalDays"] =
                      item.instanceData.Total_Lop_Recovery_Days;
                    item["totalDays"] = parseFloat(item["totalDays"]);
                    item["totalAmount"] = item.instanceData
                      .Total_Recovery_Amount
                      ? item.instanceData.Total_Recovery_Amount.toFixed(2)
                      : "";
                    item["approvalStatus"] = item.instanceData.Approval_Status
                      ? item.instanceData.Approval_Status
                      : "";
                    item["moreDetails"] = {
                      deductionMonth: item.instanceData.Salary_Deduction_Month
                        ? this.convertMonthNumberToString(
                            item.instanceData.Salary_Deduction_Month
                          )
                        : "-",
                      recoveryDate: item.instanceData.Lop_Recovery_Date
                        ? this.formatMultipleDates(
                            item.instanceData.Lop_Recovery_Date
                          )
                        : "-",
                      leaveName: item.instanceData.Leave_Name
                        ? item.instanceData.Leave_Name
                        : "-",
                      duration: item.instanceData.Duration
                        ? item.instanceData.Duration
                        : "-",
                      period: item.instanceData.Period
                        ? this.containsOnlyCommas(item.instanceData.Period)
                          ? "-"
                          : item.instanceData.Period
                        : "-",
                      reason: item.instanceData.Reason
                        ? item.instanceData.Reason
                        : "-",
                      perDaySalary: item.instanceData.Per_Day_Salary
                        ? item.instanceData.Per_Day_Salary.toFixed(2)
                        : 0,
                      remarks: item.instanceData.Remarks
                        ? item.instanceData.Remarks
                        : "-",
                      status: item.instanceData.Approval_Status
                        ? item.instanceData.Approval_Status
                        : "-",
                      addedBy: item.instanceData.Added_By
                        ? item.instanceData.Added_By
                        : "-",
                      addedOn: item.instanceData.Added_On
                        ? this.formatDate(
                            new Date(item.instanceData.Added_On + ".000Z"),
                            true
                          )
                        : "-",
                    };
                    if (
                      item.instanceData.Updated_By &&
                      item.instanceData.Updated_On
                    ) {
                      item["moreDetails"]["updatedBy"] =
                        item.instanceData.Updated_By;
                      item["moreDetails"]["updatedOn"] = item.instanceData
                        .Updated_On
                        ? this.formatDate(
                            new Date(item.instanceData.Updated_On + ".000Z"),
                            true
                          )
                        : "-";
                    }
                  }
                  // reimbursement
                  else if (vm.filterFormId === 267) {
                    item["totalAmount"] = item.instanceData.totalAmount
                      ? item.instanceData.totalAmount
                      : 0;
                    item["totalAppliedAmount"] = item.instanceData
                      .totalAppliedAmount
                      ? item.instanceData.totalAppliedAmount
                      : 0;
                    item["appliedDate"] = this.formatDate(
                      item.instanceData.submissionDate
                    );
                    item["startDateInDateFormat"] = item.instanceData
                      .submissionDate
                      ? new Date(item.instanceData.submissionDate)
                      : "-";
                    item["approvalStatus"] = item.instanceData.Approval_Status
                      ? item.instanceData.Approval_Status
                      : "";
                    item["moreDetails"] = {
                      status: item.instanceData.Approval_Status
                        ? item.instanceData.Approval_Status
                        : "-",
                      remarks: item.remarks ? item.remarks : "-",
                      addedBy: item.instanceData.Added_By
                        ? parseInt(item.instanceData.Added_By)
                        : "-",
                      addedOn: item.instanceData.Added_On
                        ? this.formatDate(item.instanceData.Added_On, true)
                        : "-",
                    };
                  }
                  // timesheets
                  else if (vm.filterFormId === 268) {
                    item["totalEffort"] = item.instanceData.totalEffort
                      ? item.instanceData.totalEffort
                      : 0;
                    item["weekendDate"] = item.instanceData.Week_Ending_Date
                      ? this.formatDate(item.instanceData.Week_Ending_Date)
                      : "-";
                    item["startDateInDateFormat"] = item.instanceData
                      .Week_Ending_Date
                      ? new Date(item.instanceData.Week_Ending_Date)
                      : "-";
                    item["approvalStatus"] = item.instanceData.Approval_Status
                      ? item.instanceData.Approval_Status
                      : "";
                    item["moreDetails"] = {
                      status: item.instanceData.Approval_Status
                        ? item.instanceData.Approval_Status
                        : "-",
                      remarks: item.remarks ? item.remarks : "-",
                      addedBy: item.instanceData.Added_By
                        ? parseInt(item.instanceData.Added_By)
                        : "-",
                      addedOn: item.instanceData.Added_On
                        ? this.formatDate(
                            new Date(item.instanceData.Added_On + ".000Z"),
                            true
                          )
                        : "-",
                    };
                  }
                  // Comp Off
                  else if (vm.filterFormId === 334) {
                    item["workedDate"] = item.instanceData.Worked_Date
                      ? this.formatDate(item.instanceData.Worked_Date)
                      : "-";
                    item["duration"] = item.instanceData.Duration
                      ? item.instanceData.Duration
                      : "-";
                    item["compOffDate"] = item.instanceData.Compensatory_Date
                      ? this.formatDate(
                          new Date(item.instanceData.Compensatory_Date),
                          false
                        )
                      : "-";
                    item["period"] = item.instanceData.Period
                      ? item.instanceData.Period
                      : "-";
                    item["approvalStatus"] = item.instanceData.Approval_Status
                      ? item.instanceData.Approval_Status
                      : "";
                    item["moreDetails"] = {
                      workedDate: item.instanceData?.Worked_Date
                        ? this.formatDate(
                            new Date(item.instanceData.Worked_Date),
                            false
                          )
                        : "",
                      duration: item.instanceData?.Duration
                        ? item.instanceData.Duration
                        : "",
                      compOffDate: item.instanceData?.Compensatory_Date
                        ? this.formatDate(
                            new Date(item.instanceData.Compensatory_Date),
                            false
                          )
                        : "",
                      period: item.instanceData?.Period
                        ? item.instanceData.Period
                        : "-",
                      status: item.instanceData?.Approval_Status
                        ? item.instanceData.Approval_Status
                        : "-",
                      addedBy: item.instanceData?.Added_By
                        ? parseInt(item.instanceData?.Added_By)
                        : "-",
                      addedOn: item.instanceData?.Added_On
                        ? this.formatDate(
                            new Date(item.instanceData?.Added_On),
                            true
                          )
                        : "-",
                    };
                    if (
                      item.instanceData?.Updated_On &&
                      item.instanceData?.Updated_By
                    ) {
                      item["moreDetails"]["updatedBy"] = parseInt(
                        item.instanceData.Updated_By
                      );
                      item["moreDetails"]["updatedOn"] = this.formatDate(
                        new Date(item.instanceData?.Updated_On),
                        true
                      );
                    }
                  }
                  // Travel Request
                  else if (vm.filterFormId === 341) {
                    item["travelName"] = item.instanceData.Trip_Name
                      ? item.instanceData.Trip_Name
                      : "-";
                    item["travelType"] = item.instanceData.Travel_Type
                      ? item.instanceData.Travel_Type
                      : "-";
                    item["destinationCountry"] = item.instanceData
                      .Destination_Country
                      ? item.instanceData.Destination_Country
                      : "-";
                    item["travelStartDate"] = item.instanceData
                      .Travel_Start_Date
                      ? this.formatDate(item.instanceData.Travel_Start_Date)
                      : "-";
                    item["travelEndDate"] = item.instanceData.Travel_End_Date
                      ? this.formatDate(item.instanceData.Travel_End_Date)
                      : "-";
                    item["amount"] = item.instanceData.Budget_Amount
                      ? item.instanceData.Budget_Amount
                      : "-";
                    item["approvalStatus"] = item.instanceData.Status
                      ? item.instanceData.Status
                      : "";
                    item["moreDetails"] = {
                      status: item.instanceData?.Status
                        ? item.instanceData.Status
                        : "-",
                      addedBy: item.instanceData?.Added_By
                        ? parseInt(item.instanceData.Added_By)
                        : "-",
                      addedOn: item.instanceData?.Added_On
                        ? this.formatDate(
                            new Date(item.instanceData.Added_On + ".000Z"),
                            true
                          )
                        : "-",
                      updatedBy: item.instanceData?.Updated_By
                        ? item.instanceData.Updated_By
                        : "-",
                      updatedOn: item.instanceData?.Updated_On
                        ? this.formatDate(
                            new Date(item.instanceData.Updated_On + ".000Z"),
                            true
                          )
                        : "-",
                    };
                  }
                  // Short Time Off Request
                  else if (vm.filterFormId === 352) {
                    // Employee Information
                    item["employeeId"] = item.instanceData.Employee_Id ?? "-";
                    item["employeeName"] =
                      item.instanceData.Employee_Name ?? "-";
                    item["userDefinedEmpId"] =
                      item.instanceData.User_Defined_EmpId ?? "-";
                    // Request Details
                    item["requestFor"] = item.instanceData.Request_For ?? "-";
                    item["earlyCheckout"] = item.instanceData.Early_Checkout
                      ? item.instanceData.Early_Checkout
                      : "No";
                    item.instanceData.Late_Attendance = item.instanceData
                      .Late_Attendance
                      ? item.instanceData.Late_Attendance
                      : "No";
                    item.instanceData.Early_Checkout =
                      item.instanceData.Early_Checkout ?? "No";
                    item.instanceData.lateAttendance =
                      item.instanceData.Late_Attendance &&
                      item.instanceData.Late_Attendance == "Yes"
                        ? 1
                        : 0;
                    item["startDateTime"] = item.instanceData.Start_Date_Time
                      ? this.formatDateTime(
                          item.instanceData.Start_Date_Time,
                          true
                        )
                      : "-";
                    item.instanceData.Start_Date = item.instanceData
                      .Start_Date_Time
                      ? moment(
                          new Date(item.instanceData.Start_Date_Time)
                        ).format("YYYY-MM-DD")
                      : "";
                    item["endDateTime"] = item.instanceData.End_Date_Time
                      ? this.formatDateTime(
                          item.instanceData.End_Date_Time,
                          true
                        )
                      : "-";
                    item.instanceData.End_Date = item.instanceData.End_Date_Time
                      ? moment(
                          new Date(item.instanceData.End_Date_Time)
                        ).format("YYYY-MM-DD")
                      : "";
                    item["viewStartDateTime"] = item.instanceData
                      .View_Start_Date_Time
                      ? this.formatDateTime(
                          item.instanceData.View_Start_Date_Time,
                          true
                        )
                      : "-";
                    item["viewEndDateTime"] = item.instanceData
                      .View_End_Date_Time
                      ? this.formatDateTime(
                          item.instanceData.View_End_Date_Time,
                          true
                        )
                      : "-";

                    // Time Off Details
                    item["totalHours"] =
                      this.formatTotalHours(item.instanceData.Total_Hours) ??
                      "-";
                    item["lateAttendance"] =
                      item.instanceData.Late_Attendance ?? "-";
                    item["earlyCheckout"] =
                      item.instanceData.Early_Checkout ?? "-";
                    item["earlyCheckoutHours"] =
                      item.instanceData.Early_Checkout_Hours ?? "-";

                    // Approval Information
                    item["approvalStatus"] =
                      item.instanceData.Approval_Status ?? "-";
                    item["approverName"] =
                      item.instanceData.Approved_By_Name ?? "-";
                    item["approvedOn"] = item.instanceData.Approved_On
                      ? this.formatDate(item.instanceData.Approved_On, true)
                      : "-";

                    // Additional Information
                    item["reason"] = item.instanceData.Reason ?? "-";
                    item["alternatePerson"] = item.instanceData
                      ?.Alternate_Person
                      ? parseInt(item.instanceData.Alternate_Person)
                      : "";
                    item["contactDetails"] =
                      item.instanceData.Contact_Details ?? "-";
                    item["autoShortTimeOff"] =
                      item.instanceData.Auto_Short_Time_Off ?? "-";
                    item["serviceProviderId"] =
                      item.instanceData.Service_Provider_Id ?? "-";

                    // Comments/Remarks
                    item["comments"] =
                      item.instanceData.comments?.length > 0
                        ? item.instanceData.comments
                        : [];

                    // More Details (system fields)
                    item["moreDetails"] = {
                      startDateTime: item.instanceData.Start_Date_Time
                        ? this.formatDateTime(
                            item.instanceData.Start_Date_Time,
                            true
                          )
                        : "-",
                      endDateTime: item.instanceData.End_Date_Time
                        ? this.formatDateTime(
                            item.instanceData.End_Date_Time,
                            true
                          )
                        : "-",
                      totalHours: item.instanceData.Total_Hours
                        ? this.formatTotalHours(item.instanceData.Total_Hours)
                        : "-",
                      reason: item.instanceData.Reason
                        ? item.instanceData.Reason
                        : "-",
                      addedBy: item.instanceData.Added_By
                        ? parseInt(item.instanceData.Added_By)
                        : "-",
                      addedOn: item.instanceData?.Added_On
                        ? this.formatDate(item.instanceData.Added_On, true)
                        : "-",
                      ...(item.instanceData.Updated_By &&
                      item.instanceData.Updated_On
                        ? {
                            updatedBy: parseInt(item.instanceData.Updated_By),
                            updatedOn: this.formatDate(
                              item.instanceData.Updated_On,
                              true
                            ),
                          }
                        : {}),
                    };
                  }
                  //Recruitment Request
                  else if (vm.filterFormId == 291 || vm.filterFormId == 290) {
                    item["positionName"] = item.instanceData.positionTitle
                      ? item.instanceData.positionTitle
                      : "-";
                    item["positionCode"] = item.instanceData.positionCode
                      ? item.instanceData.positionCode
                      : null;
                    item["groupCode"] = item.instanceData.groupCode
                      ? item.instanceData.groupCode
                      : "-";
                    item["divisionCode"] = item.instanceData.divisionCode
                      ? item.instanceData.divisionCode
                      : "-";
                    item["noOfPositions"] = item.instanceData.noOfPosition
                      ? item.instanceData.noOfPosition
                      : 0;
                    item["approvalStatus"] = item.instanceData.Approval_Status;
                    item["Approval_Status"] = item.instanceData.Approval_Status;
                    item["moreDetails"] = {
                      departmentCode: item.instanceData.departmentCode
                        ? item.instanceData.departmentCode
                        : "-",
                      sectionCode: item.instanceData.sectionCode
                        ? item.instanceData.sectionCode
                        : "-",
                      costCenter: item.instanceData.costCenter
                        ? item.instanceData.costCenter
                        : "-",
                      employeeType: item.instanceData.employeeTypeName
                        ? item.instanceData.employeeTypeName
                        : "-",
                      addedBy: item.instanceData.Added_By
                        ? item.instanceData.Added_By
                        : "-",
                      addedOn: item.instanceData.Added_On
                        ? item.instanceData.Added_On
                        : "-",
                      updatedBy: item.instanceData.Updated_By
                        ? item.instanceData.Updated_By
                        : "-",
                      updatedOn: item.instanceData.Updated_On
                        ? item.instanceData.Updated_On
                        : "-",
                    };
                  }
                  // pre approvals forms
                  else {
                    item.fileNames = item.instanceData.File_Name
                      ? [item.instanceData.File_Name]
                      : [];
                    item["startDate"] = item.instanceData.Start_Date
                      ? this.formatDate(item.instanceData.Start_Date)
                      : "-";
                    item["startDateInDateFormat"] = item.instanceData.Start_Date
                      ? new Date(item.instanceData.Start_Date)
                      : "-";
                    item["endDate"] = item.instanceData.End_Date
                      ? this.formatDate(item.instanceData.End_Date)
                      : "-";
                    item["endDateInDateFormat"] = item.instanceData.End_Date
                      ? new Date(item.instanceData.End_Date)
                      : "-";
                    item["totalDays"] = item.instanceData.Total_Days;
                    item["totalDays"] = parseFloat(item["totalDays"]);
                    item["approvalStatus"] = item.instanceData.Status;
                    item["moreDetails"] = {
                      duration: item.instanceData.Duration,
                      period: item.instanceData.Period
                        ? item.instanceData.Period
                        : "-",
                      reason: item.instanceData.Reason,
                      status: item.instanceData.Status
                        ? item.instanceData.Status
                        : "-",
                      addedBy: item.instanceData.Added_By
                        ? item.instanceData.Added_By
                        : "-",
                      addedOn: item.instanceData.Added_On
                        ? this.formatDate(
                            new Date(item.instanceData.Added_On + ".000Z"),
                            true
                          )
                        : "-",
                    };
                    if (
                      item.instanceData.Updated_By &&
                      item.instanceData.Updated_On
                    ) {
                      item["moreDetails"]["updatedBy"] =
                        item.instanceData.Updated_By;
                      item["moreDetails"]["updatedOn"] = item.instanceData
                        .Updated_On
                        ? this.formatDate(
                            new Date(item.instanceData.Updated_On + ".000Z"),
                            true
                          )
                        : "-";
                    }
                  }
                  return item;
                });
                if (vm.approvalType !== "Approval History") {
                  vm.fetchAllEmployeesList(approvalResponse, approvalType);
                }
                if (approvalResponse.length >= vm.approvalApiLimit) {
                  vm.approvalListCallCount += 1;
                  vm.retrieveUserTask(approvalType);
                } else {
                  vm.approvalListCallCount = 0;
                }
              } else {
                vm.listLoading = false;
                vm.approvalList = [];
                vm.approvalListBackup = [];
              }
            } else {
              vm.handleUserTaskError();
            }
          })
          .catch(function (e) {
            vm.handleUserTaskError(e);
          });
      } catch {
        vm.handleUserTaskError();
      }
    },

    handleUserTaskError(err = "") {
      if (err && err.response && err.response.data) {
        let errorCode = err.response.data.errorCode;
        if (errorCode) {
          switch (errorCode) {
            case "ERR-753": // task id not found
              this.errorContent =
                "Unable to retrieve the approval details. If you continue to see this issue please contact the platform administrator.";
              break;
            case "ERR-798": // Error while executing the query
            default:
              this.errorContent =
                "Something went wrong while retrieving the approval details. If you continue to see this issue please contact the platform administrator.";
              break;
          }
        } else {
          this.errorContent =
            "Something went wrong while retrieving the approval details. Please try after some time.";
        }
      } else {
        this.errorContent =
          "Something went wrong while retrieving the approval details. Please try after some time.";
      }
      this.listLoading = false;
      this.isErrorInList = true;
    },

    retrieveUserTaskHistory() {
      let vm = this;
      if (vm.approvalType === "Approval History") {
        try {
          // set loader only when this API is called first time
          if (vm.approvalHistoryCallCount === 0) {
            vm.listLoading = true;
          }
          let apiOffset = vm.approvalHistoryCallCount * vm.approvalApiLimit;
          apiOffset = apiOffset > 0 ? apiOffset + 1 : apiOffset;
          let filterStartEndDate = vm.filteredApprovalStartAndEndDate;
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          let sDate = moment(
            filterStartEndDate.startDate,
            orgDateFormat
          ).format("YYYY-MM-DD");
          let eDate = moment(filterStartEndDate.endDate, orgDateFormat)
            .add("1", "days")
            .format("YYYY-MM-DD");
          axios
            .post(
              Config.workflowUrl + "/workflow/userTaskHistory",
              {
                filter: [
                  {
                    key: "assignee",
                    value: [0],
                    operator: "not in",
                  },
                  {
                    key: "form_id",
                    value: [vm.filterFormId],
                    operator: "in",
                    condition: "and",
                  },
                  {
                    key: "created_date",
                    value: sDate,
                    operator: ">",
                    condition: "and",
                  },
                  {
                    key: "created_date",
                    value: eDate,
                    operator: "<",
                    condition: "and",
                  },
                ],
                form_id: vm.filterFormId,
                limit: vm.approvalApiLimit,
                offset: apiOffset,
              },
              {
                headers: {
                  org_code: vm.orgCode,
                  employee_id: vm.loginEmployeeId,
                  db_prefix: vm.domainName,
                  Authorization: window.$cookies.get("accessToken")
                    ? window.$cookies.get("accessToken")
                    : "",
                  refresh_token: window.$cookies.get("refreshToken")
                    ? window.$cookies.get("refreshToken")
                    : null,
                  partnerid: window.$cookies.get("partnerid")
                    ? window.$cookies.get("partnerid")
                    : "-",
                },
              }
            )
            .then((response) => {
              if (response && response.data && response.data.message) {
                let approvalResponse = JSON.parse(response.data.message);
                if (
                  (approvalResponse && approvalResponse.length > 0) ||
                  vm.approvalHistoryRetrieved.length > 0
                ) {
                  if (approvalResponse.length >= vm.approvalApiLimit) {
                    vm.approvalHistoryCallCount += 1;
                    vm.retrieveUserTaskHistory();
                  } else {
                    vm.approvalHistoryCallCount = 0;
                  }
                  approvalResponse = approvalResponse.map((item) => {
                    item["employeeName"] = item.employeeDetails.employeename;
                    item["userDefinedEmpId"] =
                      item.employeeDetails.userdefinedemployeeid;
                    item["approvalLevel"] = Math.round(
                      (item.completedTaskCount / item.totalTaskCount) * 100
                    );
                    item["taskStatus"] =
                      item.task_type === "reject"
                        ? "Rejected"
                        : item.task_type === "approve"
                        ? "Approved"
                        : "";
                    // leave details
                    if (vm.filterFormId === 31) {
                      item.fileNames = item.instanceData.documentList;
                      item["startDate"] = item.instanceData.startDate
                        ? this.formatDate(item.instanceData.startDate)
                        : "-";
                      item["startDateInDateFormat"] = item.instanceData
                        .startDate
                        ? new Date(item.instanceData.startDate)
                        : "-";
                      item["endDate"] = item.instanceData.endDate
                        ? this.formatDate(item.instanceData.endDate)
                        : "-";
                      item["endDateInDateFormat"] = item.instanceData.endDate
                        ? new Date(item.instanceData.endDate)
                        : "-";
                      item["totalDays"] = item.instanceData.totalDays;
                      item["totalDays"] = parseFloat(item["totalDays"]);
                      item["moreDetails"] = {
                        duration:
                          item.instanceData.duration == 0.5
                            ? "Half Day"
                            : "Full Day",
                        leavePeriod: item.instanceData.leavePeriod
                          ? item.instanceData.leavePeriod
                          : "-",
                        hours: item.instanceData.hours,
                        reason: item.instanceData.reason,
                        reasonType: item.instanceData.esicReason
                          ? item.instanceData.esicReason
                          : "-",
                        alternatePerson: item.instanceData.alternatePerson
                          ? item.instanceData.alternatePerson
                          : "-",
                        lateAttendance:
                          item.instanceData.lateAttendance === 1 ? "Yes" : "No",
                      };
                      if (
                        item.instanceData.lateAttendance === 1 &&
                        item.instanceData.lateAttendanceHours
                      ) {
                        if (item.instanceData.lateAttendanceHoursFromGrace) {
                          item["moreDetails"]["lateAttendanceHours"] =
                            "The employee checked in late by " +
                            item.instanceData.lateAttendanceHoursFromGrace +
                            "(HH:MM:SS) excluding the grace period of " +
                            item.instanceData.alwaysGrace +
                            "(HH:MM:SS).Including the grace period, the employee was delayed by " +
                            item.instanceData.lateAttendanceHours +
                            "(HH:MM:SS)";
                        } else {
                          item["moreDetails"]["lateAttendanceHours"] =
                            item.instanceData.lateAttendanceHours;
                        }
                      }
                      item["moreDetails"] = {
                        ...item["moreDetails"],
                        selfApply:
                          item.instanceData.isSelfApply === 1 ? "Yes" : "No",
                        comment: item.instanceData.comment
                          ? item.instanceData.comment
                          : "-",
                        remarks: item.remarks,
                        leaveStatus: item.instanceData.approvalStatus
                          ? item.instanceData.approvalStatus
                          : "-",
                        addedBy: item.instanceData.addedBy
                          ? item.instanceData.addedBy
                          : "-",
                        addedOn: item.instanceData.addedOn
                          ? this.formatDate(item.instanceData.addedOn, true)
                          : "-",
                      };
                      if (
                        item.instanceData.updatedBy &&
                        item.instanceData.updatedOn
                      ) {
                        item["moreDetails"]["updatedBy"] =
                          item.instanceData.updatedBy;
                        item["moreDetails"]["updatedOn"] = item.instanceData
                          .updatedOn
                          ? this.formatDate(item.instanceData.updatedOn, true)
                          : "-";
                      }
                      if (
                        item.instanceData.approvalStatus &&
                        item.instanceData.approvalStatus !== "Applied" &&
                        item.instanceData.approvalStatus !== "Cancel Applied" &&
                        item.instanceData.approvedOn
                      ) {
                        let approvalStatus =
                          item.instanceData.approvalStatus.toLowerCase();
                        item["moreDetails"][approvalStatus + "On"] = item
                          .instanceData.approvedOn
                          ? this.formatDate(item.instanceData.approvedOn, true)
                          : "-";
                      }
                    }
                    // resignation details
                    else if (vm.filterFormId === 34) {
                      item["appliedDate"] = item.instanceData.appliedDate
                        ? this.formatDate(item.instanceData.appliedDate)
                        : "-";
                      item["appliedDateInDateFormat"] = item.instanceData
                        .appliedDate
                        ? new Date(item.instanceData.appliedDate)
                        : "-";
                      item["exitDate"] = item.instanceData.exitDate
                        ? this.formatDate(item.instanceData.exitDate)
                        : "-";
                      item["exitDateInDateFormat"] = item.instanceData.exitDate
                        ? new Date(item.instanceData.exitDate)
                        : "-";
                      item["moreDetails"] = {
                        reasonType: item.instanceData.esicReason
                          ? item.instanceData.esicReason
                          : "-",
                        relievingReason: item.instanceData
                          .relievingReasonComment
                          ? item.instanceData.relievingReasonComment
                          : "-",
                        resignationStatus: item.instanceData.approvalStatus
                          ? item.instanceData.approvalStatus
                          : "-",
                      };
                      if (item.instanceData.approvalStatus === "Withdrawn") {
                        item["moreDetails"]["withdrawnComment"] = item
                          .instanceData.withdrawnCancellationComment
                          ? item.instanceData.withdrawnCancellationComment
                          : "-";
                      } else if (
                        item.instanceData.approvalStatus === "Canceled"
                      ) {
                        item["moreDetails"]["cancellationComment"] = item
                          .instanceData.withdrawnCancellationComment
                          ? item.instanceData.withdrawnCancellationComment
                          : "-";
                      }
                      (item["moreDetails"]["addedBy"] = item.instanceData
                        .initiatorId
                        ? item.instanceData.initiatorId
                        : "-"),
                        (item["moreDetails"]["addedOn"] = item.instanceData
                          .addedOn
                          ? this.formatDate(item.instanceData.addedOn, true)
                          : "-");
                      if (
                        item.instanceData.updatedBy &&
                        item.instanceData.updatedOn
                      ) {
                        item["moreDetails"]["updatedBy"] =
                          item.instanceData.updatedBy;
                        item["moreDetails"]["updatedOn"] = item.instanceData
                          .updatedOn
                          ? this.formatDate(item.instanceData.updatedOn, true)
                          : "-";
                      }
                      if (item.instanceData.approvalStatus) {
                        let approvalStatus =
                          item.instanceData.approvalStatus.toLowerCase();
                        item["moreDetails"][approvalStatus + "On"] = item
                          .instanceData.approvedOn
                          ? this.formatDate(item.instanceData.approvedOn, true)
                          : "-";
                      }
                    }
                    // job post details
                    else if (vm.filterFormId === 15) {
                      item["jobTitle"] = item.instanceData.jobPostName;
                      item["postingDate"] = item.instanceData.postingDate
                        ? this.formatDate(item.instanceData.postingDate)
                        : "-";
                      item["postingDateInDateFormat"] = item.instanceData
                        .postingDate
                        ? new Date(item.instanceData.postingDate)
                        : "-";
                      item["closingDate"] = item.instanceData.closingDate
                        ? this.formatDate(item.instanceData.closingDate)
                        : "-";
                      item["closingDateInDateFormat"] = item.instanceData
                        .closingDate
                        ? new Date(item.instanceData.closingDate)
                        : "-";
                      item["priority"] = item.instanceData.priority;
                      item["moreDetails"] = {
                        jobDescription: item.instanceData.jobDescription
                          ? item.instanceData.jobDescription
                          : "-",
                        noOfVacancies: item.instanceData.noOfVacancies,
                        reasonForOpening: item.instanceData.reasonForOpening
                          ? item.instanceData.reasonForOpening
                          : "-",
                        jobDuration:
                          item.instanceData.jobDuration + " month(s)", // months
                        expectedJoiningDate: this.formatDate(
                          item.instanceData.expectedJoiningDate
                        ),
                        minWorkExperience: item.instanceData.minWorkExperience,
                        maxWorkExperience: item.instanceData.maxWorkExperience,
                        minPaymentFrequency:
                          item.instanceData.minPaymentFrequency,
                        maxPaymentFrequency:
                          item.instanceData.maxPaymentFrequency,
                        agencyInvolved: item.instanceData.agencyInvolved
                          ? item.instanceData.agencyInvolved
                          : "-",
                        coolingPeriod:
                          item.instanceData.coolingPeriod + " month(s)", // month
                        travelRequired: item.instanceData.travelRequired
                          ? "Yes"
                          : "No", // yes/no
                        addedBy: item.instanceData.addedBy
                          ? item.instanceData.addedBy
                          : "-",
                      };
                    }
                    // lop recovery
                    else if (vm.filterFormId === 253) {
                      item["lopRecoveryProcessingMonth"] = item.instanceData
                        .Salary_Payment_Month
                        ? this.convertMonthNumberToString(
                            item.instanceData.Salary_Payment_Month
                          )
                        : "-";
                      item["totalDays"] =
                        item.instanceData.Total_Lop_Recovery_Days;
                      item["totalDays"] = parseFloat(item["totalDays"]);
                      item["totalAmount"] = item.instanceData
                        .Total_Recovery_Amount
                        ? item.instanceData.Total_Recovery_Amount.toFixed(2)
                        : "";
                      item["moreDetails"] = {
                        deductionMonth: item.instanceData.Salary_Deduction_Month
                          ? this.convertMonthNumberToString(
                              item.instanceData.Salary_Deduction_Month
                            )
                          : "-",
                        recoveryDate: item.instanceData.Lop_Recovery_Date
                          ? this.formatMultipleDates(
                              item.instanceData.Lop_Recovery_Date
                            )
                          : "-",
                        leaveName: item.instanceData.Leave_Name
                          ? item.instanceData.Leave_Name
                          : "-",
                        duration: item.instanceData.Duration
                          ? item.instanceData.Duration
                          : "-",
                        period: item.instanceData.Period
                          ? this.containsOnlyCommas(item.instanceData.Period)
                            ? "-"
                            : item.instanceData.Period
                          : "-",
                        reason: item.instanceData.Reason
                          ? item.instanceData.Reason
                          : "-",
                        perDaySalary: item.instanceData.Per_Day_Salary
                          ? item.instanceData.Per_Day_Salary.toFixed(2)
                          : 0,
                        remarks: item.instanceData.Remarks
                          ? item.instanceData.Remarks
                          : "-",
                        status: item.instanceData.Approval_Status
                          ? item.instanceData.Approval_Status
                          : "-",
                        addedBy: item.instanceData.Added_By
                          ? item.instanceData.Added_By
                          : "-",
                        addedOn: item.instanceData.Added_On
                          ? this.formatDate(
                              new Date(item.instanceData.Added_On + ".000Z"),
                              true
                            )
                          : "-",
                      };
                      if (
                        item.instanceData.Updated_By &&
                        item.instanceData.Updated_On
                      ) {
                        item["moreDetails"]["updatedBy"] =
                          item.instanceData.Updated_By;
                        item["moreDetails"]["updatedOn"] = item.instanceData
                          .Updated_On
                          ? this.formatDate(
                              new Date(item.instanceData.Updated_On + ".000Z"),
                              true
                            )
                          : "-";
                      }
                      if (item.taskStatus) {
                        let approvalStatus = item.taskStatus.toLowerCase();
                        item["moreDetails"][approvalStatus + "On"] = item
                          .instanceData.Approved_On
                          ? this.formatDate(
                              new Date(item.instanceData.Approved_On + ".000Z"),
                              true
                            )
                          : "-";
                      }
                    }
                    // reimbursement
                    else if (vm.filterFormId === 267) {
                      item["totalAmount"] = item.instanceData.totalAmount
                        ? item.instanceData.totalAmount
                        : 0;
                      item["totalAppliedAmount"] = item.instanceData
                        .totalAppliedAmount
                        ? item.instanceData.totalAppliedAmount
                        : 0;
                      item["appliedDate"] = this.formatDate(
                        new Date(item.instanceData.submissionDate)
                      );
                      item["startDateInDateFormat"] = item.instanceData
                        .submissionDate
                        ? new Date(item.instanceData.submissionDate)
                        : "-";
                      item["moreDetails"] = {
                        addedBy: item.instanceData.Added_By
                          ? parseInt(item.instanceData.Added_By)
                          : "-",
                        addedOn: item.instanceData.Added_On
                          ? this.formatDate(item.instanceData.Added_On, true)
                          : "-",
                        remarks: item.remarks ? item.remarks : "-",
                        status: item.instanceData.Approval_Status
                          ? item.instanceData.Approval_Status
                          : "-",
                      };
                    }
                    // timesheets
                    else if (vm.filterFormId === 268) {
                      item["totalEffort"] = item.instanceData.totalEffort
                        ? item.instanceData.totalEffort
                        : 0;
                      item["weekendDate"] = item.instanceData.Week_Ending_Date
                        ? this.formatDate(item.instanceData.Week_Ending_Date)
                        : "-";
                      item["startDateInDateFormat"] = item.instanceData
                        .Week_Ending_Date
                        ? new Date(item.instanceData.Week_Ending_Date)
                        : "-";
                      item["moreDetails"] = {
                        status: item.instanceData.Approval_Status
                          ? item.instanceData.Approval_Status
                          : "-",
                        remarks: item.remarks ? item.remarks : "-",
                        addedBy: item.instanceData.Added_By
                          ? parseInt(item.instanceData.Added_By)
                          : "-",
                        addedOn: item.instanceData.Added_On
                          ? this.formatDate(item.instanceData.Added_On, true)
                          : "-",
                      };
                    }
                    // Comp Off
                    else if (vm.filterFormId === 334) {
                      item["workedDate"] = item.instanceData.Worked_Date
                        ? this.formatDate(
                            new Date(item.instanceData.Worked_Date),
                            false
                          )
                        : "";
                      item["duration"] = item.instanceData.Duration
                        ? item.instanceData.Duration
                        : "";
                      item["compOffDate"] = item.instanceData.Compensatory_Date
                        ? this.formatDate(
                            new Date(item.instanceData.Compensatory_Date),
                            false
                          )
                        : "";
                      item["period"] = item.instanceData.Period
                        ? item.instanceData.Period
                        : "-";
                      item["moreDetails"] = {
                        status: item.instanceData?.Approval_Status
                          ? item.instanceData.Approval_Status
                          : "-",
                        addedBy: item.instanceData?.Added_By
                          ? parseInt(item.instanceData?.Added_By)
                          : "-",
                        addedOn: item.instanceData?.Added_On
                          ? this.formatDate(
                              new Date(item.instanceData?.Added_On),
                              true
                            )
                          : "-",
                        updatedBy: item.instanceData?.Updated_By
                          ? parseInt(item.instanceData?.Updated_By)
                          : "-",
                        updatedOn: item.instanceData?.Updated_On
                          ? this.formatDate(
                              new Date(item.instanceData?.Updated_On),
                              true
                            )
                          : "-",
                      };
                    }
                    // Travel Request
                    else if (vm.filterFormId === 341) {
                      item["travelName"] = item.instanceData.Trip_Name
                        ? item.instanceData.Trip_Name
                        : "-";
                      item["travelType"] = item.instanceData.Travel_Type
                        ? item.instanceData.Travel_Type
                        : "-";
                      item["destinationCountry"] = item.instanceData
                        .Destination_Country
                        ? item.instanceData.Destination_Country
                        : "-";
                      item["travelStartDate"] = item.instanceData
                        .Travel_Start_Date
                        ? this.formatDate(item.instanceData.Travel_Start_Date)
                        : "-";
                      item["travelEndDate"] = item.instanceData.Travel_End_Date
                        ? this.formatDate(item.instanceData.Travel_End_Date)
                        : "-";
                      item["amount"] = item.instanceData.Budget_Amount
                        ? item.instanceData.Budget_Amount
                        : "-";
                      item["moreDetails"] = {
                        status: item.instanceData?.Status
                          ? item.instanceData.Status
                          : "-",
                        addedBy: item.instanceData?.Added_By
                          ? parseInt(item.instanceData.Added_By)
                          : "-",
                        addedOn: item.instanceData?.Added_On
                          ? this.formatDate(
                              new Date(item.instanceData.Added_On + ".000Z"),
                              true
                            )
                          : "-",
                        updatedBy: item.instanceData?.Updated_By
                          ? item.instanceData.Updated_By
                          : "-",
                        updatedOn: item.instanceData?.Updated_On
                          ? this.formatDate(
                              new Date(item.instanceData.Updated_On + ".000Z"),
                              true
                            )
                          : "-",
                      };
                    }
                    // Short Time Off Request
                    else if (vm.filterFormId === 352) {
                      // Employee Information
                      item["employeeId"] = item.instanceData.Employee_Id ?? "-";
                      item["employeeName"] =
                        item.instanceData.Employee_Name ?? "-";
                      item["userDefinedEmpId"] =
                        item.instanceData.User_Defined_EmpId ?? "-";
                      // Request Details
                      item["requestFor"] = item.instanceData.Request_For ?? "-";
                      item["startDateTime"] = item.instanceData.Start_Date_Time
                        ? this.formatDateTime(
                            item.instanceData.Start_Date_Time,
                            true
                          )
                        : "-";
                      item["endDateTime"] = item.instanceData.End_Date_Time
                        ? this.formatDateTime(
                            item.instanceData.End_Date_Time,
                            true
                          )
                        : "-";
                      item["viewStartDateTime"] = item.instanceData
                        .View_Start_Date_Time
                        ? this.formatDateTime(
                            item.instanceData.View_Start_Date_Time,
                            true
                          )
                        : "-";
                      item["viewEndDateTime"] = item.instanceData
                        .View_End_Date_Time
                        ? this.formatDateTime(
                            item.instanceData.View_End_Date_Time,
                            true
                          )
                        : "-";

                      // Time Off Details
                      item["totalHours"] =
                        this.formatTotalHours(item.instanceData.Total_Hours) ??
                        "-";
                      item["lateAttendance"] =
                        item.instanceData.Late_Attendance ?? "-";
                      item["earlyCheckout"] =
                        item.instanceData.Early_Checkout ?? "-";
                      item["earlyCheckoutHours"] =
                        item.instanceData.Early_Checkout_Hours ?? "-";

                      // Approval Information
                      item["approvalStatus"] =
                        item.instanceData.Approval_Status ?? "-";
                      item["approverName"] =
                        item.instanceData.Approved_By_Name ?? "-";
                      item["approvedOn"] = item.instanceData.Approved_On
                        ? this.formatDate(item.instanceData.Approved_On, true)
                        : "-";

                      // Additional Information
                      item["reason"] = item.instanceData.Reason ?? "-";
                      item["alternatePerson"] =
                        item.instanceData.AlternatePersonName ?? "-";
                      item["contactDetails"] =
                        item.instanceData.Contact_Details ?? "-";
                      item["autoShortTimeOff"] =
                        item.instanceData.Auto_Short_Time_Off ?? "-";
                      item["serviceProviderId"] =
                        item.instanceData.Service_Provider_Id ?? "-";

                      // Comments/Remarks
                      item["comments"] =
                        item.instanceData.comments?.length > 0
                          ? item.instanceData.comments
                          : [];

                      // More Details (system fields)
                      item["moreDetails"] = {
                        startDateTime: item.instanceData.Start_Date_Time
                          ? this.formatDateTime(
                              item.instanceData.Start_Date_Time,
                              true
                            )
                          : "-",
                        endDateTime: item.instanceData.End_Date_Time
                          ? this.formatDateTime(
                              item.instanceData.End_Date_Time,
                              true
                            )
                          : "-",
                        totalHours: item.instanceData.Total_Hours
                          ? this.formatTotalHours(item.instanceData.Total_Hours)
                          : "-",
                        reason: item.instanceData.Reason
                          ? item.instanceData.Reason
                          : "-",
                        addedBy: item.instanceData.Added_By
                          ? parseInt(item.instanceData.Added_By)
                          : "-",
                        addedOn: item.instanceData?.Added_On
                          ? this.formatDate(item.instanceData.Added_On, true)
                          : "-",
                        ...(item.instanceData.Updated_By &&
                        item.instanceData.Updated_On
                          ? {
                              updatedBy: parseInt(item.instanceData.Updated_By),
                              updatedOn: this.formatDate(
                                item.instanceData.Updated_On,
                                true
                              ),
                            }
                          : {}),
                      };
                    }
                    // Mpp
                    else if (vm.filterFormId == 291 || vm.filterFormId == 290) {
                      item["positionName"] = item.instanceData.positionTitle
                        ? item.instanceData.positionTitle
                        : "-";
                      item["positionCode"] = item.instanceData.positionCode
                        ? item.instanceData.positionCode
                        : "-";
                      item["groupCode"] = item.instanceData.groupCode
                        ? item.instanceData.groupCode
                        : "-";
                      item["divisionCode"] = item.instanceData.divisionCode
                        ? item.instanceData.divisionCode
                        : "-";
                      item["noOfPositions"] = item.instanceData.noOfPosition
                        ? item.instanceData.noOfPosition
                        : 0;
                      item["approvedOn"] = item.instanceData.Approved_On
                        ? item.instanceData.Approved_On
                        : "-";
                      item["moreDetails"] = {
                        departmentCode: item.instanceData.departmentCode
                          ? item.instanceData.departmentCode
                          : "-",
                        sectionCode: item.instanceData.sectionCode
                          ? item.instanceData.sectionCode
                          : "-",
                        costCenter: item.instanceData.costCenter
                          ? item.instanceData.costCenter
                          : "-",
                        employeeType: item.instanceData.employeeTypeName
                          ? item.instanceData.employeeTypeName
                          : "-",
                        addedBy: item.instanceData.Added_By
                          ? item.instanceData.Added_By
                          : "-",
                        addedOn: item.instanceData.Added_On
                          ? item.instanceData.Added_On
                          : "-",
                        updatedOn: item.instanceData.Updated_On
                          ? item.instanceData.Updated_On
                          : "-",
                        updatedBy: item.instanceData.Updated_By
                          ? item.instanceData.Updated_By
                          : "-",
                      };
                    }
                    // pre approvals forms
                    else {
                      item.fileNames = item.instanceData.File_Name
                        ? [item.instanceData.File_Name]
                        : [];
                      item["startDate"] = item.instanceData.Start_Date
                        ? this.formatDate(item.instanceData.Start_Date)
                        : "-";
                      item["startDateInDateFormat"] = item.instanceData
                        .Start_Date
                        ? new Date(item.instanceData.Start_Date)
                        : "-";
                      item["endDate"] = item.instanceData.End_Date
                        ? this.formatDate(item.instanceData.End_Date)
                        : "-";
                      item["endDateInDateFormat"] = item.instanceData.End_Date
                        ? new Date(item.instanceData.End_Date)
                        : "-";
                      item["totalDays"] = item.instanceData.Total_Days;
                      item["totalDays"] = parseFloat(item["totalDays"]);
                      item["moreDetails"] = {
                        duration: item.instanceData.Duration,
                        period: item.instanceData.Period
                          ? item.instanceData.Period
                          : "-",
                        reason: item.instanceData.Reason,
                        status: item.instanceData.Status
                          ? item.instanceData.Status
                          : "-",
                        addedBy: item.instanceData.Added_By
                          ? item.instanceData.Added_By
                          : "-",
                        addedOn: item.instanceData.Added_On
                          ? this.formatDate(
                              new Date(item.instanceData.Added_On + ".000Z"),
                              true
                            )
                          : "-",
                      };
                      if (
                        item.instanceData.Updated_By &&
                        item.instanceData.Updated_On
                      ) {
                        item["moreDetails"]["updatedBy"] =
                          item.instanceData.Updated_By;
                        item["moreDetails"]["updatedOn"] = item.instanceData
                          .Updated_On
                          ? this.formatDate(
                              new Date(item.instanceData.Updated_On + ".000Z"),
                              true
                            )
                          : "-";
                      }
                      if (item.instanceData.Status) {
                        let approvalStatus =
                          item.instanceData.Status.toLowerCase();
                        item["moreDetails"][approvalStatus + "On"] = item
                          .instanceData.Approved_On
                          ? this.formatDate(
                              new Date(item.instanceData.Approved_On + ".000Z"),
                              true
                            )
                          : "-";
                      }
                    }
                    return item;
                  });
                  if (vm.approvalType === "Approval History") {
                    vm.fetchAllEmployeesList(
                      approvalResponse,
                      "Approval History"
                    );
                  }
                } else {
                  vm.listLoading = false;
                  vm.approvalHistoryList = [];
                  vm.approvalHistoryListBackup = [];
                }
              } else {
                vm.handleTaskHistoryError();
              }
            })
            .catch(function (e) {
              vm.handleTaskHistoryError(e);
            });
        } catch {
          vm.handleTaskHistoryError();
        }
      }
    },

    handleTaskHistoryError(err = "") {
      if (err && err.response && err.response.data) {
        let errorCode = err.response.data.errorCode;
        if (errorCode) {
          switch (errorCode) {
            case "ERR-753": // task id not found
              this.errorContent =
                "Unable to retrieve the approval history. If you continue to see this issue please contact the platform administrator.";
              break;
            case "ERR-798": // Error while executing the query
            default:
              this.errorContent =
                "Something went wrong while retrieving the approval history. If you continue to see this issue please contact the platform administrator.";
              break;
          }
        } else {
          this.errorContent =
            "Something went wrong while retrieving the approval history. Please try after some time.";
        }
      } else {
        this.errorContent =
          "Something went wrong while retrieving the approval history. Please try after some time.";
      }
      this.listLoading = false;
      this.isErrorInList = true;
    },

    convertMonthNumberToString(originalDate) {
      const parsedDate = moment(originalDate, "MM,YYYY");
      const formattedDate = parsedDate.format("MMM, YYYY");
      return formattedDate;
    },

    formatMultipleDates(multiDateString) {
      let multiDates = multiDateString.split(",");
      let mDates = [];
      for (let multiDate of multiDates) {
        mDates.push(this.formatDate(multiDate));
      }
      return mDates.join(", ");
    },

    containsOnlyCommas(str) {
      // Use a regular expression to check if the string contains only commas
      return /^,+$/g.test(str);
    },

    refetchAPIs() {
      if (this.approvalType === "Approval History") {
        this.approvalHistoryCallCount = 0;
        this.retrieveUserTaskHistory();
      } else if (this.approvalType === "Group Approvals") {
        this.approvalListCallCount = 0;
        if (this.isGroupIdsFetched && this.isServiceProviderEmpsFetched) {
          this.retrieveUserTask(this.approvalType);
        } else {
          this.isLoading = true;
          this.groupDependencyApiCallCount = 0;
          this.fetchGroupIds();
          this.fetchServiceProviderEmployees();
        }
      } else {
        this.approvalListCallCount = 0;
        this.approvalList = [];
        this.retrieveUserTask(this.approvalType);
      }
    },

    reloadGrid() {
      this.leaveTypeList = [];
      this.refetchUserTaskAndCountAPIs("reloadGrid");
    },

    refetchUserTaskAndCountAPIs(calledFrom = "") {
      this.showInvoiceDetailsModal = false;
      this.showTimesheetDetailsModal = false;
      this.apiCallType = calledFrom;
      if (calledFrom !== "statusUpdate") {
        this.resetFormValues();
      } else {
        this.statusApprovalCount += 1;
        this.$store.commit("UPDATE_APPROVAL_COUNT", this.statusApprovalCount);
      }
      this.approvalHistoryRetrieved = [];
      this.approvalsRetrieved = [];
      this.refetchAPIs();
      this.refetchUserTaskCounts();
    },

    refetchUserTaskCounts() {
      if (this.approvalType !== "Approval History") {
        this.isLoading = true;
        this.userTaskCountApiCallCount = 0;
        this.myApprovalsCount = 0;
        this.groupApprovalsCount = 0;
        this.outstandingApprovalsCount = 0;
        if (this.isGroupIdsFetched && this.isServiceProviderEmpsFetched) {
          this.retrieveOutstandingApprovalsCount();
          this.retrieveMyAndGroupApprovalsCount();
        } else {
          this.taskCountDependApiCallCount = 0;
          this.fetchGroupIds();
          this.fetchServiceProviderEmployees();
        }
      }
    },

    handleEditSuccess() {
      this.closeViewForm();
      this.refetchUserTaskAndCountAPIs("editSuccess");
    },

    retrieveMyAndGroupApprovalsCount() {
      let vm = this;
      let myTaskCountFilter = [
        {
          key: "assignee",
          value: [vm.loginEmployeeId],
          operator: "in",
        },
      ];
      if (
        vm.isServiceProviderAdmin &&
        (vm.filterFormId === 31 || vm.filterFormId === 34)
      ) {
        myTaskCountFilter.push(
          {
            key: "custom_group_id",
            value: vm.groupId.length > 0 ? vm.groupId : ["-"],
            operator: "in",
            Employee_Id: vm.serviceProviderEmployees,
          },
          {
            key: "Employee_Id",
            value: vm.serviceProviderEmployees,
            operator: "in",
            condition: "and",
          }
        );
      } else {
        myTaskCountFilter.push({
          key: "custom_group_id",
          value: vm.groupId.length > 0 ? vm.groupId : ["-"],
          operator: "in",
        });
      }
      this.retrieveUserTaskCount(myTaskCountFilter);
    },

    retrieveOutstandingApprovalsCount() {
      if (this.checkAnyOneAdmin) {
        let myTaskCountFilter = [
          {
            key: "assignee",
            value: [0],
            operator: "not in",
          },
        ];
        this.retrieveUserTaskCount(myTaskCountFilter, true);
      } else {
        this.userTaskCountApiCallCount += 1;
      }
    },

    retrieveUserTaskCount(myTaskCountFilter, isOutstandingCall = false) {
      let vm = this;
      try {
        axios
          .post(
            Config.workflowUrl + "/workflow/userTaskCount",
            {
              filter: myTaskCountFilter,
              form_id: vm.filterFormId,
            },
            {
              headers: {
                org_code: vm.orgCode,
                employee_id: vm.loginEmployeeId,
                db_prefix: vm.domainName,
                Authorization: window.$cookies.get("accessToken")
                  ? window.$cookies.get("accessToken")
                  : "",
                refresh_token: window.$cookies.get("refreshToken")
                  ? window.$cookies.get("refreshToken")
                  : null,
                partnerid: window.$cookies.get("partnerid")
                  ? window.$cookies.get("partnerid")
                  : "-",
              },
            }
          )
          .then((response) => {
            if (response && response.data && response.data.message) {
              let countResponse = JSON.parse(response.data.message);
              if (isOutstandingCall) {
                vm.outstandingApprovalsCount = countResponse.assignee.taskcount;
              } else {
                vm.groupApprovalsCount =
                  countResponse.custom_group_id.taskcount;
                vm.myApprovalsCount = countResponse.assignee.taskcount;
              }
              vm.userTaskCountApiCallCount += 1;
            } else {
              vm.myApprovalsCount = 0;
              vm.groupApprovalsCount = 0;
              vm.outstandingApprovalsCount = 0;
              vm.userTaskCountApiCallCount += 1;
            }
          })
          .catch(function () {
            vm.myApprovalsCount = 0;
            vm.groupApprovalsCount = 0;
            vm.outstandingApprovalsCount = 0;
            vm.userTaskCountApiCallCount += 1;
          });
      } catch {
        vm.myApprovalsCount = 0;
        vm.groupApprovalsCount = 0;
        vm.outstandingApprovalsCount = 0;
        vm.userTaskCountApiCallCount += 1;
      }
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>

<style>
.approvals-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .approvals-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
