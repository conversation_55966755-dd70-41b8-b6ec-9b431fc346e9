<template>
  <div class="pb-10">
    <div v-if="isMounted">
      <v-card
        v-if="!jobPostListLoading && jobPostId"
        color="primary"
        elevation="0"
        class="pa-4 text-center"
      >
        <v-row class="d-flex justify-center align-center">
          <v-col
            cols="12"
            sm="4"
            xs="12"
            class="text-h5"
            :class="windowWidth > 600 ? 'text-left' : 'text-center'"
          >
            <div>{{ orgName }}</div>
          </v-col>
          <v-col cols="12" sm="5" xs="12" class="text-center text-h5">
            <div>Job Title: {{ jobPostName }}</div>
          </v-col>
          <v-col
            cols="12"
            sm="3"
            xs="12"
            :class="
              preferredLocationName
                ? 'flex-row justify-space-between'
                : 'justify-end'
            "
            class="d-flex align-center"
          >
            <div v-if="preferredLocationName" class="d-flex align-center">
              <div
                style="
                  background: #fff;
                  border-radius: 50%;
                  height: 10px;
                  width: 10px;
                "
              ></div>
              <div class="pl-1 text-white">{{ preferredLocationName }}</div>
            </div>
            <div v-if="isSigninEnabled" class="d-flex align-center justify-end">
              <v-menu
                :open-on-hover="false"
                :close-on-content-click="false"
                :open-on-click="true"
                v-model="menuOpen"
              >
                <template v-slot:activator="{ props }">
                  <v-icon v-bind="props" size="20" class="fas fa-user" />
                </template>
                <v-list>
                  <v-list-item
                    v-for="(item, index) in menuItems"
                    :key="index"
                    @click="handleMenuClick(item.action)"
                  >
                    <v-list-item-title
                      ><span class="mr-2"
                        ><v-icon
                          :class="item.icon"
                          color="primary"
                          size="15" /></span
                      >{{ item.title }}</v-list-item-title
                    >
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </v-col>
        </v-row>
      </v-card>
      <v-row
        class="d-flex justify-center align-center text-center"
        v-if="jobHeader && showJobHeader"
      >
        <v-col cols="12">
          <v-card
            rounded="xs"
            elevation="0"
            class="pa-4 text-center container bg-hover"
          >
            <div class="text-h6">EQUAL OPPORTUNITY STATEMENT</div>
            <div class="d-flex justify-center align-center mr-1 text-justify">
              {{ jobHeader }}
            </div>
          </v-card>
        </v-col>
      </v-row>
      <section v-if="showForm || !jobPostId">
        <v-form
          v-if="(!jobPostId || !isSimpleForm) && !presentSimpleForm"
          ref="editDetailsForm"
          :class="jobPostId ? 'pa-12' : 'pa-2'"
        >
          <div class="mt-2">
            <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold"
              >Personal Details</span
            >
            <v-card class="bg-hover">
              <div class="text-subtitle-2 pa-3 text-grey-darken-1">
                Note: The personal data collected will be used for demographic
                representation purposes in the recruitment process. Rest assured
                that all data will be handled with strict confidentiality, in
                compliance with applicable data protection laws.
              </div>
            </v-card>
            <v-row class="mt-4">
              <v-col cols="12" v-if="!candidateIdSelected && !isUserLogedIn">
                <drag-and-drop-files
                  :mandatory="true"
                  :oldFileName="''"
                  :isError="errorMessage"
                  :fileCategory="'document'"
                  @file-event-success="onFileChange"
                ></drag-and-drop-files>
              </v-col>
              <v-col
                v-else-if="(jobPostId && isUserLogedIn) || isPortalLogedIn"
                cols="12"
              >
                <div
                  style="border: 1px solid #000; border-radius: 10px"
                  class="py-10 d-flex align-center justify-center"
                >
                  <span
                    class="text-green cursor-pointer"
                    style="text-decoration: underline"
                    @click="openResumeModel = true"
                    >View Resume & other attachments
                  </span>
                </div></v-col
              >
              <!-- <v-col cols="12" md="4" sm="6">
              <CustomSelect
                :items="['Mr', 'Miss', 'Mrs', 'Dr', 'Prof']"
                label="Salutation"
                :isRequired="true"
                :itemSelected="editedDetails.Salutation"
                :rules="[required('Salutation', editedDetails.Salutation)]"
                @selected-item="onChangeCustomSelectField($event, 'Salutation')"
                ref="salutation"
              ></CustomSelect>
            </v-col>-->
              <v-col cols="12" md="4" sm="6">
                <v-text-field
                  v-model="editedDetails.Candidate_First_Name"
                  :rules="[
                    required('First Name', editedDetails.Candidate_First_Name),
                    validateWithRulesAndReturnMessages(
                      editedDetails.Candidate_First_Name,
                      'empFirstName',
                      'First Name'
                    ),
                  ]"
                  :disabled="isUserLogedIn"
                  variant="solo"
                  ref="firstName"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    First Name
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <v-text-field
                  v-model="editedDetails.Candidate_Middle_Name"
                  :rules="
                    checkFieldAvailability(editedDetails.Candidate_Middle_Name)
                      ? [
                          validateWithRulesAndReturnMessages(
                            editedDetails.Candidate_Middle_Name,
                            'empMiddleName',
                            'Middle Name'
                          ),
                        ]
                      : [true]
                  "
                  variant="solo"
                  ref="middleName"
                  label="Middle Name"
                  @update:model-value="onChangeFields()"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <v-text-field
                  v-model="editedDetails.Candidate_Last_Name"
                  :rules="[
                    required('Last Name', editedDetails.Candidate_Last_Name),
                    validateWithRulesAndReturnMessages(
                      editedDetails.Candidate_Last_Name,
                      'empLastName',
                      'Last Name'
                    ),
                  ]"
                  variant="solo"
                  ref="lastName"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    Last Name
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <!-- Nick Name -->
              <v-col
                v-if="labelList[328]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  v-model="editedDetails.Emp_Pref_First_Name"
                  :rules="[
                    labelList[328]?.Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          labelList[328]?.Field_Alias,
                          editedDetails.Emp_Pref_First_Name
                        )
                      : true,

                    validateWithRulesAndReturnMessages(
                      editedDetails.Emp_Pref_First_Name,
                      'knownAs',
                      labelList[328]?.Field_Alias
                    ),
                  ]"
                  clearable
                  variant="solo"
                  ref="knownAs"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[328]?.Field_Alias }}
                    <span
                      v-if="
                        labelList[328]?.Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[268].Field_Visiblity == 'Yes'"
              >
                <v-text-field
                  v-model="editedDetails.Suffix"
                  variant="solo"
                  :rules="
                    labelList[268].Mandatory_Field == 'Yes'
                      ? [
                          required(
                            labelList[268].Field_Alias,
                            editedDetails.Suffix
                          ),
                        ]
                      : []
                  "
                  ref="suffix"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[268].Field_Alias }}
                    <span v-if="labelList[268].Mandatory_Field == 'Yes'"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <v-menu
                  v-model="dobMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      v-model="formattedDob"
                      prepend-inner-icon="fas fa-calendar"
                      :rules="[required('Date of Birth', formattedDob)]"
                      readonly
                      :disabled="isUserLogedIn"
                      v-bind="props"
                      variant="solo"
                      ref="candidateDob"
                    >
                      <template v-slot:label>
                        Date of Birth<span style="color: red">*</span>
                      </template></v-text-field
                    >
                  </template>
                  <v-date-picker
                    v-if="editedDetails.DOB"
                    v-model="editedDetails.DOB"
                    :max="maximumBirthDate"
                    :min="minimumBirthDate"
                    @update:modelValue="onChangeFields()"
                  />
                  <v-date-picker
                    v-else
                    v-model="defaultDOB"
                    @update:modelValue="onChangeDefaultDOB(defaultDOB)"
                    :max="maximumBirthDate"
                    :min="minimumBirthDate"
                  />
                </v-menu>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[267].Field_Visiblity == 'Yes'"
              >
                <CustomSelect
                  :items="genderList"
                  :label="labelList[267].Field_Alias"
                  :isRequired="
                    labelList[267].Mandatory_Field == 'Yes' ? true : false
                  "
                  :rules="
                    editedDetails.Gender_Id == 0 &&
                    labelList.Mandatory_Field == 'No'
                      ? [true]
                      : [
                          required(
                            labelList[267].Field_Alias,
                            editedDetails.Gender_Id + 1
                          ),
                        ]
                  "
                  :itemSelected="editedDetails.Gender_Id"
                  ref="gender"
                  itemValue="genderId"
                  itemTitle="gender"
                  :isLoading="genderListLoading"
                  :noDataText="
                    genderListLoading ? 'Loading...' : 'No data available'
                  "
                  @selected-item="
                    onChangeCustomSelectField($event, 'Gender_Id')
                  "
                ></CustomSelect>
              </v-col>
              <!-- Gender Identity -->
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[329]?.Field_Visiblity?.toLowerCase() == 'yes'"
              >
                <CustomSelect
                  :items="genderIdentityList"
                  :label="labelList[329]?.Field_Alias"
                  v-model="editedDetails.Gender_Identity_Id"
                  :isRequired="
                    labelList[329]?.Mandatory_Field?.toLowerCase() == 'yes'
                      ? true
                      : false
                  "
                  :rules="
                    labelList['329'].Mandatory_Field === 'Yes'
                      ? [
                          required(
                            labelList['329'].Field_Alias,
                            editedDetails.Gender_Identity_Id
                          ),
                        ]
                      : [true]
                  "
                  clearable
                  :itemSelected="editedDetails.Gender_Identity_Id"
                  ref="genderIdentityId"
                  itemValue="Gender_Identity_Id"
                  itemTitle="Gender_Identity"
                  :isLoading="dropdownLoading"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Gender_Identity_Id')
                  "
                />
              </v-col>
              <!-- Gender Expression -->
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[330]?.Field_Visiblity?.toLowerCase() == 'yes'"
              >
                <CustomSelect
                  :items="genderExpressionList"
                  :label="labelList[330]?.Field_Alias"
                  v-model="editedDetails.Gender_Expression_Id"
                  :isRequired="
                    labelList[330]?.Mandatory_Field?.toLowerCase() == 'yes'
                      ? true
                      : false
                  "
                  :rules="
                    labelList['330'].Mandatory_Field === 'Yes'
                      ? [
                          required(
                            labelList['330'].Field_Alias,
                            editedDetails.Gender_Expression_Id
                          ),
                        ]
                      : [true]
                  "
                  clearable
                  :itemSelected="editedDetails.Gender_Expression_Id"
                  ref="genderExpressionId"
                  itemValue="Gender_Expression_Id"
                  itemTitle="Gender_Expression"
                  :isLoading="dropdownLoading"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Gender_Expression_Id')
                  "
                />
              </v-col>
              <v-col
                v-if="labelList[209].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <CustomSelect
                  :items="pronounList"
                  item-title="Gender_Pronoun_Name"
                  item-value="Gender_Pronoun_Name"
                  :isLoading="dropdownLoading"
                  :label="labelList[209].Field_Alias"
                  :itemSelected="editedDetails.Pronoun"
                  ref="pronoun"
                  :rules="[
                    labelList[209].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[209].Field_Alias,
                          editedDetails.Pronoun
                        )
                      : true,
                  ]"
                  clearable
                  :isRequired="labelList[209].Mandatory_Field === 'Yes'"
                  @selected-item="onChangeCustomSelectField($event, 'Pronoun')"
                ></CustomSelect>
              </v-col>
              <v-col
                v-if="labelList[210].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <CustomSelect
                  :items="genderOrientationList"
                  item-title="Gender_Orientations_Name"
                  item-value="Gender_Orientations_Name"
                  :isLoading="dropdownLoading"
                  :label="labelList[210].Field_Alias"
                  :itemSelected="editedDetails.Gender_Orientations"
                  ref="genderOrientation"
                  :rules="[
                    labelList[210].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[210].Field_Alias,
                          editedDetails.Gender_Orientations
                        )
                      : true,
                  ]"
                  :isRequired="labelList[210].Mandatory_Field === 'Yes'"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Gender_Orientations')
                  "
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[325]?.Field_Visiblity?.toLowerCase() === 'yes'"
              >
                <CustomSelect
                  :items="languageList"
                  :label="labelList[325]?.Field_Alias"
                  :isAutoComplete="true"
                  :isLoading="languageListLoading"
                  :noDataText="
                    languageListLoading ? 'Loading...' : 'No data available'
                  "
                  :itemSelected="editedDetails.Lang_Known"
                  itemValue="Lang_Id"
                  itemTitle="Language_Name"
                  :isRequired="
                    labelList[325]?.Mandatory_Field?.toLowerCase() === 'yes'
                  "
                  :rules="[
                    labelList[325]?.Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          'Languages Known',
                          editedDetails.Lang_Known?.length
                            ? editedDetails.Lang_Known
                            : null
                        )
                      : true,
                  ]"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Lang_Known')
                  "
                  ref="languageKnown"
                  :selectProperties="{
                    multiple: true,
                    chips: true,
                    clearable: true,
                    closableChips: true,
                  }"
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[262].Field_Visiblity.toLowerCase() == 'yes'"
              >
                <CustomSelect
                  :label="labelList[262].Field_Alias"
                  :items="[
                    'A+',
                    'A-',
                    'B+',
                    'B-',
                    'AB+',
                    'AB-',
                    'O+',
                    'O-',
                    'A1B+',
                    'Unknown',
                  ]"
                  :isRequired="
                    labelList[262].Mandatory_Field?.toLowerCase() === 'yes'
                  "
                  :rules="
                    labelList[262].Mandatory_Field == 'Yes'
                      ? [
                          required(
                            labelList[262].Field_Alias,
                            editedDetails.Blood_Group
                          ),
                        ]
                      : []
                  "
                  ref="bloodGroup"
                  :itemSelected="editedDetails.Blood_Group"
                  :selectProperties="{
                    clearable: true,
                  }"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Blood_Group')
                  "
                >
                </CustomSelect>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[270].Field_Visiblity"
              >
                <CustomSelect
                  :items="maritalStatusList"
                  item-title="Marital_Status"
                  item-value="Marital_Status_Id"
                  :label="labelList[270].Field_Alias"
                  :isRequired="
                    labelList[270].Mandatory_Field == 'Yes' ? true : false
                  "
                  :rules="[
                    labelList[270].Mandatory_Field === 'Yes'
                      ? required(
                          labelList[270].Field_Alias,
                          editedDetails.Marital_Status !== null &&
                            editedDetails.Marital_Status !== undefined
                            ? editedDetails.Marital_Status + 1
                            : 0
                        )
                      : true,
                  ]"
                  ref="maritalStatus"
                  :selectProperties="{
                    clearable: true,
                  }"
                  :isLoading="maritalStatusListLoading"
                  :itemSelected="editedDetails.Marital_Status"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Marital_Status')
                  "
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[271].Field_Visiblity == 'Yes'"
              >
                <CustomSelect
                  v-if="labelList['271'].Predefined === 'Yes'"
                  :items="nationalityList"
                  ref="nationalityList"
                  :label="labelList['271'].Field_Alias"
                  :isRequired="labelList['271'].Mandatory_Field === 'Yes'"
                  itemTitle="nationality"
                  itemValue="nationalityId"
                  :isAutoComplete="true"
                  :isLoading="nationalityListLoading"
                  :itemSelected="editedDetails.Nationality_Id"
                  :rules="[
                    labelList['271'].Mandatory_Field === 'Yes'
                      ? required(
                          labelList['271'].Field_Alias,
                          editedDetails.Nationality_Id
                        )
                      : true,
                  ]"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Nationality_Id')
                  "
                ></CustomSelect>
                <v-text-field
                  v-else
                  label="Nationality"
                  v-model="editedDetails.Nationality"
                  :rules="
                    checkFieldAvailability(editedDetails.Nationality)
                      ? labelList[271].Mandatory_Field == 'Yes'
                        ? [
                            validateWithRulesAndReturnMessages(
                              editedDetails.Nationality,
                              'nationality',
                              labelList[271].Field_Alias
                            ),
                            required(
                              labelList[271].Field_Alias,
                              editedDetails.Nationality
                            ),
                          ]
                        : [
                            validateWithRulesAndReturnMessages(
                              editedDetails.Nationality,
                              'nationality',
                              labelList[271].Field_Alias
                            ),
                          ]
                      : labelList[271].Mandatory_Field == 'Yes'
                      ? [
                          required(
                            labelList[271].Field_Alias,
                            editedDetails.Nationality
                          ),
                        ]
                      : []
                  "
                  variant="solo"
                  ref="nationalityText"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[271].Field_Alias }}
                    <span
                      v-if="labelList[271].Mandatory_Field == 'Yes'"
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[264].Field_Visiblity == 'Yes'"
              >
                <v-text-field
                  v-model="editedDetails.Father_Name"
                  :rules="
                    checkFieldAvailability(editedDetails.Father_Name)
                      ? labelList[264].Mandatory_Field == 'Yes'
                        ? [
                            validateWithRulesAndReturnMessages(
                              editedDetails.Father_Name,
                              'empLastName',
                              labelList[264].Field_Alias
                            ),
                            required(
                              labelList[264].Field_Alias,
                              editedDetails.Father_Name
                            ),
                          ]
                        : [
                            validateWithRulesAndReturnMessages(
                              editedDetails.Father_Name,
                              'empLastName',
                              labelList[264].Field_Alias
                            ),
                          ]
                      : labelList[264].Mandatory_Field == 'Yes'
                      ? [
                          required(
                            labelList[264].Field_Alias,
                            editedDetails.Father_Name
                          ),
                        ]
                      : [true]
                  "
                  variant="solo"
                  ref="fathersName"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[264].Field_Alias }}
                    <span v-if="labelList[264].Mandatory_Field == 'Yes'"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[263].Field_Visiblity == 'Yes'"
              >
                <v-text-field
                  v-model="editedDetails.Mother_Name"
                  :label="`Mother's Name`"
                  :rules="
                    checkFieldAvailability(editedDetails.Mother_Name)
                      ? labelList[263].Mandatory_Field == 'Yes'
                        ? [
                            validateWithRulesAndReturnMessages(
                              editedDetails.Mother_Name,
                              'empLastName',
                              labelList[263].Field_Alias
                            ),
                            required(
                              labelList[263].Field_Alias,
                              editedDetails.Mother_Name
                            ),
                          ]
                        : [
                            validateWithRulesAndReturnMessages(
                              editedDetails.Mother_Name,
                              'empLastName',
                              labelList[263].Field_Alias
                            ),
                          ]
                      : labelList[263].Mandatory_Field == 'Yes'
                      ? [
                          required(
                            labelList[263].Field_Alias,
                            editedDetails.Mother_Name
                          ),
                        ]
                      : [true]
                  "
                  variant="solo"
                  ref="mothersName"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[263].Field_Alias }}
                    <span v-if="labelList[263].Mandatory_Field == 'Yes'"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                v-if="
                  labelList['220'] && labelList['220'].Field_Visiblity === 'Yes'
                "
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  v-model="editedDetails.National_Identification_Number"
                  :label="labelList['220'].Field_Alias"
                  :rules="[
                    labelList['220'].Mandatory_Field === 'Yes'
                      ? required(
                          `${labelList['220'].Field_Alias}`,
                          editedDetails.National_Identification_Number
                        )
                      : true,
                    editedDetails.National_Identification_Number
                      ? checkFieldAvailability(
                          editedDetails.National_Identification_Number,
                          'aadhar',
                          `${labelList['220'].Field_Alias}`
                        )
                      : true,
                  ]"
                  ref="nationalIdentityNumber"
                  @update:model-value="onChangeFields()"
                  variant="solo"
                >
                  <template v-slot:label>
                    <span>{{ labelList[220].Field_Alias }}</span>
                    <span
                      v-if="labelList[220].Mandatory_Field == 'Yes'"
                      class="ml-1"
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[265].Field_Visiblity == 'Yes'"
              >
                <CustomSelect
                  :items="workPermitList"
                  :isLoading="dropdownListLoading"
                  :label="labelList[265].Field_Alias"
                  :rules="
                    labelList[265].Mandatory_Field == 'Yes'
                      ? [
                          required(
                            labelList[265].Field_Alias,
                            editedDetails.Work_Permit_Ids
                          ),
                        ]
                      : []
                  "
                  :isRequired="
                    labelList[265].Mandatory_Field == 'Yes' ? true : false
                  "
                  ref="workPermit"
                  itemValue="Work_Authorization_Id"
                  itemTitle="Work_Authorization_Name"
                  :selectProperties="{
                    multiple: true,
                    chips: true,
                    clearable: true,
                    closableChips: true,
                  }"
                  :itemSelected="editedDetails.Work_Permit_Ids"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Work_Permit_Ids')
                  "
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[266].Field_Visiblity == 'Yes'"
              >
                <v-text-field
                  v-model="editedDetails.Other_Work_Permit"
                  variant="solo"
                  :rules="
                    checkFieldAvailability(editedDetails.Other_Work_Permit)
                      ? [
                          validateWithRulesAndReturnMessages(
                            editedDetails.Other_Work_Permit,
                            'empLastName',
                            'Other Work Permit'
                          ),
                        ]
                      : [true]
                  "
                  ref="otherWorkPermit"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    <span>{{ labelList[266].Field_Alias }}</span>
                    <span
                      v-if="labelList[266].Mandatory_Field == 'Yes'"
                      class="ml-1"
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                v-if="
                  labelList[287].Field_Visiblity == 'Yes' && !isPortalLogedIn
                "
                cols="12"
                md="4"
                sm="6"
              >
                <CustomSelect
                  :items="sourceList"
                  :isLoading="sourceListLoading"
                  :noDataText="
                    sourceListLoading ? 'Loading...' : 'No data available'
                  "
                  :label="labelList[287].Field_Alias"
                  :isRequired="labelList[287].Mandatory_Field == 'Yes'"
                  :itemSelected="
                    editedDetails.Source ? editedDetails.Source : null
                  "
                  :rules="[
                    labelList[287].Mandatory_Field == 'Yes'
                      ? required(
                          `${labelList[287].Field_Alias}`,
                          editedDetails.Source
                        )
                      : true,
                  ]"
                  :disabled="jobPostId || isUserLogedIn"
                  itemValue="Source_Title"
                  itemTitle="Source_Title"
                  :isAutoComplete="true"
                  ref="source"
                  @selected-item="onChangeCustomSelectField($event, 'Source')"
                ></CustomSelect>
              </v-col>
              <!-- Physically Challenged -->
              <v-col
                v-if="labelList[331]?.Field_Visiblity?.toLowerCase() == 'yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <div>
                  <div class="text-grey-darken-1" style="font-size: 16px">
                    {{ labelList[331]?.Field_Alias }}
                  </div>
                  <v-switch
                    color="primary"
                    v-model="editedDetails.Physically_Challenged"
                    :true-value="1"
                    :false-value="0"
                    class="ml-1"
                    @update:model-value="onChangeFields()"
                  />
                </div>
              </v-col>
            </v-row>
          </div>
          <div
            class="mt-3"
            v-if="labelList[383]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold">
              Language(s) Known
            </span>
            <v-row class="mt-4">
              <v-col
                v-for="(lang, index) in editedDetails.Lang_Known"
                :key="index"
                cols="12"
                md="12"
              >
                <v-row>
                  <!-- Language Known -->
                  <v-col cols="12" md="4" sm="6">
                    <CustomSelect
                      v-model="lang.Lang_Id"
                      :ref="`langKnown${index}`"
                      :items="languageList"
                      :label="labelList[383]?.Field_Alias"
                      :isRequired="
                        labelList[383]?.Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      :rules="
                        labelList['383'].Mandatory_Field === 'Yes'
                          ? [
                              required(
                                labelList['383'].Field_Alias,
                                lang.Lang_Id
                              ),
                            ]
                          : [true]
                      "
                      clearable
                      :itemSelected="lang.Lang_Id"
                      itemValue="Lang_Id"
                      itemTitle="Language_Name"
                      :isLoading="languageListLoading"
                      @selected-item="
                        onChangeCustomSelectField($event, 'Lang_Id', index)
                      "
                    />
                  </v-col>

                  <!-- Spoken -->
                  <v-col cols="12" md="2" sm="3">
                    <div class="text-grey-darken-1" style="font-size: 16px">
                      {{ labelList[356]?.Field_Alias }}
                    </div>
                    <v-switch
                      v-model="lang.langSpoken"
                      :true-value="1"
                      :false-value="0"
                      color="primary"
                      class="ml-1"
                    />
                  </v-col>

                  <!-- Read/Write -->
                  <v-col cols="12" md="2" sm="3">
                    <div class="text-grey-darken-1" style="font-size: 16px">
                      {{ labelList[357]?.Field_Alias }}
                    </div>
                    <v-switch
                      v-model="lang.langReadWrite"
                      :true-value="1"
                      :false-value="0"
                      color="primary"
                      class="ml-1"
                    />
                  </v-col>

                  <!-- Proficiency -->
                  <v-col cols="12" md="4" sm="6">
                    <div class="d-flex align-center">
                      <CustomSelect
                        v-if="
                          labelList[358]?.Predefined?.toLowerCase() === 'yes'
                        "
                        :items="languageProficiencyList"
                        :label="labelList[358].Field_Alias"
                        :isAutoComplete="true"
                        :isLoading="languageListLoading"
                        :itemSelected="lang.langProficiency"
                        v-model="lang.langProficiency"
                        itemValue="Language_Proficiency"
                        itemTitle="Language_Proficiency"
                        :isRequired="
                          labelList[358].Mandatory_Field?.toLowerCase() ===
                          'yes'
                        "
                        class="flex-grow-1"
                        max-width="75%"
                        variant="solo"
                        clearable
                        :rules="[
                          labelList[358]?.Mandatory_Field?.toLowerCase() ===
                          'yes'
                            ? required(
                                labelList[358]?.Field_Alias,
                                lang.langProficiency
                              )
                            : true,
                        ]"
                        :ref="`langProficiency${index}`"
                        @selected-item="
                          onChangeCustomSelectField(
                            $event,
                            'langProficiency',
                            index
                          )
                        "
                      />
                      <v-text-field
                        v-else
                        v-model="lang.langProficiency"
                        :ref="`langProficiency${index}`"
                        clearable
                        :rules="[
                          labelList[358]?.Mandatory_Field?.toLowerCase() ===
                          'yes'
                            ? required(
                                labelList[358]?.Field_Alias,
                                lang.langProficiency
                              )
                            : true,
                          validateWithRulesAndReturnMessages(
                            lang.langProficiency,
                            'skillName',
                            labelList[358]?.Field_Alias
                          ),
                        ]"
                        class="flex-grow-1"
                        max-width="75%"
                        variant="solo"
                      >
                        <template v-slot:label>
                          <span>{{ labelList[358]?.Field_Alias }}</span>
                          <span
                            v-if="
                              labelList[358]?.Mandatory_Field?.toLowerCase() ===
                              'yes'
                            "
                            style="color: red"
                          >
                            *
                          </span>
                        </template>
                      </v-text-field>
                      <div class="ml-2">
                        <v-icon
                          v-if="editedDetails.Lang_Known.length > 1"
                          size="15"
                          class="fas fa-trash"
                          color="primary"
                          @click="editedDetails.Lang_Known.splice(index, 1)"
                        />
                        <v-btn
                          v-if="index === editedDetails.Lang_Known.length - 1"
                          color="primary"
                          class=""
                          variant="text"
                          @click="
                            editedDetails.Lang_Known.push({
                              langKnown: null,
                              langSpoken: 0,
                              langReadWrite: 0,
                              langProficiency: null,
                            })
                          "
                        >
                          <v-icon class="mr-1" size="15">fas fa-plus</v-icon>
                          Add New
                        </v-btn>
                      </div>
                    </div>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </div>

          <div class="mt-6">
            <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold"
              >Job Details</span
            >
            <v-row class="mt-4">
              <v-col
                v-if="!jobPostId && !isPortalLogedIn"
                cols="12"
                md="4"
                sm="6"
              >
                <div class="d-flex mt-n2" v-if="disableJobPost && !jobPostId">
                  <p class="custom-label">
                    Job Title
                    <span style="color: red">*</span>
                  </p>
                  <v-tooltip location="right">
                    <template v-slot:activator="{ props }">
                      <v-icon
                        class="ml-1"
                        v-bind="props"
                        size="small"
                        color="info"
                        >fas fa-info-circle</v-icon
                      >
                    </template>
                    <div style="max-width: 350px !important">
                      Job title cannot be modified as there is an assessment or
                      interview scheduled for this candidate
                    </div>
                  </v-tooltip>
                </div>
                <CustomSelect
                  ref="jobTitle"
                  :items="jobPostList"
                  :isLoading="jobPostListLoading"
                  :label="disableJobPost ? '' : 'Job Title'"
                  :itemSelected="
                    editedDetails.Job_Post_Id
                      ? parseInt(editedDetails.Job_Post_Id)
                      : null
                  "
                  itemValue="Job_Post_Id"
                  itemTitle="Job_Post_Name"
                  :isAutoComplete="true"
                  :noDataText="'No data available'"
                  :disabled="disableJobPost ? true : false"
                  :isRequired="true"
                  :rules="[required('Job Title', editedDetails.Job_Post_Id)]"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Job_Post_Id')
                  "
                ></CustomSelect>
              </v-col>

              <v-col
                v-if="
                  !preferredLocationName &&
                  labelList[290].Field_Visiblity == 'Yes'
                "
                cols="12"
                sm="12"
                lg="4"
                md="4"
                xl="4"
              >
                <CustomSelect
                  :items="jobpostLocations"
                  :label="labelList[290].Field_Alias"
                  ref="preferredLocation"
                  :isAutoComplete="true"
                  :itemSelected="editedDetails.Preferred_Location"
                  :isLoading="jobPostLocationLoading"
                  :select-properties="{
                    multiple: true,
                    chips: true,
                    clearable: true,
                    closableChips: true,
                  }"
                  :noDataText="
                    jobPostLocationLoading ? 'Loading...' : 'No data available'
                  "
                  itemValue="Location_Id"
                  itemTitle="Location_Name"
                  :isRequired="labelList[290].Mandatory_Field == 'Yes'"
                  :rules="[
                    labelList[290].Mandatory_Field == 'Yes'
                      ? required(
                          `${labelList[290].Field_Alias}`,
                          editedDetails.Preferred_Location &&
                            editedDetails.Preferred_Location.length > 0
                            ? editedDetails.Preferred_Location
                            : null
                        )
                      : true,
                  ]"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Preferred_Location')
                  "
                ></CustomSelect>
              </v-col>

              <v-col cols="12" sm="12" lg="4" md="4" xl="4">
                <div class="d-flex mt-n4">
                  <p class="custom-label">
                    Skill Set
                    <span style="color: red">*</span>
                    <v-tooltip
                      text="Eg.. Communication, Interpersonal etc..."
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          class="ml-1"
                          v-bind="props"
                          size="small"
                          color="blue"
                        >
                          fas fa-info-circle
                        </v-icon>
                      </template>
                    </v-tooltip>
                  </p>
                </div>
                <v-text-field
                  ref="skillSet"
                  v-model="input"
                  @update:modelValue="showAddIcon"
                  variant="solo"
                  :rules="[
                    alphaNumSpaceNewLineWithElevenSymbolValidation(input),
                    required('Key Skills', skillSet[0]),
                  ]"
                  @keydown.enter.prevent="addChip"
                >
                  <template v-slot:default>
                    <v-icon v-if="showIcon" @click="addChip" size="x-small"
                      >fas fa-plus</v-icon
                    >
                    <v-chip
                      v-for="(chip, index) in skillSet"
                      append-icon="fas fa-times-circle"
                      :key="index"
                      class="ma-1"
                      @click="removeChip(index)"
                      >{{ chip }}</v-chip
                    >
                  </template>
                </v-text-field>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <formTextFeild
                  ref="currentEmployer"
                  v-model="editedDetails.Current_Employer"
                  :rules="
                    checkFieldAvailability(editedDetails.Current_Employer)
                      ? [
                          validateWithRulesAndReturnMessages(
                            editedDetails.Current_Employer,
                            'currentEmployer',
                            'Current Employer'
                          ),
                        ]
                      : [true]
                  "
                  label="Current Employer"
                  @update:model-value="onChangeFields"
                ></formTextFeild>
              </v-col>

              <v-col
                v-if="labelList[227].Field_Visiblity == 'Yes'"
                cols="12"
                sm="12"
                lg="4"
                md="4"
                xl="4"
              >
                <formTextFeild
                  ref="noticePeriod"
                  v-model="editedDetails.Notice_Period"
                  type="number"
                  :rules="[
                    labelList[227].Mandatory_Field == 'Yes'
                      ? required(
                          `${labelList[227].Field_Alias}`,
                          editedDetails.Notice_Period
                        )
                      : true,
                    editedDetails.Notice_Period
                      ? numericRequiredValidation(
                          `${labelList[227].Field_Alias}`,
                          editedDetails.Notice_Period
                        )
                      : true,
                  ]"
                  @update:model-value="onChangeFields"
                >
                  <template v-slot:label>
                    <span>{{ labelList[227].Field_Alias }} (In days)</span>
                    <span
                      v-if="labelList[227].Mandatory_Field == 'Yes'"
                      class="ml-1"
                      style="color: red"
                      >*</span
                    >
                  </template>
                </formTextFeild>
              </v-col>
              <v-col cols="12" md="4" sm="6" class="mt-n4">
                <p class="custom-label">
                  Total Experience
                  <span style="color: red">*</span>
                </p>
                <div class="d-flex">
                  <formTextFeild
                    ref="totalExperienceInYears"
                    v-model="editedDetails.Total_Experience_In_Years"
                    type="number"
                    :rules="[
                      numericRequiredValidation(
                        'Total Experience (In years)',
                        editedDetails.Total_Experience_In_Years
                      ),
                    ]"
                    @update:model-value="onChangeFields"
                    ><template v-slot:append-inner>
                      Years
                    </template></formTextFeild
                  >
                  <formTextFeild
                    ref="totalExperienceInMonths"
                    v-model="editedDetails.Total_Experience_In_Months"
                    type="number"
                    :rules="[
                      numericRequiredValidation(
                        'Total Experience (In months)',
                        editedDetails.Total_Experience_In_Months
                      ),
                    ]"
                    class="ml-3"
                    @update:model-value="onChangeFields"
                    ><template v-slot:append-inner>
                      Months
                    </template></formTextFeild
                  >
                </div>
              </v-col>
              <v-col cols="12" sm="12" lg="4" md="4" xl="4">
                <formTextFeild
                  ref="currentCTC"
                  v-model="editedDetails.Current_CTC"
                  type="number"
                  :rules="[
                    numericRequiredValidation(
                      'Current Basic Salary (monthly)',
                      editedDetails.Current_CTC
                    ),
                  ]"
                  @update:model-value="onChangeFields"
                >
                  <template v-slot:label>
                    Current Basic Salary (monthly)
                    <span style="color: red">*</span>
                  </template>
                </formTextFeild>
              </v-col>
              <v-col cols="12" sm="12" lg="4" md="4" xl="4">
                <formTextFeild
                  ref="expectedCTC"
                  v-model="editedDetails.Expected_CTC"
                  type="number"
                  :rules="[
                    numericRequiredValidation(
                      'Expected Basic Salary (monthly)',
                      editedDetails.Expected_CTC
                    ),
                  ]"
                  @update:model-value="onChangeFields"
                >
                  <template v-slot:label>
                    Expected Basic Salary (monthly)
                    <span style="color: red">*</span>
                  </template>
                </formTextFeild>
              </v-col>
              <v-col
                cols="12"
                sm="12"
                lg="4"
                md="4"
                xl="4"
                v-if="
                  editedDetails?.Source?.toLowerCase() === 'seek' &&
                  candidateIdSelected &&
                  !isUserLogedIn
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Expected Salary (Captured from Seek)
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    candidateDetails?.Expected_CTC_Sourced_From_Seek
                      ? candidateDetails?.Expected_CTC_Sourced_From_Seek
                      : "-"
                  }}
                </p>
              </v-col>
              <v-col
                v-if="labelList[289].Field_Visiblity == 'Yes'"
                cols="12"
                sm="12"
                lg="4"
                md="4"
                xl="4"
              >
                <CustomSelect
                  :items="currencyList"
                  :isLoading="dropdownListLoading"
                  :label="labelList[289].Field_Alias"
                  ref="currency"
                  :isRequired="labelList[289].Mandatory_Field == 'Yes'"
                  :itemSelected="
                    editedDetails.Currency
                      ? parseInt(editedDetails.Currency)
                      : null
                  "
                  itemValue="Currency_Id"
                  :rules="[
                    labelList[289].Mandatory_Field == 'Yes'
                      ? required(
                          `${labelList[289].Field_Alias}`,
                          editedDetails.Currency
                        )
                      : true,
                  ]"
                  itemTitle="Currency_Name"
                  :isAutoComplete="true"
                  :noDataText="'No data available'"
                  @selected-item="onChangeCustomSelectField($event, 'Currency')"
                >
                </CustomSelect>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[269].Field_Visiblity == 'Yes'"
              >
                <v-text-field
                  v-model="editedDetails.Passport_No"
                  :rules="
                    checkFieldAvailability(editedDetails.Passport_No)
                      ? labelList[269].Mandatory_Field == 'Yes'
                        ? [
                            validateWithRulesAndReturnMessages(
                              editedDetails.Passport_No,
                              'passportNo',
                              'Passport Number'
                            ),
                            required(
                              labelList[269].Field_Alias,
                              editedDetails.Passport_No
                            ),
                          ]
                        : [
                            validateWithRulesAndReturnMessages(
                              editedDetails.Passport_No,
                              'passportNo',
                              'Passport Number'
                            ),
                          ]
                      : labelList[269].Mandatory_Field == 'Yes'
                      ? [
                          required(
                            labelList[269].Field_Alias,
                            editedDetails.Passport_No
                          ),
                        ]
                      : [true]
                  "
                  variant="solo"
                  ref="passportNo"
                  @update:model-value="onChangeFields"
                >
                  <template v-slot:label>
                    {{ labelList[269].Field_Alias }}
                    <span
                      v-if="labelList[269].Mandatory_Field == 'Yes'"
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[322].Field_Visiblity == 'Yes'"
              >
                <formTextFeild
                  :ref="'Reference_Name'"
                  v-model="editedDetails.Verifier_Name"
                  :rules="[
                    checkFieldAvailability(editedDetails.Verifier_Name)
                      ? validateWithRulesAndReturnMessages(
                          editedDetails.Verifier_Name,
                          'empLastName',
                          'Name of Character Reference'
                        )
                      : true,
                    labelList[322].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[322].Field_Alias,
                          editedDetails.Verifier_Name
                        )
                      : true,
                  ]"
                  label="Name of Character Reference"
                  @update:model-value="onChangeFields"
                >
                  <template v-slot:label>
                    {{ labelList[322].Field_Alias }}
                    <span
                      v-if="labelList[322].Mandatory_Field == 'Yes'"
                      class="text-red"
                      >*</span
                    >
                  </template>
                </formTextFeild>
              </v-col>
              <v-col
                cols="12"
                sm="12"
                lg="4"
                md="4"
                xl="4"
                v-if="labelList[323].Field_Visiblity == 'Yes'"
              >
                <formTextFeild
                  :ref="'verifierPhoneno'"
                  v-model="editedDetails.Verifier_Phone_Number"
                  :rules="[
                    labelList[323].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[323].Field_Alias,
                          editedDetails.Verifier_Phone_Number
                        )
                      : true,
                    editedDetails.Verifier_Phone_Number
                      ? minLengthValidation(
                          labelList[323]?.Field_Alias,
                          editedDetails.Verifier_Phone_Number,
                          6
                        )
                      : true,
                  ]"
                  :counter="15"
                  :maxlength="15"
                  label="Character Reference Mobile Number"
                  @update:model-value="onChangeFields"
                >
                  <template v-slot:label>
                    {{ labelList[323].Field_Alias }}
                    <span
                      v-if="labelList[323].Mandatory_Field == 'Yes'"
                      class="text-red"
                      >*</span
                    >
                  </template>
                </formTextFeild>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[324].Field_Visiblity == 'Yes'"
              >
                <v-text-field
                  v-model.trim="editedDetails.Verifier_Email_Id"
                  variant="solo"
                  :rules="[
                    checkFieldAvailability(editedDetails.Verifier_Email_Id)
                      ? validateWithRulesAndReturnMessages(
                          editedDetails.Verifier_Email_Id,
                          'empEmail',
                          'Character Reference Email'
                        )
                      : true,
                    labelList[324].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[324].Field_Alias,
                          editedDetails.Verifier_Name
                        )
                      : true,
                  ]"
                  ref="referenceEmail"
                  label="Character Reference Email"
                  @update:model-value="onChangeFields"
                >
                  <template v-slot:label>
                    {{ labelList[324].Field_Alias }}
                    <span
                      v-if="labelList[324].Mandatory_Field == 'Yes'"
                      class="text-red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
            </v-row>
          </div>

          <div class="mt-6">
            <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold"
              >Contact Details</span
            >
            <v-row class="mt-2">
              <v-col v-if="!isManualEnterForPermanentAddress" cols="12">
                <VCard>
                  <vue-google-autocomplete
                    id="pMap"
                    ref="pGoogleVal"
                    class="form-control pa-5 google-auto-complete-address-field"
                    placeholder="Please type your address"
                    v-on:placechanged="setPermanentAddress"
                    :enable-geolocation="true"
                    :fields="['address_components', 'geometry']"
                  ></vue-google-autocomplete>
                </VCard>
              </v-col>
              <v-col cols="12">
                <div>
                  <v-checkbox-btn
                    v-model="isManualEnterForPermanentAddress"
                    color="primary"
                    false-icon="far fa-square"
                    true-icon="fas fa-check-square"
                    indeterminate-icon="fas fa-minus-circle"
                    class="ml-n2"
                    @change="onChangeFields"
                  >
                    <template v-slot:label>
                      <div class="text-grey-darken-1" style="font-size: 16px">
                        No autocomplete address found
                      </div>
                    </template>
                  </v-checkbox-btn>
                </div>
              </v-col>
              <v-col cols="12" sm="12" lg="4" md="4" xl="4">
                <formTextFeild
                  :ref="'pApartment_Name'"
                  v-model="editedDetails.pApartment_Name"
                  :rules="[
                    required('Street 1', editedDetails.pApartment_Name),
                    validateWithRulesAndReturnMessages(
                      editedDetails.pApartment_Name,
                      'street1',
                      'Street 1'
                    ),
                  ]"
                  :disabled="
                    !isManualEnterForPermanentAddress &&
                    !enableEditAutoPermanentAddress
                  "
                  @update:model-value="
                    fillCurrentAddressBasedOnPermanentAddress
                  "
                >
                  <template v-slot:label>
                    Street 1
                    <span style="color: red">*</span>
                  </template>
                </formTextFeild>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <formTextFeild
                  :ref="'pStreet_Name'"
                  v-model="editedDetails.pStreet_Name"
                  :rules="
                    checkFieldAvailability(editedDetails.pStreet_Name)
                      ? [
                          validateWithRulesAndReturnMessages(
                            editedDetails.pStreet_Name,
                            'street',
                            'Street 2'
                          ),
                        ]
                      : [true]
                  "
                  :disabled="
                    !isManualEnterForPermanentAddress &&
                    !enableEditAutoPermanentAddress
                  "
                  label="Street 2"
                  @update:model-value="
                    fillCurrentAddressBasedOnPermanentAddress
                  "
                ></formTextFeild>
              </v-col>
              <!-- pBarangay -->
              <v-col
                v-if="labelList[332]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                sm="12"
                lg="4"
                md="4"
                xl="4"
              >
                <CustomSelect
                  v-if="labelList[332].Predefined.toLowerCase() === 'yes'"
                  :items="barangayList"
                  v-model="editedDetails.pBarangay_Id"
                  :label="labelList[332].Field_Alias"
                  item-title="barangayDetails"
                  item-value="Barangay_Id"
                  :isLoading="isBarangayLoading"
                  :isAutoComplete="true"
                  :isRequired="labelList[332].Mandatory_Field == 'Yes'"
                  :rules="[
                    labelList[332].Mandatory_Field == 'Yes'
                      ? required(
                          `${labelList[332].Field_Alias}`,
                          editedDetails.pBarangay_Id
                        )
                      : true,
                  ]"
                  :disabled="
                    !isManualEnterForPermanentAddress &&
                    !enableEditAutoPermanentAddress
                  "
                  placeholder="Type minimum 3 characters to list"
                  @input="onChangeFields"
                  :noDataText="noDataText"
                  :itemSelected="
                    editedDetails.pBarangay_Id ? editedDetails.pBarangay_Id : ''
                  "
                  @selected-item="
                    onChangeCustomSelectField($event, 'pBarangay_Id')
                  "
                  @update-search-value="callBarangayList($event)"
                  @update:model-value="onUpdatePBarangay($event)"
                ></CustomSelect>
                <v-text-field
                  v-else
                  v-model="editedDetails.pBarangay"
                  :rules="[
                    labelList[332]?.Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          labelList[332]?.Field_Alias,
                          editedDetails.pBarangay
                        )
                      : true,

                    validateWithRulesAndReturnMessages(
                      editedDetails.pBarangay,
                      'barangay',
                      labelList[332]?.Field_Alias
                    ),
                  ]"
                  clearable
                  variant="solo"
                  ref="pBarangay"
                  :disabled="
                    !isManualEnterForPermanentAddress &&
                    !enableEditAutoPermanentAddress
                  "
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[332]?.Field_Alias }}
                    <span
                      v-if="
                        labelList[332]?.Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" sm="12" lg="4" md="4" xl="4">
                <CustomSelect
                  v-if="
                    labelList[143] &&
                    labelList[143].Predefined.toLowerCase() === 'yes'
                  "
                  :items="cityList"
                  label="City"
                  :isAutoComplete="true"
                  :isLoading="cityListLoading"
                  :noDataText="
                    cityListLoading ? 'Loading...' : 'No data available'
                  "
                  :itemSelected="
                    editedDetails.pCity_Id ? editedDetails.pCity_Id : ''
                  "
                  itemValue="City_Id"
                  itemTitle="cityStateDetails"
                  :isRequired="true"
                  :disabled="
                    !isManualEnterForPermanentAddress &&
                    !enableEditAutoPermanentAddress
                  "
                  :rules="[required('City', editedDetails.pCity_Id)]"
                  ref="pCity"
                  @selected-item="onChangeCustomSelectField($event, 'pCity_Id')"
                ></CustomSelect>
                <formTextFeild
                  v-else
                  :ref="'pCityTxt'"
                  v-model="editedDetails.pCity"
                  :rules="[
                    required('City', editedDetails.pCity),
                    validateWithRulesAndReturnMessages(
                      editedDetails.pCity,
                      'city',
                      'City'
                    ),
                  ]"
                  :disabled="
                    !isManualEnterForPermanentAddress &&
                    !enableEditAutoPermanentAddress
                  "
                  @update:model-value="
                    fillCurrentAddressBasedOnPermanentAddress
                  "
                >
                  <template v-slot:label>
                    City
                    <span style="color: red">*</span>
                  </template>
                </formTextFeild>
              </v-col>
              <v-col cols="12" sm="12" lg="4" md="4" xl="4">
                <formTextFeild
                  :ref="'pState'"
                  v-model="editedDetails.pState"
                  :rules="[
                    required('State/Province', editedDetails.pState),
                    validateWithRulesAndReturnMessages(
                      editedDetails.pState,
                      'state',
                      'State/Province'
                    ),
                  ]"
                  :disabled="
                    !isManualEnterForPermanentAddress &&
                    !enableEditAutoPermanentAddress
                  "
                  @update:model-value="
                    fillCurrentAddressBasedOnPermanentAddress
                  "
                >
                  <template v-slot:label>
                    State/Province
                    <span style="color: red">*</span>
                  </template>
                </formTextFeild>
              </v-col>
              <!-- region -->
              <v-col
                v-if="labelList[333]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                sm="12"
                lg="4"
                md="4"
                xl="4"
              >
                <v-text-field
                  v-model="editedDetails.pRegion"
                  :rules="[
                    labelList[333]?.Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          labelList[333]?.Field_Alias,
                          editedDetails.pRegion
                        )
                      : true,
                    validateWithRulesAndReturnMessages(
                      editedDetails.pRegion,
                      'region',
                      labelList[333]?.Field_Alias
                    ),
                  ]"
                  clearable
                  variant="solo"
                  ref="region"
                  :disabled="
                    !isManualEnterForPermanentAddress &&
                    !enableEditAutoPermanentAddress
                  "
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[333]?.Field_Alias }}
                    <span
                      v-if="
                        labelList[333]?.Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" sm="12" lg="4" md="4" xl="4">
                <CustomSelect
                  :items="countryList"
                  label="Country"
                  :isAutoComplete="true"
                  :isLoading="countryListLoading"
                  :noDataText="
                    countryListLoading ? 'Loading...' : 'No data available'
                  "
                  :itemSelected="
                    editedDetails.pCountry ? editedDetails.pCountry : ''
                  "
                  itemValue="Country_Code"
                  itemTitle="Country_Name"
                  :isRequired="true"
                  :rules="[required('Country', editedDetails.pCountry)]"
                  :disabled="
                    !isManualEnterForPermanentAddress &&
                    !enableEditAutoPermanentAddress
                  "
                  ref="pCountry"
                  @selected-item="onChangeCustomSelectField($event, 'pCountry')"
                ></CustomSelect>
              </v-col>
              <v-col
                v-if="labelList[150]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                sm="12"
                lg="4"
                md="4"
                xl="4"
              >
                <formTextFeild
                  :ref="'pZipCode'"
                  v-model="editedDetails.pPincode"
                  :rules="[
                    labelList[150].Mandatory_Field === 'Yes'
                      ? required(
                          labelList[150].Field_Alias,
                          editedDetails.pPincode
                        )
                      : true,
                    editedDetails.pPincode
                      ? validateWithRulesAndReturnMessages(
                          editedDetails.pPincode,
                          'pinCode',
                          labelList[150].Field_Alias
                        )
                      : true,
                  ]"
                  :disabled="
                    !isManualEnterForPermanentAddress &&
                    !enableEditAutoPermanentAddress
                  "
                  @update:model-value="
                    fillCurrentAddressBasedOnPermanentAddress
                  "
                >
                  <template v-slot:label>
                    {{ labelList[150].Field_Alias }}
                    <span
                      style="color: red"
                      v-if="labelList[150].Mandatory_Field === 'Yes'"
                      >*</span
                    >
                  </template>
                </formTextFeild>
              </v-col>
              <v-col cols="12" sm="12" lg="4" md="4" xl="4">
                <div
                  class="mt-n4"
                  :class="mobileNumberValidation ? '' : 'custom-label'"
                  :style="
                    mobileNumberValidation
                      ? 'color: rgb(var(--v-theme-error)); font-size: 12px'
                      : ''
                  "
                >
                  Mobile Number
                  <span style="color: red">*</span>
                </div>
                <VueTelInput
                  class="pa-2"
                  v-model="editedDetails.Mobile_No"
                  :preferred-countries="['IN', 'PH', 'US', 'AU']"
                  :autoDefaultCountry="true"
                  :error="!mobileNumberValidation"
                  error-color="#E53935"
                  valid-color="#9E9E9E"
                  mode="national"
                  @country-changed="getCountryCode($event)"
                  @validate="validateMobileNumber"
                  :valid-characters-only="true"
                  ref="mobileNo"
                ></VueTelInput>
                <span
                  v-if="mobileNumberValidation"
                  class="ma-2"
                  style="color: rgb(var(--v-theme-error)); font-size: 12px"
                  >{{ mobileNumberValidation }}</span
                >
              </v-col>

              <v-col cols="12" sm="12" lg="4" md="4" xl="4">
                <v-text-field
                  v-model="editedDetails.Personal_Email"
                  variant="solo"
                  :rules="[
                    validateWithRulesAndReturnMessages(
                      editedDetails.Personal_Email,
                      'empEmail',
                      'Personal Email'
                    ),
                  ]"
                  :disabled="isUserLogedIn"
                  ref="personalEmail"
                  @update:model-value="onChangeFields"
                >
                  <template v-slot:label>
                    Personal Email
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <!-- emergencyContactName -->
              <v-col
                v-if="labelList[334]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                sm="12"
                lg="4"
                md="4"
                xl="4"
              >
                <v-text-field
                  v-model="editedDetails.Emergency_Contact_Name"
                  :rules="[
                    labelList[334]?.Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          labelList[334]?.Field_Alias,
                          editedDetails.Emergency_Contact_Name
                        )
                      : true,
                    alphaWithSpaceAndDot(
                      labelList[334]?.Field_Alias,
                      editedDetails.Emergency_Contact_Name
                    ),
                    minLengthValidation(
                      labelList[334]?.Field_Alias,
                      editedDetails.Emergency_Contact_Name,
                      3
                    ),
                    maxLengthValidation(
                      labelList[334]?.Field_Alias,
                      editedDetails.Emergency_Contact_Name,
                      100
                    ),
                  ]"
                  clearable
                  variant="solo"
                  ref="emergencyContactName"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[334]?.Field_Alias }}
                    <span
                      v-if="
                        labelList[334]?.Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <!-- emergencyContactNumber -->
              <v-col
                v-if="labelList[335]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                sm="12"
                lg="4"
                md="4"
                xl="4"
              >
                <v-text-field
                  v-model="editedDetails.Fax_No"
                  :rules="[
                    labelList[335]?.Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          labelList[335]?.Field_Alias,
                          editedDetails.Fax_No
                        )
                      : true,
                    editedDetails.Fax_No
                      ? minLengthValidation(
                          labelList[335]?.Field_Alias,
                          editedDetails.Fax_No,
                          6
                        )
                      : true,
                  ]"
                  :counter="15"
                  :maxlength="15"
                  variant="solo"
                  clearable
                  ref="emergencyContactNumber"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[335]?.Field_Alias }}
                    <span
                      v-if="
                        labelList[335]?.Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <!-- emergencyContactRelation -->
              <v-col
                v-if="labelList[359]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                sm="12"
                lg="4"
                md="4"
                xl="4"
              >
                <v-text-field
                  v-model="editedDetails.Emergency_Contact_Relation"
                  :rules="[
                    labelList[359]?.Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          labelList[359]?.Field_Alias,
                          editedDetails.Emergency_Contact_Relation
                        )
                      : true,
                    alphaWithSpaceAndDot(
                      labelList[359]?.Field_Alias,
                      editedDetails.Emergency_Contact_Relation
                    ),
                    minLengthValidation(
                      labelList[359]?.Field_Alias,
                      editedDetails.Emergency_Contact_Relation,
                      3
                    ),
                    maxLengthValidation(
                      labelList[359]?.Field_Alias,
                      editedDetails.Emergency_Contact_Relation,
                      30
                    ),
                  ]"
                  variant="solo"
                  clearable
                  ref="emergencyContactRelation"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[359]?.Field_Alias }}
                    <span
                      v-if="
                        labelList[359]?.Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
            </v-row>
          </div>

          <div class="mt-6">
            <ExperienceDetails
              v-if="!isParsingResumeData"
              ref="experienceMain"
              :candidateDetails="editedDetails"
              :isEdit="true"
              :dateFormat="orgDateFormat"
              @update-experience-details="fillDataForPreviewScreen"
            >
            </ExperienceDetails>
          </div>

          <div class="mt-6">
            <CareerInfo
              v-if="!isParsingResumeData"
              ref="careerMain"
              :isEdit="true"
              :isUserLogedIn="isUserLogedIn"
              :candidateDetails="editedDetails"
              :jobPostId="jobPostId"
              :dateFormat="orgDateFormat"
              @update-career-info-details="fillDataForPreviewScreen"
            />
          </div>
          <div class="mt-6">
            <JobCandidatesCustomFields
              :custom-form-name="formName"
              ref="customFieldsMain"
              :isUserLogedIn="isUserLogedIn"
              :form-id="jobPostId || isUserLogedIn ? 311 : 16"
              :primary-id="candidateIdSelected"
              :show-view-form="false"
              :show-edit-form="true"
              @update-additional-details="fillDataForPreviewScreen"
            />
          </div>
        </v-form>
        <v-form v-else ref="simpleForm" :class="jobPostId ? 'pa-12' : 'pa-2'">
          <div class="mt-2">
            <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold"
              >Personal Details</span
            >
            <v-card class="bg-hover">
              <div class="text-subtitle-2 pa-3 text-grey-darken-1">
                Note: The personal data collected will be used for demographic
                representation purposes in the recruitment process. Rest assured
                that all data will be handled with strict confidentiality, in
                compliance with applicable data protection laws.
              </div>
            </v-card>
            <v-row class="mt-4">
              <v-col cols="12" v-if="!candidateIdSelected && !isUserLogedIn">
                <drag-and-drop-files
                  :mandatory="true"
                  :oldFileName="''"
                  :isError="errorMessage"
                  :fileCategory="'document'"
                  @file-event-success="onFileChange"
                ></drag-and-drop-files>
              </v-col>
              <v-col
                v-else-if="(jobPostId && isUserLogedIn) || isPortalLogedIn"
                cols="12"
              >
                <div
                  style="border: 1px solid #000; border-radius: 10px"
                  class="py-10 d-flex align-center justify-center"
                >
                  <span
                    class="text-green cursor-pointer"
                    style="text-decoration: underline"
                    @click="openResumeModel = true"
                    >View Resume & other attachments
                  </span>
                </div></v-col
              >
              <v-col cols="12" md="4" sm="6">
                <v-text-field
                  v-model="editedDetails.Candidate_First_Name"
                  :rules="[
                    required('First Name', editedDetails.Candidate_First_Name),
                    validateWithRulesAndReturnMessages(
                      editedDetails.Candidate_First_Name,
                      'empFirstName',
                      'First Name'
                    ),
                  ]"
                  :disabled="isUserLogedIn"
                  variant="solo"
                  ref="firstName"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    First Name
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <v-text-field
                  v-model="editedDetails.Candidate_Last_Name"
                  :rules="[
                    required('Last Name', editedDetails.Candidate_Last_Name),
                    validateWithRulesAndReturnMessages(
                      editedDetails.Candidate_Last_Name,
                      'empLastName',
                      'Last Name'
                    ),
                  ]"
                  variant="solo"
                  ref="lastName"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    Last Name
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <v-menu
                  v-model="dobMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      v-model="formattedDob"
                      prepend-inner-icon="fas fa-calendar"
                      :rules="[required('Date of Birth', formattedDob)]"
                      readonly
                      :disabled="isUserLogedIn"
                      v-bind="props"
                      variant="solo"
                      ref="candidateDob"
                    >
                      <template v-slot:label>
                        Date of Birth<span style="color: red">*</span>
                      </template></v-text-field
                    >
                  </template>
                  <v-date-picker
                    v-if="editedDetails.DOB"
                    v-model="editedDetails.DOB"
                    :max="maximumBirthDate"
                    :min="minimumBirthDate"
                    @update:modelValue="onChangeFields()"
                  />
                  <v-date-picker
                    v-else
                    v-model="defaultDOB"
                    @update:modelValue="onChangeDefaultDOB(defaultDOB)"
                    :max="maximumBirthDate"
                    :min="minimumBirthDate"
                  />
                </v-menu>
              </v-col>
              <v-col cols="12" sm="12" lg="4" md="4" xl="4">
                <div
                  class="mt-n4"
                  :class="mobileNumberValidation ? '' : 'custom-label'"
                  :style="
                    mobileNumberValidation
                      ? 'color: rgb(var(--v-theme-error)); font-size: 12px'
                      : ''
                  "
                >
                  Mobile Number
                  <span style="color: red">*</span>
                </div>
                <VueTelInput
                  class="pa-2"
                  v-model="editedDetails.Mobile_No"
                  :preferred-countries="['IN', 'PH', 'US', 'AU']"
                  :autoDefaultCountry="true"
                  :error="!mobileNumberValidation"
                  error-color="#E53935"
                  valid-color="#9E9E9E"
                  mode="national"
                  @country-changed="getCountryCode($event)"
                  @validate="validateMobileNumber"
                  :valid-characters-only="true"
                  ref="mobileNo"
                ></VueTelInput>
                <span
                  v-if="mobileNumberValidation"
                  class="ma-2"
                  style="color: rgb(var(--v-theme-error)); font-size: 12px"
                  >{{ mobileNumberValidation }}</span
                >
              </v-col>
              <v-col cols="12" sm="12" lg="4" md="4" xl="4">
                <v-text-field
                  v-model="editedDetails.Personal_Email"
                  variant="solo"
                  :rules="[
                    validateWithRulesAndReturnMessages(
                      editedDetails.Personal_Email,
                      'empEmail',
                      'Personal Email'
                    ),
                  ]"
                  :disabled="isUserLogedIn"
                  ref="personalEmail"
                  @update:model-value="onChangeFields"
                >
                  <template v-slot:label>
                    Personal Email
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
            </v-row>

            <div class="mt-6">
              <JobCandidatesCustomFields
                :custom-form-name="formName"
                ref="customFieldsMain"
                :isUserLogedIn="isUserLogedIn"
                :form-id="jobPostId || isUserLogedIn ? 311 : 16"
                :primary-id="candidateIdSelected"
                :show-view-form="false"
                :show-edit-form="true"
                @update-additional-details="fillDataForPreviewScreen"
              />
            </div>
          </div>
        </v-form>
      </section>
    </div>
  </div>
  <v-bottom-navigation
    v-if="
      openBottomSheet &&
      (isPortalLogedIn
        ? candidateDetails?.Hiring_Stage?.toLowerCase() !== 'preboarding'
        : true)
    "
  >
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            v-if="!jobPostId && !isUserLogedIn"
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
          >
            <span class="primary">Cancel</span>
          </v-btn>
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            v-if="jobPostId"
            rounded="lg"
            variant="outlined"
            size="small"
            color="primary"
            style="height: 40px; margin-top: 10px; margin-right: 10px"
            @click="openPreview()"
          >
            <span class="primary">Preview</span>
          </v-btn>
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 primary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateEditForm()"
          >
            <span>{{
              (isUserLogedIn ? false : candidateIdSelected) || !jobPostId
                ? "Save"
                : "Apply"
            }}</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
          >Close</v-btn
        >
      </div>
    </template>
  </AppSnackBar>
  <v-overlay
    v-if="jobPostId && privacyData?.Enable"
    :model-value="presentPrivacyPopup"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        style="width: 100vw; height: 100vh"
        elevation="0"
      >
        <v-card-title
          class="d-flex pa-4 bg-primary justify-center align-center"
        >
          <div class="text-h6 text-medium">Data Privacy Consent</div>
        </v-card-title>

        <v-card-text class="card px-0" style="overflow-y: auto">
          <div class="px-10 mx-10">
            <div class="border-md pa-4" style="max-width: 100%">
              <div ref="editorView" class="quill-editorView" />
            </div>
            <div class="d-flex flex-row">
              <v-checkbox
                v-model="dataPrivacyConfirmation"
                density="compact"
                :true-value="1"
                :false-value="0"
                color="primary"
              />
              <span class="ml-2 text-subtitle-1 font-weight-bold mt-1"
                >By submitting this data privacy consent, you allow us to use
                your personal data for the above mentioned purposes.</span
              >
            </div>
            <div class="d-flex justify-end align-center">
              <v-btn
                @click="closePrivacyPopup()"
                :disabled="!dataPrivacyConfirmation"
                class="mt-3 primary"
                variant="elevated"
                rounded="lg"
              >
                Next
              </v-btn>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </template>
  </v-overlay>
  <v-overlay
    v-if="jobPostId && showPreviewScreen"
    :model-value="showPreviewScreen"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        style="width: 100vw; height: 100vh"
        elevation="0"
      >
        <v-card-text class="pa-4 ml-4" v-if="!isSimpleForm">
          <div class="d-flex justify-end mt-2 mb-n6">
            <v-btn @click="backToEditScreen()" class="primary" variant="text">
              <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
            </v-btn>
          </div>
          <PersonalInfo :previewScreen="true" :candidateDetails="previewData" />
          <JobInfo :candidateDetails="previewData" />
          <ExperienceDetails
            :candidateDetails="previewData"
            :isEdit="false"
          ></ExperienceDetails>
          <ContactInfo :candidateDetails="previewData" />
          <CareerInfo :isEdit="false" :candidateDetails="previewData" />
          <JobCandidatesCustomFields
            :custom-form-name="formName"
            :form-id="311"
            :primary-id="candidateIdSelected ? candidateIdSelected : 0"
            :show-view-form="true"
            :show-edit-form="false"
            :pass-preview-value="previewData?.customFieldInputs"
          />
        </v-card-text>
        <v-card-text class="pa-4 ml-4" v-else>
          <div class="d-flex justify-end mt-2 mb-n6">
            <v-btn @click="backToEditScreen()" class="primary" variant="text">
              <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
            </v-btn>
          </div>
          <div class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
              <v-progress-circular
                model-value="100"
                color="blue"
                :size="18"
                class="mr-1"
              ></v-progress-circular>
              <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold"
                >Personal Details</span
              >
            </div>
          </div>
          <v-row class="pa-4 ma-2 card-blue-background">
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">First Name</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(previewData.Candidate_First_Name) }}
              </p>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">Last Name</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(previewData.Candidate_Last_Name) }}
              </p>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">Date of Birth</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ formatDate(previewData.DOB) }}
              </p>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">Mobile Number</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{
                  previewData.Mobile_No_Country_Code
                    ? previewData.Mobile_No
                      ? previewData.Mobile_No_Country_Code +
                        " " +
                        previewData.Mobile_No
                      : "-"
                    : checkNullValue(previewData.Mobile_No)
                }}
              </p>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">Email Address</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(previewData.Personal_Email) }}
              </p>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </template>
  </v-overlay>
  <AppLoading
    v-if="isLoading || isParsingResumeData || portalAccessLoading"
    :message="
      isParsingResumeData
        ? 'Parsing the resume... Please wait for the process to finish. Once completed, review the details for accuracy, update any remaining information, and then submit.'
        : ''
    "
  ></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
  <AppWarningModal
    v-if="openAccountConfirmationModel"
    :open-modal="openAccountConfirmationModel"
    confirmation-heading="Do you have an existing account?"
    icon-name=""
    :persistModal="true"
    @close-warning-modal="onCloseAccountConfirmation()"
    @accept-modal="acceptAccountConfirmation()"
  >
  </AppWarningModal>
  <v-dialog v-model="showpopup" width="400" persistent>
    <v-card min-width="400" class="pa-10 rounded-lg">
      <v-card-text class="text-center d-flex flex-column align-center">
        <v-btn
          @click="redirectToCandidatePortal()"
          variant="text"
          class="text-h6 text-green text-decoration-underline"
        >
          View Application Status
        </v-btn>
      </v-card-text>
    </v-card>
  </v-dialog>
  <AppWarningModal
    v-if="jobPostId && openSignInModel"
    :open-modal="openSignInModel"
    confirmation-heading="Sign in"
    :confirmationSubText="
      !presentOTPSetion
        ? 'Access the available job opportunities, track your application status and keep your profile up to date.'
        : ''
    "
    closeButtonText=""
    acceptButtonText=""
    :persistModal="true"
    @close-warning-modal="closeSigninModel()"
    ><template v-slot:warningModalContent>
      <v-row class="mt-2">
        <v-col v-if="!presentOTPSetion" cols="12">
          <v-text-field
            variant="solo"
            ref="signInEmail"
            v-model="signInEmail"
            min-width="350px"
            placeholder="Email Address"
            class="mt-4"
            :rules="[
              validateWithRulesAndReturnMessages(
                signInEmail,
                'empEmail',
                'Email Address'
              ),
            ]"
            label="Email Address"
          />
          <v-row class="d-flex justify-center mt-2">
            <v-btn
              :disabled="disableNextButtton"
              class="primary"
              @click="validateSignEmail()"
              >Next</v-btn
            >
          </v-row>
        </v-col>
        <v-col v-else cols="12">
          <div
            style="padding: 0px"
            class="text-subtitle-1 font-weight-regular text-green text-center"
          >
            We sent OTP to your email address: {{ signInEmail }}
            <p>Please enter the OTP to sign in</p>
            <p class="text-caption text-red">
              The OTP will expire after {{ otpExpireTime }} minutes
            </p>
            <v-text-field
              variant="solo"
              ref="OTP"
              class="mt-4"
              v-model="signInPassword"
              type="number"
              min-width="250px"
              :rules="[required('OTP', signInPassword)]"
              label="OTP"
            />
          </div>
          <v-row class="d-flex justify-center my-2">
            <v-btn
              variant="text"
              class="text-info text-decoration-underline"
              @click="validateSignEmail()"
              >Re-send OTP</v-btn
            >
          </v-row>
          <v-row class="d-flex justify-center mt-2">
            <v-btn class="primary" @click="validatesignOTP()">Sign in</v-btn>
          </v-row>
        </v-col>
      </v-row>
    </template>
  </AppWarningModal>
  <FilePreviewAndEdit
    v-if="openResumeModel"
    :fileName="editedDetails.Resume"
    :otherAttachment="editedDetails.Other_Attachments"
    :heading="editedDetails.Resume ? 'Attachment' : 'Resume Upload'"
    :isUpload="editedDetails.Resume ? false : true"
    folderName="resume"
    :fileSize="editedDetails.Resume_File_Size"
    :is-unauthorized="isUserLogedIn"
    @close-preview-modal="openResumeModel = false"
    @update-resume-details="onChangeFiles"
  />
</template>

<script>
import { defineAsyncComponent } from "vue";
import Cookies from "js-cookie";
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import JobCandidatesCustomFields from "./custom-fields/JobCandidatesCustomFields.vue";
const FilePreviewAndEdit = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewAndEdit.vue")
);
import {
  LIST_MARITAL_STATUS,
  MARITAL_STATUS_LIST,
  LIST_CITIES_NO_AUTH,
  LIST_BARANGAY,
  GENDER_LIST,
} from "@/graphql/dropDownQueries.js";
import {
  UPDATE_JOB_CANDIDATE_DETAILS,
  ADD_JOB_CANDIDATE_DETAILS,
  CANDIDATES_DROP_DOWN,
  LIST_JOB_LOCATIONS,
  LIST_JOB_POSTS,
  RETRIEVE_CANDIDATE_INTERVIEW,
  RESUME_PARSER,
  GET_JOB_HEADER,
  LIST_SOURCE_OF_APPLICATION,
  GET_PARTNER_ID,
  GENERATE_PASSCODE_FOR_PORTAL_ACCESS,
  PASSCODE_VALIDATE_PORTAL_ACCESS,
} from "@/graphql/recruitment/recruitmentQueries.js";
import ExperienceDetails from "./experience-details/ExperienceDetails.vue";
import DragAndDropFiles from "@/components/custom-components/DragAndDrop.vue";
import CareerInfo from "./career/CareerInfo.vue";
import VueGoogleAutocomplete from "vue-google-autocomplete";
import { VueTelInput } from "vue-tel-input";
import { convertToBytes, checkNullValue } from "@/helper.js";
import { LIST_NATIONALITIES } from "@/graphql/dropDownQueries.js";
import { CUSTOM_COLOR_PICKER } from "@/graphql/commonQueries.js";
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";
import PersonalInfo from "./PersonalInfo.vue";
import JobInfo from "./JobInfo.vue";
import ContactInfo from "./ContactInfo.vue";
export default {
  name: "EditJobCandidateDetails",

  mixins: [validationRules],
  emits: [
    "edit-updated",
    "close-edit-form",
    "signin-candidate",
    "candidate-portal-access-enabled",
  ],

  props: {
    candidateDetails: {
      type: Object,
      required: true,
    },
    candidateIdSelected: {
      type: Number,
      default: 0,
    },
    jobPostId: {
      type: [Number, String],
      default: 0,
    },
    showJobHeader: {
      type: Boolean,
      required: true,
    },
    isPortalLogedIn: {
      type: Boolean,
      required: false,
    },
    presentSimpleForm: {
      type: Boolean,
      required: false,
    },
  },

  components: {
    CustomSelect,
    PersonalInfo,
    JobInfo,
    ContactInfo,
    ExperienceDetails,
    VueGoogleAutocomplete,
    VueTelInput,
    JobCandidatesCustomFields,
    CareerInfo,
    DragAndDropFiles,
    FilePreviewAndEdit,
  },

  data() {
    return {
      isMounted: false,
      editedDetails: {},
      isFormDirty: false,
      openWarningModal: false,
      alreadyExistErrMsg: {
        National_Identification_Number: true,
      },
      errorMessage: "",
      // contact
      isManualEnterForPermanentAddress: true,
      enableEditAutoPermanentAddress: false,
      // mobile
      isValidMobileNumber: true,
      mobileNoCountryCode: 0,
      mobileNoAlreadyExist: false,
      // edit
      openBottomSheet: true,
      isLoading: false,
      isParsingResumeData: false,
      validationMessages: [],
      showValidationAlert: false,
      DOBErrorMsg: "",
      // dropdown list
      languageList: [],
      languageListLoading: false,
      workPermitList: [],
      dropdownListLoading: false,
      jobpostLocations: [],
      jobPostLocationLoading: false,
      countryList: [],
      countryListLoading: false,
      jobPostList: [],
      jobPostListLoading: false,
      jobPostName: "",
      preferredLocationName: "",
      currencyList: [],
      disableJobPost: true,
      maritalStatusList: [],
      maritalStatusListLoading: false,
      input: "",
      cityList: [],
      barangayList: [],
      cityListLoading: false,
      isBarangayLoading: false,
      genderList: [],
      genderListLoading: false,
      genderOrientationList: [],
      languageProficiencyList: [],
      genderIdentityList: [],
      genderExpressionList: [],
      dropdownLoading: false,
      pronounList: [],
      skillSet: [],
      sourceListLoading: false,
      showpopup: false,
      sourceList: [
        {
          Source_Title: "Branch Posting",
        },
        {
          Source_Title: "Cebuana Lhuillier Foundation",
        },
        {
          Source_Title: "Company Website",
        },
        {
          Source_Title: "Facebook",
        },
        {
          Source_Title: "Indeed Job Posts",
        },
        {
          Source_Title: "Job Fair (Others)",
        },
        {
          Source_Title: "Job Fair (School)",
        },
        {
          Source_Title: "Job Street",
        },
        {
          Source_Title: "Linkedin",
        },
        {
          Source_Title: "Others",
        },
        {
          Source_Title: "Outsourced",
        },
        {
          Source_Title: "PESO",
        },
        {
          Source_Title: "Referral (Employee)",
        },
        {
          Source_Title: "Referral (Others)",
        },
        {
          Source_Title: "Walk-In",
        },
      ],
      // edited data
      editedExpDetails: [],
      editedEducationDetails: [],
      editedCertificationDetails: [],
      editedCustomFieldDetails: {},
      editedSkillsDetails: [],
      isValidExperience: false,
      isValidEducation: false,
      isValidCertification: false,
      isValidCustomFields: false,
      showIcon: false,
      // file
      isFileChanged: false,
      fileContent: null,
      fileInBase64Format: "",
      jobHeader: "",
      nationalityList: [],
      nationalityListLoading: false,
      defaultCountryCode: "",
      dateFormat: "",
      orgName: "",
      dobMenu: false,
      formattedDob: "",
      defaultDOB: "",
      // Data Privacy Concent
      presentPrivacyPopup: false,
      dataPrivacyConfirmation: 0,
      privacyData: {},
      // Candidate Portal
      portalAccessLoading: false,
      isSigninEnabled: false,
      isUserLogedIn: false,
      openResumeModel: false,
      openSignInModel: false,
      presentOTPSetion: false,
      signInEmail: "",
      signInPassword: "",
      // Preview Screen
      showPreviewScreen: false,
      previewData: {},
      openAccountConfirmationModel: false,
      isSimpleForm: true,
      otpExpireTime: 60,
      menuOpen: false,
      searchString: "",
      showForm: false,
    };
  },

  computed: {
    noDataText() {
      if (this.isBarangayLoading) {
        return "Loading...";
      } else if (
        !this.isBarangayLoading &&
        this.barangayList?.length == 0 &&
        this.searchString?.length >= 3
      ) {
        return "no data found";
      } else {
        return "Type minimum 3 characters to list";
      }
    },
    companyName() {
      const { organizationName } = this.$store.state.orgDetails;
      return organizationName;
    },
    menuItems() {
      if (!this.isUserLogedIn) {
        return [
          {
            title: "Sign in",
            icon: "fas fa-sign-in-alt",
            action: "signin",
          },
        ];
      } else {
        return [
          { title: "My Profile", icon: "fas fa-user", action: "profile" },
          {
            title: "Application Status",
            icon: "fas fa-info-circle",
            action: "applicationStatus",
          },
          { title: "View Jobs", icon: "fas fa-list-ul", action: "viewJobs" },
          {
            title: "Sign out",
            icon: "fas fa-sign-out-alt",
            action: "signout",
          },
        ];
      }
    },
    portalCandidateId() {
      return Cookies.get("portalCandidateId");
    },
    disableNextButtton() {
      return (
        !this.signInEmail || this.$refs["signInEmail"]?.errorMessages?.length
      );
    },
    formName() {
      let formName =
        this.jobPostId || this.isUserLogedIn
          ? this.accessIdRights("311")
          : this.accessIdRights("16");
      if (formName?.customFormName && formName.customFormName !== "") {
        return [formName.customFormName];
      } else return ["Job Candidates"];
    },
    accessIdRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    loginEmployeeName() {
      return this.$store.state.userDetails.employeeFirstName;
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    orgDateFormat() {
      let format = this.dateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
    defaultBirthDate() {
      let minBirthDate = new Date();
      minBirthDate.setFullYear(minBirthDate.getFullYear() - 30);
      minBirthDate.setDate(1);
      const minBirthDateISO = minBirthDate;
      return minBirthDateISO;
    },
    minimumBirthDate() {
      let minBirthDate = new Date();
      minBirthDate.setFullYear(minBirthDate.getFullYear() - 80);
      const minBirthDateISO = minBirthDate;
      return moment(minBirthDateISO).format("YYYY-MM-DD");
    },
    maximumBirthDate() {
      let minBirthDate = new Date();
      minBirthDate.setFullYear(minBirthDate.getFullYear() - 15);
      const minBirthDateISO = minBirthDate;
      return moment(minBirthDateISO).format("YYYY-MM-DD");
    },
    mobileNumberValidation() {
      if (!this.editedDetails.Mobile_No) {
        return "Mobile number is required";
      } else if (!this.isValidMobileNumber) {
        return "Please provide a valid mobile number";
      } else {
        return "";
      }
    },
    domainName() {
      return this.$store.getters.domain;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    isInputValid() {
      const rules = [
        this.alphaNumSpaceNewLineWithElevenSymbolValidation(this.input),
        this.required("Key Skills", this.skillSet[0]),
      ];
      return rules[0] === true ? true : false;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
  },

  watch: {
    "editedDetails.Job_Post_Id": function (val) {
      if (val && this.isFormDirty) this.editedDetails.Preferred_Location = null;
      this.retrieveJobPostLocation(val);
    },
    isManualEnterForPermanentAddress() {
      if (this.$refs.pGoogleVal) this.$refs.pGoogleVal.clear();
      this.onChangeFields();
    },
    "editedDetails.pCity_Id": function (val) {
      if (this.labelList[143] && this.labelList[143].Predefined === "Yes") {
        let filterCity = this.cityList.filter((el) => el.City_Id == val);
        if (filterCity && filterCity[0]) {
          let details = filterCity[0].cityStateDetails
            ? filterCity[0].cityStateDetails.split(", ")
            : ["", ""];
          this.editedDetails["pCity"] = filterCity[0].City_Name;
          this.editedDetails["pState"] = details[1];
          this.editedDetails["pRegion"] = null;
          this.editedDetails["pCountry"] = filterCity[0].Country_Code;
        }
      }
    },
    "editedDetails.DOB": function (val) {
      if (val) {
        this.dobMenu = false;
        this.formattedDob = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    isPortalLogedIn: {
      handler(val) {
        if (val) {
          this.isUserLogedIn = val;
        }
      },
      immediate: true,
    },
    portalCandidateId: {
      handler(val) {
        if (!val && this.isUserLogedIn) {
          this.signoutCandidate(true);
        }
      },
      immediate: true,
    },
    candidateDetails: {
      handler(val) {
        if (val) {
          this.editedDetails = JSON.parse(JSON.stringify(val));
          if (!this.isPortalLogedIn)
            this.editedDetails.Job_Post_Id = parseInt(this.jobPostId);
          this.prefillDetails();
        }
      },
      deep: true,
    },
    isUserLogedIn: {
      handler() {
        this.emitPortalAccessStatus();
      },
      immediate: true,
    },
    isSigninEnabled: {
      handler() {
        this.emitPortalAccessStatus();
      },
      immediate: true,
    },
    presentSimpleForm: {
      handler(val) {
        if (val && this.isPortalLogedIn) {
          this.isSimpleForm = true;
        } else this.isSimpleForm = false;
      },
      immediate: true,
    },
  },

  mounted() {
    this.dateFormat = this.$store.state.orgDetails.orgDateFormat;
    this.getOrgDateFormatForNoAuth();
    this.editedDetails = this.candidateDetails
      ? JSON.parse(JSON.stringify(this.candidateDetails))
      : {};
    this.prefillDetails();
    if (!this.candidateIdSelected) {
      this.onChangeFields();
    }
    if (
      this.editedDetails["Resume"] &&
      this.editedDetails["Resume_File_Size"]
    ) {
      this.fileContent = {
        name: this.formattedFileName(this.editedDetails["Resume"]),
        size: convertToBytes(this.editedDetails["Resume_File_Size"]),
      };
    } else {
      this.fileContent = null;
    }
    this.retrieveLanguages();
    this.retrieveDropDownDetails();
    this.retrieveCountries();
    this.retrieveJobPosts();
    if (!this.jobPostId) {
      this.retrieveCandidateInterviews();
    } else {
      this.editedDetails.Job_Post_Id = parseInt(this.jobPostId);
    }
    this.retrieveJobPostLocation();
    this.retrieveMaritalStatusList();
    this.retrieveCities();
    this.retrieveGender();
    this.retrieveSource();
    this.getDropdownDetails();
    this.retrieveNationalityList();
    if (this.jobPostId) {
      this.retrievePartnerId();
    }
    this.isMounted = true;
    this.checkSessionValidity();
  },
  methods: {
    emitPortalAccessStatus() {
      this.$emit(
        "candidate-portal-access-enabled",
        !this.isUserLogedIn && this.isSigninEnabled
      );
    },
    checkNullValue,
    prefillDetails() {
      let langKnown = [];
      this.callBarangayList(this.editedDetails?.pBarangay);
      this.editedDetails.Marital_Status = this.editedDetails.Marital_Status_Id;
      if (this.editedDetails["DOB"]) {
        this.editedDetails["DOB"] = moment(
          this.editedDetails["DOB"],
          "YYYY/MM/DD"
        )._d;
      }
      if (this.editedDetails.DOB) {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        this.editedDetails.DOB = new Date(this.editedDetails.DOB);
        this.formattedDob = moment(this.editedDetails.DOB).format(
          orgDateFormat
        );
      }
      this.defaultDOB = new Date(this.defaultBirthDate);
      if (
        this.editedDetails.Lang_Known &&
        this.editedDetails.Lang_Known.length > 0
      ) {
        langKnown = this.editedDetails.Lang_Known.map((item) => ({
          Lang_Id: item.Lang_Id,
          Language_Name: item.Language_Name || null,
          Lang_Spoken: item.Lang_Spoken || null,
          Lang_Read_Write: item.Lang_Read_Write || null,
          Lang_Proficiency: item.Lang_Proficiency || null,
        }));
      }
      if (this.labelList[325]?.Field_Visiblity?.toLowerCase() === "yes") {
        this.editedDetails["Lang_Known"] = langKnown.map(
          (lang) => lang.Lang_Id
        );
      } else if (
        this.labelList[383]?.Field_Visiblity?.toLowerCase() === "yes"
      ) {
        if (!langKnown.length) {
          // Add default entry if no languages are present
          this.editedDetails["Lang_Known"] = [
            {
              Lang_Id: null,
              langSpoken: 0,
              langReadWrite: 0,
              langProficiency: null,
            },
          ];
        } else {
          this.editedDetails["Lang_Known"] = langKnown.map((lang) => ({
            Lang_Id: lang.Lang_Id,
            langSpoken: lang.Lang_Spoken ? 1 : 0,
            langReadWrite: lang.Lang_Read_Write ? 1 : 0,
            langProficiency: lang.Lang_Proficiency,
          }));
        }
      } else {
        this.editedDetails["Lang_Known"] = [];
      }
      let locationNames = [];
      if (
        this.editedDetails.Preferred_Location &&
        this.editedDetails.Preferred_Location.length > 0
      ) {
        for (
          var p = 0;
          p < this.editedDetails["Preferred_Location"].length;
          p++
        ) {
          locationNames.push(
            this.editedDetails["Preferred_Location"][p].Location_Id
          );
        }
      }
      this.editedDetails["Preferred_Location"] = locationNames;
      this.editedDetails["Father_Name"] = "";
      this.editedDetails["Mother_Name"] = "";
      this.editedDetails["Nationality"] = this.candidateDetails?.Nationality;
      this.editedDetails["Nationality_Id"] =
        this.candidateDetails.Nationality_Id;
      if (
        this.editedDetails.Candidate_Dependent &&
        this.editedDetails.Candidate_Dependent.length > 0
      ) {
        this.editedDetails["Father_Name"] = this.formFatherMotherName(
          this.editedDetails.Candidate_Dependent,
          "Father"
        );
        this.editedDetails["Mother_Name"] = this.formFatherMotherName(
          this.editedDetails.Candidate_Dependent,
          "Mother"
        );
      }
      let wPermitIds = [];
      if (
        this.editedDetails.Work_Permit &&
        this.editedDetails.Work_Permit.length > 0
      ) {
        for (var k = 0; k < this.editedDetails.Work_Permit.length; k++) {
          wPermitIds.push(this.editedDetails.Work_Permit[k].Work_Permit_Id);
        }
      }
      this.editedDetails["Work_Permit_Ids"] = wPermitIds;
      if (this.editedDetails.Mobile_No_Country_Code) {
        let cCode = this.editedDetails.Mobile_No_Country_Code.split("+");
        this.mobileNoCountryCode = cCode && cCode.length > 0 ? cCode[1] : "";
        this.editedDetails["Mobile_No"] =
          this.editedDetails.Mobile_No_Country_Code +
          this.editedDetails.Mobile_No;
      }
      this.skillSet = this.editedDetails.Skill_Set?.length
        ? this.editedDetails.Skill_Set
        : [];
      // Prefill source of application based on source in public form & Candidate portal
      const source = this.$route.query.source || null;
      if (source) {
        this.editedDetails["Source"] =
          source === "Linkedin" ? "LinkedIn" : source;
      } else if (this.jobPostId && !this.isUserLogedIn) {
        this.editedDetails["Source"] = "Career Portal";
      } else if (this.isUserLogedIn) {
        this.editedDetails["Source"] = "Candidate Experience Portal";
      }
    },
    callBarangayList(searchString) {
      this.searchString = searchString;
      if (searchString?.length >= 3) {
        this.retrieveBarangay(searchString);
      }
    },
    closePrivacyPopup() {
      this.presentPrivacyPopup = false;
      // Save to local storage that privacy popup has been shown for this job post
      if (this.jobPostId) {
        this.savePrivacyPopupStatus();
      }
      if (this.isSigninEnabled && this.jobPostId) {
        let candidateId = Cookies.get("portalCandidateId");
        if (candidateId) {
          this.showForm = true;
          this.$emit("signin-candidate", candidateId);
          this.isUserLogedIn = true;
          this.menuOpen = true;
        } else {
          this.openAccountConfirmationModel = true;
        }
      }
    },
    // Method to save privacy popup status to local storage
    savePrivacyPopupStatus() {
      // Get existing accepted job posts from local storage
      const acceptedPrivacyKey = "acceptedPrivacyJobPosts";
      let acceptedJobPosts = localStorage.getItem(acceptedPrivacyKey);
      let jobPostsArray = acceptedJobPosts ? JSON.parse(acceptedJobPosts) : [];

      // Add current job post if not already in the array
      if (!jobPostsArray.includes(this.jobPostId)) {
        jobPostsArray.push(this.jobPostId);
        localStorage.setItem(acceptedPrivacyKey, JSON.stringify(jobPostsArray));
      }
    },

    // Method to check if privacy popup has been shown for this job post
    checkPrivacyPopupStatus() {
      // If no job post ID or user is logged in, don't proceed
      if (!this.jobPostId) return;

      const acceptedPrivacyKey = "acceptedPrivacyJobPosts";
      let acceptedJobPosts = localStorage.getItem(acceptedPrivacyKey);

      // Check if user has a valid session
      const candidateId = Cookies.get("portalCandidateId");
      const expiryTime = Cookies.get("portalCandidateExpiry");

      // If user has a valid session, don't show privacy popup
      if (candidateId && expiryTime && new Date() < new Date(expiryTime)) {
        this.presentPrivacyPopup = false;
        this.showForm = true;
        this.dataPrivacyConfirmation = 1;
      }

      if (acceptedJobPosts) {
        const jobPostsArray = JSON.parse(acceptedJobPosts);
        // If this job post is in the array, don't show the popup
        if (jobPostsArray.includes(this.jobPostId)) {
          this.presentPrivacyPopup = false;
          this.dataPrivacyConfirmation = 1;
          this.openAccountConfirmationModel = true;
        } else {
          this.presentPrivacyPopup = true;
          this.showForm = false;
        }
      }
    },

    // Method to reset privacy popup status for this job post after form submission
    resetPrivacyPopupStatus() {
      if (!this.jobPostId) return;

      const acceptedPrivacyKey = "acceptedPrivacyJobPosts";
      let acceptedJobPosts = localStorage.getItem(acceptedPrivacyKey);

      if (acceptedJobPosts) {
        let jobPostsArray = JSON.parse(acceptedJobPosts);
        // Remove this job post from the array
        jobPostsArray = jobPostsArray.filter(
          (id) => id != parseInt(this.jobPostId)
        );
        localStorage.setItem(acceptedPrivacyKey, JSON.stringify(jobPostsArray));
      }
    },
    handleMenuClick(action) {
      this.menuOpen = false;
      if (action?.toLowerCase() === "signin") this.openSignInModel = true;
      else if (action?.toLowerCase() === "signout") this.signoutCandidate();
      else if (action?.toLowerCase() === "profile") {
        this.$router.push("/candidate-portal?action=profile");
      } else if (action?.toLowerCase() === "applicationstatus") {
        this.$router.push("/candidate-portal?action=applicationstatus");
      } else if (action?.toLowerCase() === "viewjobs") {
        this.$router.push("/candidate-portal?action=viewjobs");
      }
    },
    closeSigninModel() {
      this.openSignInModel = false;
      this.presentOTPSetion = false;
      this.showForm = true;
      this.signInEmail = "";
      this.signInPassword = "";
    },
    signoutCandidate(timeout = false) {
      this.enableCandidatePortal = false;
      this.signInEmail = "";
      this.signInPassword = "";
      this.presentOTPSetion = false;
      this.isUserLogedIn = false;
      this.editedDetails = {};
      Cookies.remove("portalCandidateId");
      // Remove cookies on logout
      Cookies.remove("portalCandidateExpiry");

      // Reset privacy popup status when user signs out
      if (this.jobPostId) {
        this.resetPrivacyPopupStatus();
      }

      if (timeout) {
        var snackbarData = {
          isOpen: true,
          type: "warning",
          message: "You have been signed out successfully",
        };
        this.showAlert(snackbarData);
      }
    },
    checkSessionValidity() {
      setInterval(() => {
        const expiryTime = Cookies.get("portalCandidateExpiry");

        if (expiryTime && new Date() > new Date(expiryTime)) {
          this.signoutCandidate(true);
        }
      }, 5000); // Check every 5 seconds
    },
    validateSignEmail() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: GENERATE_PASSCODE_FOR_PORTAL_ACCESS,
          client: "apolloClientBG",
          variables: {
            emailId: String(vm.signInEmail).trim(),
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.generatePassCodeForPortalAccess &&
            !response.data.generatePassCodeForPortalAccess.errorCode
          ) {
            vm.presentOTPSetion = true;
          } else {
            vm.handleGeneratePassword(
              response.data.generatePassCodeForPortalAccess.errorCode
            );
          }
          vm.isLoading = false;
        })
        .catch((error) => {
          vm.isLoading = false;
          vm.handleGeneratePassword(error);
        });
    },
    handleGeneratePassword(error = "") {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "generate",
        form: "candidate details",
        isListError: false,
      });
    },
    validatesignOTP() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: PASSCODE_VALIDATE_PORTAL_ACCESS,
          client: "apolloClientBG",
          variables: {
            emailId: String(vm.signInEmail).trim(),
            passCode: parseInt(vm.signInPassword),
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.passcodeValidatePortalAccess &&
            !response.data.passcodeValidatePortalAccess.errorCode
          ) {
            vm.candidateId = parseInt(
              response.data.passcodeValidatePortalAccess?.data
            );
            // Set session expiration for 1 day
            const expiryTime = new Date(Date.now() + 24 * 60 * 60 * 1000); // 1 day (24 hours)
            // Store data in cookies
            Cookies.set("portalCandidateId", vm.candidateId, {
              expires: expiryTime,
            });
            Cookies.set("portalCandidateExpiry", expiryTime.toUTCString(), {
              expires: expiryTime,
            }); // Expires in 1 day
            vm.checkSessionValidity();
            vm.$emit("signin-candidate", vm.candidateId);
            vm.isUserLogedIn = true;
            vm.closeSigninModel();
            vm.menuOpen = true;
          } else {
            vm.handleGeneratePassword(
              response.data.passcodeValidatePortalAccess.errorCode
            );
          }
          vm.isLoading = false;
        })
        .catch((error) => {
          vm.isLoading = false;
          vm.handleGeneratePassword(
            error || "There was an error while validating the OTP"
          );
        });
    },
    onChangeDefaultDOB(value) {
      this.editedDetails.DOB = value;
      this.isFormDirty = true;
    },
    onCloseAccountConfirmation() {
      this.openAccountConfirmationModel = false;
      this.showForm = true;
    },
    acceptAccountConfirmation() {
      this.openAccountConfirmationModel = false;
      this.openSignInModel = true;
    },
    getDetailsFromResume() {
      let vm = this;
      if (this.fileContent && this.fileContent.name) {
        vm.isParsingResumeData = true;
        //vm.resetFieldsForResumeParser();
        vm.$apollo
          .query({
            query: RESUME_PARSER,
            client: "apolloClientAO",
            fetchPolicy: "no-cache",
            variables: {
              FileName: this.fileContent.name,
              Content: vm.fileInBase64Format,
            },
          })
          .then((response) => {
            if (
              response.data &&
              response.data.resumeParser &&
              !response.data.resumeParser.errorCode
            ) {
              const { responseData } = response.data.resumeParser;
              if (responseData) {
                let parsedResponse = JSON.parse(responseData);
                if (parsedResponse.profile) {
                  const {
                    firstname,
                    lastName,
                    contactNumber,
                    emailID,
                    middleName,
                    Gender,
                    Address,
                  } = parsedResponse.profile;
                  vm.editedDetails.Candidate_First_Name = firstname;
                  vm.editedDetails.Candidate_Last_Name = lastName;
                  vm.editedDetails.Candidate_Middle_Name =
                    middleName != "NA" ? middleName : "";
                  vm.editedDetails.Mobile_No =
                    contactNumber &&
                    contactNumber !== "NA" &&
                    contactNumber.length > 0
                      ? contactNumber[0]
                      : "";
                  vm.editedDetails.Personal_Email =
                    emailID && emailID.length > 0 && emailID !== "NA"
                      ? typeof emailID == "object"
                        ? emailID[0]
                        : emailID
                      : "";
                  let dob =
                    typeof parsedResponse.profile["Date Of Birth"] == "object"
                      ? parsedResponse.profile["Date Of Birth"]["day"] +
                        parsedResponse.profile["Date Of Birth"]["month"] +
                        parsedResponse.profile["Date Of Birth"]["year"]
                      : parsedResponse.profile["Date Of Birth"] != "NA"
                      ? parsedResponse.profile["Date Of Birth"]
                      : undefined;
                  if (dob) {
                    dob = new Date(dob);
                  }
                  vm.editedDetails.DOB = dob;
                  this.formattedDob = this.formatDate(dob);
                  let sGender = vm.genderList.filter(
                    (el) => el.gender.toLowerCase() == Gender.toLowerCase()
                  );
                  if (sGender.length > 0) {
                    vm.editedDetails.Gender_Id = sGender[0].genderId;
                  }
                  if (Address && Address != "NA" && Address.length > 0) {
                    let street1 = Address[0].Street1;
                    let city = Address[1].City;
                    let state = Address[2].State;
                    let country = Address[3].Country;
                    let pincode = Address[4]["Postal Code"];
                    if (
                      city &&
                      city != "NA" &&
                      city != "" &&
                      vm.labelList[143] &&
                      vm.labelList[143].Predefined === "Yes"
                    ) {
                      let filterCity = vm.cityList.filter(
                        (el) => el.City_Name.toLowerCase() == city.toLowerCase()
                      );
                      if (filterCity && filterCity[0]) {
                        let details = filterCity[0].cityStateDetails
                          ? filterCity[0].cityStateDetails.split(", ")
                          : ["", ""];
                        vm.editedDetails["pState"] = details[1];
                        vm.editedDetails["pCountry"] =
                          filterCity[0].Country_Code;
                      }
                    } else {
                      vm.editedDetails.pCity = city != "NA" ? city : "";
                      vm.editedDetails["pState"] = state != "NA" ? state : "";
                      let sCountry = vm.countryList.filter(
                        (el) =>
                          el.Country_Name.toLowerCase() == country.toLowerCase()
                      );
                      if (sCountry.length > 0) {
                        vm.editedDetails.pCountry = sCountry[0].Country_Code;
                      }
                    }
                    vm.editedDetails.pApartment_Name =
                      street1 != "NA" ? street1 : "";
                    vm.editedDetails["pPincode"] =
                      pincode != "NA" ? pincode : "";
                  }
                }
                if (parsedResponse.professionalExperience) {
                  const { skills } = parsedResponse.professionalExperience[0];
                  vm.skillSet = skills ? skills : [];
                }
                if (parsedResponse.professionalQualification) {
                  const { education, certifications } =
                    parsedResponse.professionalQualification;
                  if (education && education != "NA" && education.length > 0) {
                    vm.editedDetails.Candidate_Education = [];
                    for (let edu of education) {
                      if (
                        edu.Bachelors &&
                        edu.Bachelors.InstituteName != "NA" &&
                        edu.Bachelors.InstituteName != "" &&
                        edu.Bachelors.DegreeName != "NA" &&
                        edu.Bachelors.DegreeName != "" &&
                        edu.Bachelors.year_of_passing != "NA" &&
                        edu.Bachelors.year_of_passing != ""
                      ) {
                        vm.editedDetails.Candidate_Education.push({
                          Education_Type: "",
                          Education_Type_Id: 4,
                          Specialisation: edu.Bachelors.DegreeName,
                          Institute_Name: edu.Bachelors.InstituteName,
                          Year_Of_Passing: edu.Bachelors.year_of_passing
                            ? parseInt(
                                edu.Bachelors.year_of_passing.match(/\b\d{4}\b/)
                              )
                            : null,
                          Percentage: edu.Bachelors.percentage
                            ? edu.Bachelors.percentage !== "NA"
                              ? edu.Bachelors.percentage
                              : 0
                            : 0,
                        });
                      }
                      if (
                        edu.Masters &&
                        edu.Masters.InstituteName != "NA" &&
                        edu.Masters.InstituteName != "N/A" &&
                        edu.Masters.InstituteName != "" &&
                        edu.Masters.DegreeName != "NA" &&
                        edu.Masters.DegreeName != "N/A" &&
                        edu.Masters.DegreeName != "" &&
                        edu.Masters.year_of_passing != "NA" &&
                        edu.Masters.year_of_passing != "N/A" &&
                        edu.Masters.year_of_passing != ""
                      ) {
                        vm.editedDetails.Candidate_Education.push({
                          Education_Type: "",
                          Education_Type_Id: 10,
                          Specialisation: edu.Masters.DegreeName,
                          Institute_Name: edu.Masters.InstituteName,
                          Year_Of_Passing: edu.Masters.year_of_passing
                            ? parseInt(edu.Masters.year_of_passing)
                            : null,
                          Percentage: edu.Masters.percentage
                            ? edu.Masters.percentage !== "NA"
                              ? edu.Masters.percentage
                              : 0
                            : 0,
                        });
                      }
                    }
                  }
                  if (
                    certifications &&
                    certifications != "NA" &&
                    certifications.length > 0
                  ) {
                    for (let certificate of certifications) {
                      vm.editedDetails.Candidate_Certifications.push({
                        Certification_Name: certificate,
                      });
                    }
                  }
                }
                if (parsedResponse.professionalExperience) {
                  let expData = [];
                  let expInYear = null;
                  let expInMonth = null;
                  for (let exp of parsedResponse.professionalExperience) {
                    if (exp.company.text != "NA" && exp.Role != "NA") {
                      // start
                      let sDateFull = undefined;
                      if (exp.startDate) {
                        let sDate = exp.startDate.date;
                        let sMonth = exp.startDate.month;
                        let sYear = exp.startDate.year;
                        // start date
                        sDate = sDate && sDate != "NA" ? parseInt(sDate) : 1;
                        sDate = sDate < 10 ? "0" + sDate.toString() : sDate;
                        // start month
                        sMonth =
                          sMonth && sMonth !== "NA"
                            ? moment(sMonth, "MMMM").month()
                            : 0;
                        sMonth = sMonth + 1;
                        sMonth = sMonth < 10 ? "0" + sMonth.toString() : sMonth;
                        // start year
                        sYear = sYear && sYear !== "NA" ? sYear : "";
                        // start full
                        if (sDate && sMonth && sYear) {
                          sDateFull = sYear + "/" + sMonth + "/" + sDate;
                          sDateFull = new Date(sDateFull);
                          sDateFull = moment(sDateFull).format("YYYY/MM/DD");
                        }
                      }
                      // end
                      let eDateFull = undefined;
                      if (exp.endDate && exp.endDate.month != "Present") {
                        let eDate = exp.endDate.date;
                        let eMonth = exp.endDate.month;
                        let eYear = exp.endDate.year;
                        // end date
                        eDate = eDate && eDate != "NA" ? parseInt(eDate) : 1;
                        eDate = eDate < 10 ? "0" + eDate.toString() : eDate;
                        // end month
                        eMonth =
                          eMonth && eMonth !== "NA"
                            ? moment(eMonth, "MMMM").month()
                            : 0;
                        eMonth = eMonth + 1;
                        eMonth = eMonth < 10 ? "0" + eMonth.toString() : eMonth;
                        // end year
                        eYear = eYear && eYear !== "NA" ? eYear : "";
                        // end full
                        if (eDate && eMonth && eYear) {
                          eDateFull = eYear + "/" + eMonth + "/" + eDate;
                          eDateFull = new Date(eDateFull);
                          eDateFull = moment(eDateFull).format("YYYY/MM/DD");
                        }
                      }
                      expData.push({
                        Prev_Company_Name: exp.company.text,
                        Designation: exp.Role,
                        Start_Date: sDateFull,
                        End_Date: eDateFull,
                      });
                      var date2 = sDateFull
                        ? moment(sDateFull, "YYYY/MM/DD")
                        : "";
                      var date1 = eDateFull
                        ? moment(eDateFull, "YYYY/MM/DD")
                        : "";

                      // Calculate the difference in years and months
                      if (date1 && date2) {
                        var years = date1.diff(date2, "years");
                        date2.add(years, "years");
                        var months = date1.diff(date2, "months");
                        expInYear = expInYear + parseInt(years);
                        expInMonth = expInMonth + parseInt(months);

                        if (expInMonth > 12) {
                          expInYear = expInYear + 1;
                          expInMonth = expInMonth % 12;
                        }
                      }
                    }
                  }
                  vm.editedDetails.Total_Experience_In_Years = expInYear;
                  vm.editedDetails.Total_Experience_In_Months = expInMonth;
                  vm.editedDetails.Candidate_Experience = expData;
                }
              }
            }
            vm.isParsingResumeData = false;
          })
          .catch(() => {
            vm.isParsingResumeData = false;
          });
      }
    },
    formFatherMotherName(details, relation) {
      let motherName = details.filter((el) => el.Relationship === relation);
      return motherName && motherName.length > 0
        ? motherName[0].Dependent_First_Name
        : "";
    },
    checkFieldAvailability(value) {
      if (value) {
        let strValue = value.toString();
        return strValue.trim().length > 0;
      } else return false;
    },
    onChangeFields() {
      this.isFormDirty = true;
    },
    onChangeCustomSelectField(value, field, index) {
      if (field === "Preferred_Location") {
        let selectedLocation = this.jobpostLocations.filter(
          (el) => el.Location_Id == value
        );
        if (
          selectedLocation &&
          selectedLocation[0] &&
          selectedLocation[0].Currency_Id
        ) {
          this.editedDetails["Currency"] = selectedLocation[0].Currency_Id;
        }
      }
      if (field === "Currency") {
        this.editedDetails["Currency_Name"] =
          this.currencyList.find((el) => el.Currency_Id == value)
            ?.Currency_Name || null;
      }
      if (field === "Gender_Id") {
        this.editedDetails["Gender"] =
          this.genderList.find((el) => el.genderId == value)?.gender || null;
      }
      if (field === "Gender_Identity_Id") {
        this.editedDetails["Gender_Identity"] =
          this.genderIdentityList.find((el) => el.Gender_Identity_Id == value)
            ?.Gender_Identity || null;
      }
      if (field === "Marital_Status") {
        this.editedDetails["Marital_Status_Name"] =
          this.maritalStatusList.find((el) => el.Marital_Status_Id == value)
            ?.Marital_Status || null;
      }
      if (field === "Nationality_Id") {
        this.editedDetails["Nationality_Table_Name"] =
          this.nationalityList.find((el) => el.nationalityId == value)
            ?.nationality || null;
      }
      if (
        this.labelList[358]?.Field_Visiblity?.toLowerCase() === "yes" &&
        this.labelList[383]?.Field_Visiblity?.toLowerCase() === "yes"
      ) {
        if (field === "Lang_Id") {
          const selectedLang = this.languageList.find(
            (el) => el.Lang_Id === value
          );
          if (this.editedDetails["Lang_Known"][index]) {
            this.editedDetails["Lang_Known"][index].Lang_Id =
              selectedLang?.Lang_Id || null;
            this.editedDetails["Lang_Known"][index].Language_Name =
              selectedLang?.Language_Name || null;
          }
        }
        if (field === "langProficiency") {
          // Update the specific index in Lang_Known
          const proficiency =
            this.languageProficiencyList.find(
              (el) => el.Language_Proficiency === value
            )?.Language_Proficiency || null;

          if (this.editedDetails["Lang_Known"][index]) {
            this.editedDetails["Lang_Known"][index].Lang_Proficiency =
              proficiency || null;
          }
        }
      }
      if (field === "Work_Permit_Ids") {
        let workList =
          this.workPermitList.filter((el) =>
            value.includes(el.Work_Authorization_Id)
          ) || [];

        workList = workList.map((el) => ({
          Work_Permit_Id: el.Work_Authorization_Id,
          Work_Permit: el.Work_Authorization_Name,
        }));

        this.editedDetails["Work_Permit"] = workList;
      }
      if (field === "Gender_Expression_Id") {
        this.editedDetails["Gender_Expression"] =
          this.genderExpressionList.find(
            (el) => el.Gender_Expression_Id == value
          )?.Gender_Expression || null;
      }
      this.onChangeFields();
      this.editedDetails[field] = value;
    },
    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.closeEditForm();
    },
    formattedFileName(fileName) {
      if (fileName) {
        let fileNameChunks = fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "File";
      }
      return "";
    },
    isValidDate(dateString) {
      // Check if the date string is in ISO 8601 format
      const isoFormat = moment(dateString, moment.ISO_8601, true).isValid();
      // Check if the date string is in RFC 2822 format
      const rfc2822Format = moment(
        dateString,
        "ddd, DD MMM YYYY HH:mm:ss ZZ",
        true
      ).isValid();
      return isoFormat || rfc2822Format;
    },
    removeFiles() {
      this.editedDetails["Resume"] = "";
      this.editedDetails["Resume_File_Size"] = "";
      this.fileContent = null;
      this.resetFieldsForResumeParser();
    },
    getDropdownDetails() {
      let vm = this;
      vm.dropdownLoading = true;
      const {
        clients: { apolloClientAO, apolloClientA },
      } = vm.$apolloProvider;

      const selectedClient =
        vm.jobPostId || vm.isUserLogedIn ? apolloClientAO : apolloClientA;
      vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: selectedClient,
          payload: {
            formId: 16,
            key: [
              "Gender_Identity_Id",
              "Gender_Expression_Id",
              "Privacy_Statement_Id",
              "gender_orientations",
              "gender_pronoun",
              "language_proficiency",
            ],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            tempData.forEach((item) => {
              if (item.tableKey?.toLowerCase() === "gender_identity_id") {
                vm.genderIdentityList = item.data.map((dataItem) => ({
                  Gender_Identity_Id: dataItem.Gender_Identity_Id,
                  Gender_Identity: dataItem.Gender_Identity,
                }));
              } else if (
                item.tableKey?.toLowerCase() === "gender_expression_id"
              ) {
                vm.genderExpressionList = item.data.map((dataItem) => ({
                  Gender_Expression_Id: dataItem.Gender_Expression_Id,
                  Gender_Expression: dataItem.Gender_Expression,
                }));
              } else if (item.tableKey?.toLowerCase() === "gender_pronoun") {
                vm.pronounList = item.data.map((dataItem) => ({
                  Gender_Pronoun_Id: dataItem.Gender_Pronoun_Id,
                  Gender_Pronoun_Name: dataItem.Gender_Pronoun_Name,
                }));
              } else if (
                item.tableKey?.toLowerCase() === "gender_orientations"
              ) {
                vm.genderOrientationList = item.data.map((dataItem) => ({
                  Gender_Orientations_Id: dataItem.Gender_Orientations_Id,
                  Gender_Orientations_Name: dataItem.Gender_Orientations_Name,
                }));
              } else if (
                item.tableKey?.toLowerCase() === "language_proficiency"
              ) {
                vm.languageProficiencyList = item.data.map((dataItem) => ({
                  Language_Proficiency_Id: dataItem.Language_Proficiency_Id,
                  Language_Proficiency: dataItem.Language_Proficiency,
                }));
              } else if (
                item.tableKey?.toLowerCase() === "privacy_statement_id"
              ) {
                const structureData = item.data.find(
                  (dataItem) => dataItem.Form_Id === 311
                );
                if (structureData && Object.keys(structureData)?.length) {
                  vm.privacyData = structureData;
                  vm.presentPrivacyPopup = true;
                  // Check if privacy popup has been shown for this job post
                  this.checkPrivacyPopupStatus();
                  setTimeout(() => {
                    vm.initQuillEditor();
                  }, 0);
                }
              }
            });
          } else {
            let err = res.data.retrieveDropdownDetails.errorCode;
            vm.handleGetDropdownDetails(err);
          }
          vm.dropdownLoading = false;
        })
        .catch((err) => {
          vm.dropdownLoading = false;
          vm.handleGetDropdownDetails(err);
        })
        .finally(() => {
          if (this.showJobHeader) {
            this.retrieveJobHeader();
          }
        });
    },
    handleGetDropdownDetails(err) {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "dropdown details",
        isListError: false,
      });
    },
    initQuillEditor() {
      if (this.$refs.editorView) {
        this.quill = new Quill(this.$refs.editorView, {
          theme: "snow",
        });
        this.setEditorFontSize("14px");
        this.quill.root.innerHTML = this.privacyData?.Template_Data;
        this.quill.enable(false);
      } else {
        // Handle the case when the container is not found
        this.$emit("error", "Quill container not found");
      }
    },
    setEditorFontSize(fontSize) {
      const editorElement = this.$refs.editorView.querySelector(".ql-editor");
      if (editorElement) {
        editorElement.style.fontSize = fontSize;
      }
    },
    resetFieldsForResumeParser() {
      this.editedDetails.Candidate_First_Name = "";
      this.editedDetails.Candidate_Last_Name = "";
      this.editedDetails.Emp_Pref_First_Name = "";
      this.editedDetails.Candidate_Middle_Name = "";
      this.editedDetails.pCity_Id = null;
      this.editedDetails.pCity = "";
      this.editedDetails.Mobile_No = "";
      this.editedDetails.DOB = null;
      this.editedDetails.Gender_Id = null;
      this.editedDetails.Gender_Identity_Id = null;
      this.editedDetails.Gender_Expression_Id = null;
      this.editedDetails.pState = "";
      this.editedDetails.pCountry = "";
      this.editedDetails.pPincode = "";
      this.editedDetails.pStreet_Name = "";
      this.editedDetails.Candidate_Experience = [];
      this.editedDetails.Candidate_Certifications = [];
      this.editedDetails.Personal_Email = "";
      this.editedDetails.pBarangay_Id = null;
      this.editedDetails.pBarangay = "";
      this.editedDetails.pRegion = "";
      this.editedDetails.Emergency_Contact_Name = "";
      this.editedDetails.Fax_No = "";
      this.editedDetails.Emp_Pref_First_Name = "";
      this.skillSet = [];
      this.editedDetails.Candidate_Education = [];
      this.editedDetails.Total_Experience_In_Years = null;
      this.editedDetails.Total_Experience_In_Months = null;
      this.onChangeFields();
    },
    closeEditForm() {
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        this.openBottomSheet = false;
        this.$emit("close-edit-form");
      }
    },
    fillDataForPreviewScreen(data) {
      this.previewData = { ...this.previewData, ...data };
    },
    backToEditScreen() {
      this.showPreviewScreen = false;
    },
    openPreview() {
      let wholeData = { ...this.previewData, ...this.editedDetails } || {};
      wholeData["Job_Post_Name"] = this.jobPostName || "";
      wholeData["Physically_Challenged"] =
        this.editedDetails?.Physically_Challenged || 0;
      if (this.labelList[325]?.Field_Visiblity?.toLowerCase() === "yes") {
        let langList =
          this.languageList.filter((el) =>
            this.editedDetails["Lang_Known"].includes(el.Lang_Id)
          ) || [];

        langList =
          langList?.map((el) => ({
            Lang_Id: el.Lang_Id,
            Language_Name: el.Language_Name,
            Lang_Spoken: null,
            Lang_Read_Write: null,
            Lang_Proficiency: "",
          })) || [];

        wholeData["Lang_Known"] = langList;
      }
      if (
        this.labelList[356]?.Field_Visiblity?.toLowerCase() === "yes" &&
        this.labelList[357]?.Field_Visiblity?.toLowerCase() === "yes"
      )
        wholeData["Lang_Known"] =
          wholeData["Lang_Known"]?.map(
            ({ langSpoken, langReadWrite, ...rest }) => ({
              Lang_Spoken: langSpoken,
              Lang_Read_Write: langReadWrite,
              ...rest,
            })
          ) || [];
      wholeData["Country_Name"] =
        this.countryList.find(
          (el) => el.Country_Code === this.editedDetails.pCountry
        )?.Country_Name || null;
      wholeData["Candidate_Dependent"] = [
        {
          Dependent_First_Name: this.editedDetails?.Father_Name,
          Relationship: "Father",
        },
        {
          Dependent_First_Name: this.editedDetails?.Mother_Name,
          Relationship: "Mother",
        },
      ];
      this.showPreviewScreen = true;
      this.previewData = wholeData;
    },
    fillCurrentAddressBasedOnPermanentAddress() {
      if (this.sameAsPermanent) {
        this.editedDetails.cStreet_Name = this.editedDetails.pStreet_Name;
        this.editedDetails.cBarangay_Id = this.editedDetails.pBarangay_Id;
        this.editedDetails.cCity_Id = this.editedDetails.pCity_Id;
        this.editedDetails.cCity = this.editedDetails.pCity;
        this.editedDetails.cState = this.editedDetails.pState;
        this.editedDetails.cPincode = this.editedDetails.pPincode;
        this.editedDetails.cCountry = this.editedDetails.pCountry;
        this.editedDetails.cApartment_Name = this.editedDetails.pApartment_Name;
      }
      this.onChangeFields();
    },

    resetPermanentAddress() {
      this.editedDetails.pStreet_Name = "";
      this.editedDetails.pCity_Id = null;
      this.editedDetails.pBarangay_Id = null;
      this.editedDetails.pBarangay = "";
      this.editedDetails.pCity = "";
      this.editedDetails.pState = "";
      this.editedDetails.pPincode = "";
      this.editedDetails.pRegion = "";
      this.editedDetails.pCountry = "";
      this.editedDetails.pApartment_Name = "";
    },

    setPermanentAddress(addressData, fullData) {
      this.resetPermanentAddress();
      for (const component of fullData.address_components) {
        const componentType = component.types[0];

        switch (componentType) {
          case "street_number":
          case "street_address":
          case "premise":
          case "establishment":
          case "route": {
            this.editedDetails.pApartment_Name &&
            this.editedDetails.pApartment_Name.length
              ? (this.editedDetails.pApartment_Name =
                  this.editedDetails.pApartment_Name +
                  " " +
                  component.long_name)
              : (this.editedDetails.pApartment_Name = component.long_name);
            break;
          }

          case "neighborhood": {
            this.editedDetails.pStreet_Name = component.long_name;
            break;
          }

          case "administrative_area_level_2":
          case "sublocality_level_2":
          case "sublocality_level_1": {
            this.editedDetails.pStreet_Name =
              this.editedDetails.pStreet_Name &&
              this.editedDetails.pStreet_Name.length
                ? (this.editedDetails.pStreet_Name =
                    this.editedDetails.pStreet_Name +
                    ", " +
                    component.long_name)
                : (this.editedDetails.pStreet_Name = component.long_name);
            break;
          }

          case "locality": {
            this.editedDetails.pCity && this.editedDetails.pCity.length
              ? ""
              : (this.editedDetails.pCity = component.long_name);
            break;
          }

          case "administrative_area_level_3": {
            this.editedDetails.pCity && this.editedDetails.pCity.length
              ? this.editedDetails.pStreet_Name &&
                this.editedDetails.pStreet_Name.length
                ? (this.editedDetails.pStreet_Name =
                    this.editedDetails.pStreet_Name + " " + component.long_name)
                : (this.editedDetails.pStreet_Name = component.long_name)
              : (this.editedDetails.pCity = component.long_name);
            break;
          }

          case "administrative_area_level_1": {
            this.editedDetails.pState = component.long_name;
            break;
          }

          case "country": {
            const selectedCountry = this.countryList.filter((item) => {
              return item.Country_Name === component.long_name;
            });
            if (selectedCountry && selectedCountry.length > 0) {
              this.editedDetails.pCountry = selectedCountry[0].Country_Code;
            }
            break;
          }

          case "postal_code": {
            this.editedDetails.pPincode = component.long_name;
            break;
          }

          case "postal_code_suffix": {
            this.editedDetails.pPincode = `${this.editedDetails.pPincode}-${component.long_name}`;
            break;
          }
        }
      }
      if (this.editedDetails.pCity?.length && this.cityList) {
        let filterCity = this.cityList.filter(
          (el) =>
            el.City_Name.toLowerCase() == this.editedDetails.pCity.toLowerCase()
        );
        if (filterCity && filterCity[0]) {
          this.editedDetails.pCity_Id = filterCity[0].City_Id;
        }
      }
      let allFieldHasValue = false;
      if (
        this.editedDetails.pStreet_Name &&
        (this.editedDetails.pBarangay_Id || this.editedDetails.pBarangay) &&
        (this.editedDetails.pCity_Id || this.editedDetails.pCity) &&
        this.editedDetails.pState &&
        this.editedDetails.pPincode &&
        this.editedDetails.pRegion &&
        this.editedDetails.pCountry &&
        this.editedDetails.pApartment_Name
      ) {
        allFieldHasValue = true;
      }
      this.enableEditAutoPermanentAddress = !allFieldHasValue;
      this.fillCurrentAddressBasedOnPermanentAddress();
      this.onChangeFields();
    },

    getCountryCode(countryCode) {
      if (countryCode) {
        this.mobileNoCountryCode = countryCode.dialCode;
        this.editedDetails.Mobile_No_Country_Code = "+" + countryCode.dialCode;

        if (countryCode.iso2) {
          this.defaultCountryCode = countryCode.iso2.toLowerCase();

          if (countryCode.iso2.toLowerCase() == "in") {
            this.editedDetails.Currency = 72;
          } else if (countryCode.iso2.toLowerCase() == "ph") {
            this.editedDetails.Currency = 121;
          } else if (countryCode.iso2.toLowerCase() == "id") {
            this.editedDetails.Currency = 73;
          } else if (countryCode.iso2.toLowerCase() == "th") {
            this.editedDetails.Currency = 151;
          }
          if (
            this.editedDetails.Nationality_Id == null &&
            this.editedDetails.Nationality == null
          ) {
            this.assignNationalityBasedOnCode();
          }
          this.editedDetails["Currency_Name"] =
            this.currencyList.find(
              (el) => el.Currency_Id == this.editedDetails.Currency
            )?.Currency_Name || "";
        } else {
          this.editedDetails.Currency = null;
        }
      } else {
        this.editedDetails.Mobile_No_Country_Code = "";
      }
    },

    validateMobileNumber(param) {
      this.isValidMobileNumber = param.valid;
      this.onChangeFields();
    },
    async validateExperience() {
      if (this.$refs.experienceMain) {
        let vResponse = await this.$refs.experienceMain.validateAddEditForm();
        if (vResponse == false) {
          this.isValidExperience = false;
        } else {
          this.editedExpDetails = vResponse;
          this.editedDetails.Candidate_Experience = [];
          this.isValidExperience = true;
        }
      } else {
        this.isValidExperience = true;
      }
      return;
    },
    async validateEducation() {
      if (this.$refs.careerMain) {
        let vResponse =
          await this.$refs.careerMain.validateEducationAddEditForm();
        if (vResponse == false) {
          this.isValidEducation = false;
        } else {
          this.editedEducationDetails = vResponse;
          this.editedDetails.Candidate_Education = [];
          this.isValidEducation = true;
        }
      } else {
        this.isValidEducation = true;
      }
      return;
    },
    async validateCertification() {
      if (this.$refs.careerMain) {
        let vResponse =
          await this.$refs.careerMain.validateCertificationAddEditForm();
        if (vResponse == false) {
          this.isValidCertification = false;
        } else {
          this.editedCertificationDetails = vResponse;
          this.editedDetails.Candidate_Certifications = [];
          this.isValidCertification = true;
        }
      } else {
        this.isValidCertification = true;
      }
      return;
    },
    async validateCustomFields() {
      if (this.$refs.customFieldsMain) {
        let vResponse =
          await this.$refs.customFieldsMain.validateCustomFieldsEditForm();
        if (!vResponse || Object.keys(vResponse).length === 0) {
          this.isValidCustomFields = false;
        } else {
          this.editedCustomFieldDetails = vResponse;
          this.editedDetails.customFieldInputs = vResponse;
          this.isValidCustomFields = true;
        }
      } else {
        this.isValidCustomFields = true;
      }
      return;
    },
    async validateEditForm() {
      if (!this.editedDetails || !this.editedDetails["Resume"]) {
        this.errorMessage = "Resume is Required";
      } else {
        this.errorMessage = "";
      }
      if (this.isSimpleForm && (this.jobPostId || this.isPortalLogedIn)) {
        let isFormValid = await this.$refs.simpleForm.validate();
        await Promise.all([this.validateCustomFields()]);
        if (
          isFormValid &&
          isFormValid.valid &&
          !this.mobileNumberValidation &&
          this.isValidCustomFields &&
          this.errorMessage == ""
        ) {
          this.updateCandidateDetails();
        }
      } else {
        let isFormValid = await this.$refs.editDetailsForm.validate();
        await Promise.all([
          this.validateExperience(),
          this.validateEducation(),
          this.validateCertification(),
          this.validateCustomFields(),
        ]);
        if (
          isFormValid &&
          isFormValid.valid &&
          !this.mobileNumberValidation &&
          this.isValidExperience &&
          this.isValidEducation &&
          this.isValidCertification &&
          this.isValidCustomFields &&
          this.errorMessage == ""
        ) {
          this.DOBErrorMsg = "";
          this.updateCandidateDetails();
        } else {
          this.DOBErrorMsg = "Date of Birth is required";
          // Check the validity of each field
          const invalidFields = [];
          Object.keys(this.$refs).forEach((refName) => {
            const field = this.$refs[refName];
            if (field && field.rules) {
              let allTrue = field.rules.every((value) => value === true);
              if (field.rules.length > 0 && !allTrue) {
                invalidFields.push(refName);
              }
            }
          });
          // Log or handle the invalid fields
          if (invalidFields.length > 0) {
            const firstErrorField = invalidFields[0];
            this.$nextTick(() => {
              const fieldRef = this.$refs[firstErrorField];
              if (fieldRef) {
                let langKnown = [];
                for (let i = 0; i < this.editedDetails.Lang_Known.length; i++) {
                  langKnown.push(`langKnown${i}`);
                  if (this.labelList[358]?.Predefined?.toLowerCase() === "yes")
                    langKnown.push(`langProficiency${i}`);
                }
                let selectFields = [
                  "salutation",
                  "gender",
                  "genderIdentityId",
                  "genderExpressionId",
                  "genderOrientation",
                  "pronoun",
                  "maritalStatus",
                  "bloodGroup",
                  "languageKnown",
                  "workPermit",
                  "source",
                  "jobTitle",
                  "preferredLocation",
                  "currency",
                  "nationalityList",
                  "pCountry",
                ];
                selectFields = selectFields.concat(langKnown);
                if (selectFields.includes(firstErrorField)) {
                  fieldRef.onFocusCustomSelect();
                } else {
                  // except for select
                  fieldRef.focus();
                }
                if (fieldRef.$el) {
                  const rect = fieldRef.$el.getBoundingClientRect();
                  window.scrollTo({
                    top: (window.scrollY + rect.top) * 2, // Adjust as needed
                    behavior: "smooth",
                  });
                }
              }
            });
          }
        }
      }
    },
    redirectToCandidatePortal() {
      this.$router.push({ path: "/candidate-portal" });
    },
    onChangePBarangay(val) {
      if (
        this.labelList[332] &&
        this.labelList[332].Predefined.toLowerCase() === "yes"
      ) {
        let filterCity = this.barangayList.filter(
          (el) => el.Barangay_Id == val
        );
        if (filterCity && filterCity[0]) {
          this.editedDetails.pBarangay = filterCity[0].Barangay_Name;
          this.editedDetails.pRegion = filterCity[0].Region_Name;
          this.editedDetails.pCity_Id = filterCity[0].City_Id;
          this.editedDetails.pCity = filterCity[0].City_Name;
          this.editedDetails.pState = filterCity[0].State_Name;
          this.editedDetails.pCountry = filterCity[0].Country_Code;
        }
      }
    },
    onUpdatePBarangay(value) {
      this.onChangePBarangay(value);
    },
    retrievePartnerId() {
      let vm = this;
      vm.$apollo
        .query({
          query: GET_PARTNER_ID,
          client: "apolloClientX",
          fetchPolicy: "no-cache",
          variables: {
            Org_Code: vm.orgCode,
          },
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.getCompanyPartner &&
            res.data.getCompanyPartner.partnerIntegration
          ) {
            window.$cookies.set(
              "partnerid",
              res.data.getCompanyPartner.partnerIntegration,
              "1d"
            );
          } else {
            window.$cookies.set("partnerid", "-", "1d");
          }
        });
    },
    updateCandidateDetails() {
      let vm = this;
      vm.isLoading = true;
      let langArray = [];
      let languagesKnown = [];
      langArray = vm.editedDetails.Lang_Known;
      if (this.labelList[325]?.Field_Visiblity?.toLowerCase() === "yes") {
        languagesKnown = langArray.map((langId) => ({
          langKnown: langId,
          langSpoken: null,
          langReadWrite: null,
          langProficiency: "",
        }));
      } else if (
        this.labelList[383]?.Field_Visiblity?.toLowerCase() === "yes"
      ) {
        languagesKnown = langArray.map((lang) => ({
          langKnown: lang.Lang_Id,
          langSpoken: lang.langSpoken,
          langReadWrite: lang.langReadWrite,
          langProficiency: lang.langProficiency,
        }));
      } else {
        languagesKnown = [];
      }
      let educationArray = [];
      let editedEducationArray =
        vm.editedEducationDetails && vm.editedEducationDetails.length > 0
          ? vm.editedEducationDetails
          : vm.editedDetails.Candidate_Education;
      if (editedEducationArray && editedEducationArray.length > 0) {
        for (let education of editedEducationArray) {
          let eObj = {
            educationType: education.Education_Type_Id,
            institute: education.Institute_Name,
            speacialisation: education.Specialisation,
            yearOfStart: education.Year_Of_Start
              ? parseInt(education.Year_Of_Start)
              : 0,
            yearOfPassing: education.Year_Of_Passing
              ? parseInt(education.Year_Of_Passing)
              : 0,
            percentage: education.Percentage
              ? parseFloat(education.Percentage)
              : 0,
            institutionId: education.Institution_Id
              ? education.Institution_Id
              : 0,
            specializationId: education.Specialization_Id
              ? education.Specialization_Id
              : 0,
            startDate: education.Start_Date,
            endDate: education.End_Date,
            city: education.City,
            state: education.State,
            country: education.Country,
          };
          educationArray.push(eObj);
        }
      }
      let certificateArray = [];
      let editedCertificationArray =
        vm.editedCertificationDetails &&
        vm.editedCertificationDetails.length > 0
          ? vm.editedCertificationDetails
          : vm.editedDetails.Candidate_Certifications;
      if (editedCertificationArray && editedCertificationArray.length > 0) {
        for (let certificate of editedCertificationArray) {
          let cObj = {
            certificationName: certificate.Certification_Name,
            receivedDate: moment(certificate.Received_Date).isValid()
              ? moment(certificate.Received_Date, "YYYY/MM/DD").format(
                  "DD/MM/YYYY"
                )
              : null,
            certificateReceivedFrom: certificate.Certificate_Received_From,
            certificationFileName: "",
            ranking: certificate.Ranking || null,
          };
          certificateArray.push(cObj);
        }
      }
      let experienceArray = [];
      let editedExpArray =
        vm.editedExpDetails && vm.editedExpDetails.length > 0
          ? vm.editedExpDetails
          : vm.editedDetails.Candidate_Experience;
      if (editedExpArray && editedExpArray.length > 0) {
        for (let experience of editedExpArray) {
          let exObj = {
            companyName: experience.Prev_Company_Name,
            designation: experience.Designation,
            startDate: moment(experience.Start_Date).isValid()
              ? moment(experience.Start_Date, "YYYY/MM/DD").format("DD/MM/YYYY")
              : null,
            endDate: moment(experience.End_Date).isValid()
              ? moment(experience.End_Date, "YYYY/MM/DD").format("DD/MM/YYYY")
              : null,
            companyLocation: experience.Prev_Company_Location || null,
            referenceDetails: experience.References || [],
          };
          experienceArray.push(exObj);
        }
      }
      let dependent = [];
      if (vm.editedDetails.Father_Name) {
        dependent.push(vm.editedDetails.Father_Name);
      }
      if (vm.editedDetails.Mother_Name) {
        dependent.push(vm.editedDetails.Mother_Name);
      }
      let genderValue = vm.genderList.filter(
        (el) => el.genderId == vm.editedDetails.Gender_Id
      );
      let apiVariables = {
        salutation: vm.editedDetails.Salutation
          ? vm.editedDetails.Salutation
          : "",
        firstName: vm.editedDetails.Candidate_First_Name,
        lastName: vm.editedDetails.Candidate_Last_Name,
        knownAs: vm.editedDetails.Emp_Pref_First_Name || "",
        middleName: vm.editedDetails.Candidate_Middle_Name,
        genderId: parseInt(vm.editedDetails.Gender_Id),
        genderIdentityId: parseInt(vm.editedDetails.Gender_Identity_Id) || null,
        genderExpressionId:
          parseInt(vm.editedDetails.Gender_Expression_Id) || null,
        physicallyChallenged: vm.editedDetails.Physically_Challenged || 0,
        gender:
          genderValue && genderValue.length > 0 ? genderValue[0].gender : "",
        dob: moment(vm.editedDetails.DOB).isValid()
          ? moment(vm.editedDetails.DOB).format("YYYY-MM-DD")
          : null,
        bloodGroup: vm.editedDetails.Blood_Group,
        maritalStatus: vm.editedDetails.Marital_Status,
        nationality:
          this.labelList["271"].Predefined === "No"
            ? vm.editedDetails.Nationality
            : null,
        nationalityId:
          this.labelList["271"].Predefined === "Yes"
            ? vm.editedDetails.Nationality_Id
            : null,
        languagesKnown: languagesKnown,
        candidateDependent: dependent,
        candidateWorkPermit: vm.editedDetails.Work_Permit_Ids,
        candidateOtherWorkPermit: vm.editedDetails.Other_Work_Permit,
        nationalIdentificationNumber:
          vm.editedDetails.National_Identification_Number,
        otherWorkAuthorization: vm.editedDetails.Other_Work_Permit,
        emailId: vm.editedDetails.Personal_Email,
        permanent_barangay_id: vm.editedDetails.pBarangay_Id
          ? vm.editedDetails.pBarangay_Id
          : null,
        barangay: vm.editedDetails.pBarangay,
        region: vm.editedDetails.pRegion,
        emergencyContactName: vm.editedDetails.Emergency_Contact_Name,
        emergencyContactRelation: vm.editedDetails.Emergency_Contact_Relation,
        emergencyContactNo: vm.editedDetails.Fax_No,
        mobileNo: vm.editedDetails.Mobile_No,
        mobileNoCountryCode: vm.editedDetails.Mobile_No_Country_Code,
        apartmentName: vm.editedDetails.pApartment_Name,
        street: vm.editedDetails.pStreet_Name,
        permanent_city_id: vm.editedDetails.pCity_Id
          ? vm.editedDetails.pCity_Id
          : null,
        city: vm.editedDetails.pCity,
        state: vm.editedDetails.pState,
        country: vm.editedDetails.pCountry,
        pincode: vm.editedDetails.pPincode,
        preferredLocation: vm.editedDetails.Preferred_Location,
        candidateEducation: educationArray,
        candidateExperience: experienceArray,
        passportNo: vm.editedDetails.Passport_No,
        candidateCertification: certificateArray,
        candidateSkills: [],
        verifierName: vm.editedDetails.Verifier_Name,
        verifierPhoneNo: vm.editedDetails.Verifier_Phone_Number,
        verifierEmailId: vm.editedDetails.Verifier_Email_Id,
        jobPost: vm.editedDetails.Job_Post_Id,
        currentEmployer: vm.editedDetails.Current_Employer,
        noticePeriod: vm.editedDetails.Notice_Period
          ? parseInt(vm.editedDetails.Notice_Period)
          : null,
        currentCTC: vm.editedDetails.Current_CTC
          ? parseInt(vm.editedDetails.Current_CTC)
          : null,
        expectedCTC: vm.editedDetails.Expected_CTC
          ? parseInt(vm.editedDetails.Expected_CTC)
          : null,
        resume: vm.editedDetails.Resume,
        resumeFileSize: vm.editedDetails.Resume_File_Size,
        currency: vm.editedDetails.Currency,
        status: vm.editedDetails.Status_Id ? vm.editedDetails.Status_Id : 10,
        totalExperienceInYears: vm.editedDetails.Total_Experience_In_Years
          ? parseInt(vm.editedDetails.Total_Experience_In_Years)
          : 0,
        totalExperienceInMonths: vm.editedDetails.Total_Experience_In_Months
          ? parseInt(vm.editedDetails.Total_Experience_In_Months)
          : 0,
        source: vm.editedDetails.Source,
        candidateProfilePicture: "",
        genderOrientations: vm.editedDetails.Gender_Orientations,
        pronoun: vm.editedDetails.Pronoun,
        Suffix: vm.editedDetails.Suffix,
        action: "",
        customFieldInputs: Object.keys(vm.editedCustomFieldDetails).length
          ? vm.editedCustomFieldDetails
          : null,
        dataPrivacyStatement: parseInt(vm.dataPrivacyConfirmation),
      };
      if (
        !vm.candidateIdSelected ||
        (vm.isUserLogedIn && vm.jobPostId && !vm.isPortalLogedIn)
      ) {
        let simpleForm = {
          firstName: vm.editedDetails.Candidate_First_Name,
          lastName: vm.editedDetails.Candidate_Last_Name,
          gender: "",
          dob: moment(vm.editedDetails.DOB).isValid()
            ? moment(vm.editedDetails.DOB).format("YYYY-MM-DD")
            : null,
          emailId: vm.editedDetails.Personal_Email,
          mobileNo: vm.editedDetails.Mobile_No,
          mobileNoCountryCode: vm.editedDetails.Mobile_No_Country_Code,
          jobPost: vm.editedDetails.Job_Post_Id,
          resume: vm.editedDetails.Resume,
          resumeFileSize: vm.editedDetails.Resume_File_Size,
          status: 10,
          customFieldInputs: Object.keys(vm.editedCustomFieldDetails).length
            ? vm.editedCustomFieldDetails
            : null,
          source: vm.editedDetails.Source,
        };
        if (vm.jobPostId && vm.isUserLogedIn) apiVariables.status = 10;
        if (!this.jobPostId || !this.isSimpleForm || this.isUserLogedIn) {
          this.addDetails(apiVariables);
        } else {
          this.addDetails(simpleForm);
        }
      } else {
        let editObj = {
          candidateId: vm.candidateIdSelected,
        };
        let updateObj = { ...editObj, ...apiVariables };
        if (vm.isUserLogedIn) {
          updateObj.status = 0;
          updateObj.jobPost = null;
        }
        this.updateDetails(updateObj);
      }
    },
    updateDetails(variableObj) {
      let vm = this;
      vm.isLoading = true;
      variableObj.skillSet = vm.skillSet;
      variableObj.status = 0;
      vm.$apollo
        .mutate({
          mutation: UPDATE_JOB_CANDIDATE_DETAILS,
          variables: variableObj,
          client:
            vm.jobPostId || vm.isUserLogedIn
              ? "apolloClientAO"
              : "apolloClientA",
        })
        .then((res) => {
          if (res && res.data && res.data.updateJobCandidates) {
            const { errorCode } = res.data.updateJobCandidates;
            if (!errorCode) {
              if (vm.editedDetails["Resume"] && vm.isFileChanged) {
                vm.uploadFileContents();
              } else {
                let snackbarData = {
                  isOpen: true,
                  message: "Candidate details updated successfully",
                  type: "success",
                };
                vm.showAlert(snackbarData);
                vm.isLoading = false;
                if (!vm.jobPostId) {
                  vm.$emit("edit-updated");
                }
              }
            } else {
              vm.isLoading = false;
              vm.handleUpdateError();
            }
          } else {
            vm.isLoading = false;
            vm.handleUpdateError();
          }
          if (vm.candidateIdSelected) {
            vm.$emit("close-candidate-details");
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleUpdateError(err);
        });
    },
    addDetails(variableObj) {
      let vm = this;
      vm.isLoading = true;
      if (!this.jobPostId || !this.isSimpleForm || this.isUserLogedIn) {
        variableObj.skillSet = vm.skillSet;
      }
      vm.$apollo
        .mutate({
          mutation: ADD_JOB_CANDIDATE_DETAILS,
          variables: variableObj,
          client:
            vm.jobPostId || vm.isUserLogedIn
              ? "apolloClientAO"
              : "apolloClientA",
        })
        .then((res) => {
          if (res && res.data && res.data.insertJobCandidates) {
            const { errorCode } = res.data.insertJobCandidates;
            if (!errorCode) {
              vm.previewData = {};
              // Reset privacy popup status when form is submitted
              if (this.jobPostId) {
                this.resetPrivacyPopupStatus();
              }
              if (vm.editedDetails["Resume"] && vm.isFileChanged) {
                vm.uploadFileContents(res.data.insertJobCandidates.data);
              } else {
                if (!vm.jobPostId) {
                  let snackbarData = {
                    isOpen: true,
                    message: "Candidate details added successfully",
                    type: "success",
                  };
                  vm.showAlert(snackbarData);
                }
                vm.isLoading = false;
                if (vm.jobPostId && vm.isUserLogedIn) {
                  let editObj = {
                    candidateId: parseInt(res.data.insertJobCandidates?.data),
                  };
                  let updateObj = { ...editObj, ...variableObj };
                  updateObj.status = 0;
                  updateObj.jobPost = null;
                  this.updateDetails(updateObj);
                }
                vm.$emit("edit-updated", res.data.insertJobCandidates.data);
              }
            } else {
              vm.isLoading = false;
              vm.handleUpdateError();
            }
          } else {
            vm.isLoading = false;
            vm.handleUpdateError();
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleUpdateError(err);
        });
    },
    handleUpdateError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: !this.candidateIdSelected ? "adding" : "updating",
          form: "candidate details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
      if (this.jobPostId && this.isUserLogedIn) {
        this.showpopup = true;
      }
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    async retrieveLanguages() {
      this.languageListLoading = true;
      this.languageList = [];
      await this.$store
        .dispatch("listLanguages", {
          client:
            this.jobPostId || this.isUserLogedIn
              ? "apolloClientAP"
              : "apolloClientI",
        })
        .then((langList) => {
          this.languageList = langList;
          this.languageListLoading = false;
        })
        .catch(() => {
          this.languageListLoading = false;
        });
    },
    retrieveDropDownDetails() {
      let vm = this;
      vm.dropdownListLoading = true;
      vm.$apollo
        .query({
          query: CANDIDATES_DROP_DOWN,
          client:
            vm.jobPostId || vm.isUserLogedIn
              ? "apolloClientAO"
              : "apolloClientA",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getDropDownBoxDetails &&
            !response.data.getDropDownBoxDetails.errorCode.length
          ) {
            const { workAuthorizations, currency } =
              response.data.getDropDownBoxDetails;
            vm.workPermitList =
              workAuthorizations && workAuthorizations.length > 0
                ? workAuthorizations
                : [];
            vm.currencyList = currency && currency.length > 0 ? currency : [];
            vm.editedDetails["Currency_Name"] =
              vm.currencyList.find(
                (el) => el.Currency_Id == vm.editedDetails.Currency
              )?.Currency_Name || "";
          }
          vm.dropdownListLoading = false;
        })
        .catch(() => {
          vm.dropdownListLoading = false;
        });
    },

    async uploadFileContents(candidateId = "") {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "_" + "/" + this.orgCode + "/resume/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + this.editedDetails["Resume"],
          action: "upload",
          type: "documents",
          fileContent: vm.fileContent,
          client:
            vm.jobPostId || vm.isUserLogedIn
              ? "apolloClientAO"
              : "apolloClientA",
        })
        .then(() => {
          if (!vm.jobPostId) {
            let snackbarData = {
              isOpen: true,
              message: !vm.candidateIdSelected
                ? "Candidate details added successfully"
                : "Candidate details updated successfully",
              type: "success",
            };
            if (candidateId !== "updateResume") vm.showAlert(snackbarData);
          }
          if (candidateId !== "updateResume")
            vm.$emit("edit-updated", candidateId);
          vm.isLoading = false;
        })
        .catch(() => {
          let snackbarData = {
            isOpen: true,
            message: !vm.candidateIdSelected
              ? "Candidate details added successfully. But unable to upload the resume"
              : "Candidate details updated successfully. But unable to upload the resume",
            type: "warning",
          };
          vm.showAlert(snackbarData);
          vm.isLoading = false;
          vm.$emit("edit-updated", candidateId);
        });
    },

    retrieveJobPostLocation(inputJobPostId) {
      let vm = this;
      let jobPostIdToSendBackend = inputJobPostId
        ? inputJobPostId
        : vm.editedDetails.Job_Post_Id;
      if (jobPostIdToSendBackend) {
        vm.jobPostLocationLoading = true;
        vm.$apollo
          .query({
            query: LIST_JOB_LOCATIONS,
            client:
              vm.jobPostId || vm.isUserLogedIn
                ? "apolloClientAO"
                : "apolloClientA",
            fetchPolicy: "no-cache",
            variables: {
              jobpostId: jobPostIdToSendBackend
                ? jobPostIdToSendBackend
                : vm.jobPostId,
            },
          })
          .then((response) => {
            if (
              response.data &&
              response.data.listJobPostLocations &&
              !response.data.listJobPostLocations.errorCode.length
            ) {
              vm.jobpostLocations =
                response.data.listJobPostLocations.jobpostLocations;
              if (
                (vm.jobpostLocations &&
                  vm.jobpostLocations.length > 0 &&
                  vm.jobPostId &&
                  !vm.editedDetails.Preferred_Location) ||
                vm.editedDetails.Preferred_Location.length == 0
              ) {
                vm.preferredLocationName = vm.jobpostLocations[0].Location_Name;
                vm.editedDetails.Preferred_Location =
                  vm.jobpostLocations[0].Location_Id;
                vm.editedDetails["Currency"] =
                  vm.jobpostLocations[0].Currency_Id;
              }
            }
            vm.jobPostLocationLoading = false;
          })
          .catch(() => {
            vm.jobPostLocationLoading = false;
          });
      }
    },

    async retrieveCountries() {
      this.countryListLoading = true;
      this.countryList = [];
      await this.$store
        .dispatch("listCountries", {
          client:
            this.jobPostId || this.isUserLogedIn
              ? "apolloClientAP"
              : "apolloClientI",
        })
        .then((langList) => {
          this.countryList = langList;
          this.countryListLoading = false;
        })
        .catch(() => {
          this.countryListLoading = false;
        });
    },

    retrieveJobPosts() {
      let vm = this;
      vm.jobPostListLoading = true;
      vm.$apollo
        .query({
          query: LIST_JOB_POSTS,
          client:
            vm.jobPostId || vm.isUserLogedIn
              ? "apolloClientAO"
              : "apolloClientA",
          variables: {
            searchString: "",
            designation: null,
            functionalArea: null,
            jobType: null,
            closingDate: "",
            status: null,
            location: [],
            isDropDownCall: 1,
            skills: [],
            qualification: [],
            action: vm.candidateIdSelected ? "update" : "add",
            formId: 16,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listJobPost &&
            !response.data.listJobPost.errorCode.length
          ) {
            vm.jobPostList = response.data.listJobPost.JobpostDetails;
            if (vm.candidateIdSelected) {
              vm.jobPostList = vm.jobPostList.filter(
                (el) =>
                  el.Job_Post_Status === "Open" ||
                  el.Job_Post_Id == vm.editedDetails.Job_Post_Id
              );
            } else {
              let openedJobPost = vm.jobPostList.filter(
                (el) => el.Job_Post_Id == vm.editedDetails.Job_Post_Id
              );
              vm.jobPostName = openedJobPost.length
                ? openedJobPost[0].Job_Post_Name
                : "";
            }
            vm.jobPostListLoading = false;
          } else {
            vm.jobPostListLoading = false;
          }
        })
        .catch(() => {
          vm.jobPostListLoading = false;
        });
    },

    retrieveCandidateInterviews() {
      let vm = this;
      if (vm.candidateIdSelected) {
        vm.$apollo
          .query({
            query: RETRIEVE_CANDIDATE_INTERVIEW,
            client:
              vm.jobPostId || vm.isUserLogedIn
                ? "apolloClientAO"
                : "apolloClientA",
            fetchPolicy: "no-cache",
            variables: {
              candidateId: parseInt(vm.candidateIdSelected),
            },
          })
          .then((response) => {
            if (
              response.data &&
              response.data.retrieveCandidatesInterview &&
              !response.data.retrieveCandidatesInterview.errorCode
            ) {
              const { candidateInterview } =
                response.data.retrieveCandidatesInterview;
              if (candidateInterview && candidateInterview.length > 0) {
                vm.disableJobPost = true;
              } else {
                vm.disableJobPost = false;
              }
            } else {
              vm.disableJobPost = false;
            }
          })
          .catch(() => {
            vm.disableJobPost = false;
          });
      } else {
        vm.disableJobPost = false;
      }
    },

    retrieveMaritalStatusList() {
      let vm = this;
      vm.maritalStatusListLoading = true;
      if (vm.jobPostId || vm.isUserLogedIn) {
        vm.$apollo
          .query({
            query: MARITAL_STATUS_LIST,
            client: "apolloClientX",
            variables: {
              Org_Code: this.orgCode,
              Url_Hash: "",
            },
          })
          .then((response) => {
            if (
              response.data &&
              response.data.listMartialStatus &&
              !response.data.listMartialStatus.errorCode
            ) {
              const { marital_status } = response.data.listMartialStatus;
              vm.maritalStatusList = marital_status;
            }
            vm.maritalStatusListLoading = false;
          })
          .catch(() => {
            vm.maritalStatusListLoading = false;
          });
      } else {
        vm.$apollo
          .query({
            query: LIST_MARITAL_STATUS,
            client: "apolloClientAC",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.retrieveMaritalStatus &&
              !response.data.retrieveMaritalStatus.errorCode
            ) {
              const { maritalStatus } = response.data.retrieveMaritalStatus;
              vm.maritalStatusList = maritalStatus
                ? JSON.parse(maritalStatus)
                : [];
            }
            vm.maritalStatusListLoading = false;
          })
          .catch(() => {
            vm.maritalStatusListLoading = false;
          });
      }
    },
    retrieveCities() {
      let vm = this;
      vm.cityListLoading = true;
      vm.$apollo
        .query({
          query: LIST_CITIES_NO_AUTH,
          client: "apolloClientAS",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getCityListWithState &&
            !response.data.getCityListWithState.errorCode
          ) {
            const { cityDetails } = response.data.getCityListWithState;
            vm.cityList = cityDetails;
            if (
              !this.editedDetails.pCity_Id &&
              this.editedDetails.pCity &&
              this.editedDetails.pState
            ) {
              let filterCity = cityDetails.filter(
                (el) =>
                  el.cityStateDetails?.includes(this.editedDetails.pCity) &&
                  el.cityStateDetails?.includes(this.editedDetails.pState)
              );
              if (filterCity && filterCity[0]) {
                this.editedDetails.pCity_Id = filterCity[0].City_Id;
              }
            }
          }
          vm.cityListLoading = false;
        })
        .catch(() => {
          vm.cityListLoading = false;
        });
    },
    retrieveBarangay(searchString) {
      let vm = this;
      vm.isBarangayLoading = true;
      vm.$apollo
        .query({
          query: LIST_BARANGAY,
          client: "apolloClientAS",
          fetchPolicy: "no-cache",
          variables: {
            searchString: searchString,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getBarangayListWithCity &&
            !response.data.getBarangayListWithCity.errorCode &&
            response.data.getBarangayListWithCity.barangayDetails?.length
          ) {
            const { barangayDetails } = response.data.getBarangayListWithCity;
            vm.barangayList = barangayDetails;
          }
          vm.isBarangayLoading = false;
        })
        .catch(() => {
          vm.isBarangayLoading = false;
        });
    },
    retrieveGender() {
      let vm = this;
      vm.genderListLoading = true;
      vm.$apollo
        .query({
          query: GENDER_LIST,
          client: "apolloClientX",
          variables: {
            Org_Code: this.orgCode,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveGenderList &&
            !response.data.retrieveGenderList.errorCode
          ) {
            const { genderData } = response.data.retrieveGenderList;
            vm.genderList = genderData;
          }
          vm.genderListLoading = false;
        })
        .catch(() => {
          vm.genderListLoading = false;
        });
    },
    retrieveSource() {
      let vm = this;
      vm.sourceListLoading = true;
      vm.$apollo
        .query({
          query: LIST_SOURCE_OF_APPLICATION,
          client:
            vm.jobPostId || vm.isUserLogedIn
              ? "apolloClientAO"
              : "apolloClientA",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveSourceMasterList &&
            !response.data.retrieveSourceMasterList.errorCode
          ) {
            const { source } = response.data.retrieveSourceMasterList;
            vm.sourceList = source;
          }
          vm.sourceListLoading = false;
        })
        .catch(() => {
          vm.sourceListLoading = false;
        });
    },
    showAddIcon() {
      this.showIcon = !!this.input.trim();
    },
    addChip() {
      if (this.isInputValid && this.input.trim()) {
        this.skillSet.push(this.input.trim());
        this.input = "";
        this.showIcon = false;
        this.editedDetails["Skill_Set"] = this.skillSet || [];
      }
    },
    removeChip(index) {
      this.skillSet.splice(index, 1);
      this.editedDetails["Skill_Set"] = this.skillSet || [];
    },
    retrieveJobHeader() {
      let vm = this;
      vm.portalAccessLoading = true;
      vm.$apollo
        .query({
          query: GET_JOB_HEADER,
          client: "apolloClientAO",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.recruitmentSetting &&
            res.data.recruitmentSetting.settingResult &&
            res.data.recruitmentSetting.settingResult[0]
          ) {
            this.jobHeader =
              res.data.recruitmentSetting.settingResult[0]
                ?.Equal_opportunity_stmt || null;
            this.isSigninEnabled =
              res.data.recruitmentSetting.settingResult[0]?.Candidate_Portal_Login_Access?.toLowerCase() ===
              "yes";
            this.isSimpleForm =
              res.data.recruitmentSetting.settingResult[0]?.Candidate_Application_Form_Type?.toLowerCase() ===
              "simple";
            this.otpExpireTime =
              res.data.recruitmentSetting.settingResult[0]?.Exp_portal_Expiry_Time_In_Mins;
            if (!this.privacyData || !this.privacyData.Enable) {
              if (this.isSigninEnabled && this.jobPostId) {
                let candidateId = Cookies.get("portalCandidateId");
                if (candidateId) {
                  vm.showForm = true;
                  this.$emit("signin-candidate", candidateId);
                  vm.isUserLogedIn = true;
                  vm.openAccountConfirmationModel = false;
                } else {
                  this.openAccountConfirmationModel = true;
                }
              } else {
                vm.showForm = true;
              }
            }
          }
          vm.portalAccessLoading = false;
        })
        .catch(() => {
          vm.portalAccessLoading = false;
        });
    },
    retrieveNationalityList() {
      let vm = this;
      vm.nationalityListLoading = true;
      vm.$apollo
        .query({
          query: LIST_NATIONALITIES,
          variables: { Org_Code: this.$store.getters.orgCode },
          client: "apolloClientX",
        })
        .then((data) => {
          let res = data.data;
          if (
            res &&
            res.retrieveNationalityList &&
            res.retrieveNationalityList.nationalityData
          ) {
            this.nationalityList = res.retrieveNationalityList.nationalityData;

            if (
              this.editedDetails.Nationality_Id == null &&
              this.editedDetails.Nationality == null
            ) {
              this.assignNationalityBasedOnCode();
            }
            // Prefill Preview Key for public form
            if (this.labelList[271]?.Predefined?.toLowerCase() === "yes") {
              this.editedDetails["Nationality_Table_Name"] =
                this.nationalityList.find(
                  (el) => el.nationalityId == this.editedDetails.Nationality_Id
                )?.nationality || null;
            }
          }
          vm.nationalityListLoading = false;
        })
        .catch(() => {
          vm.nationalityListLoading = false;
        });
    },
    assignNationalityBasedOnCode() {
      let nationalityIdList = this.nationalityList.map((ele) => {
        return ele.nationalityId;
      });
      if (this.defaultCountryCode == "in") {
        if (nationalityIdList.includes(12)) {
          this.editedDetails.Nationality_Id = 12;
          this.editedDetails.Nationality = "Indian";
        }
      } else if (this.defaultCountryCode == "ph") {
        if (nationalityIdList.includes(3)) {
          this.editedDetails.Nationality_Id = 3;
          this.editedDetails.Nationality = "Filipino";
        }
      } else if (this.defaultCountryCode == "id") {
        if (nationalityIdList.includes(5)) {
          this.editedDetails.Nationality_Id = 5;
          this.editedDetails.Nationality = "Indonesian";
        }
      } else if (this.defaultCountryCode == "th") {
        if (nationalityIdList.includes(9)) {
          this.editedDetails.Nationality_Id = 9;
          this.editedDetails.Nationality = "Thai";
        }
      }
      // Prefill Preview Key for public form
      if (this.labelList[271]?.Predefined?.toLowerCase() === "yes") {
        this.editedDetails["Nationality_Table_Name"] =
          this.nationalityList.find(
            (el) => el.nationalityId == this.editedDetails.Nationality_Id
          )?.nationality || null;
      }
    },
    // Custom Color Picker Api
    getOrgDateFormatForNoAuth() {
      let vm = this;
      if (vm.jobPostId) {
        vm.$apollo
          .query({
            query: CUSTOM_COLOR_PICKER,
            client: "apolloClientAO",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.customColorPicker &&
              response.data.customColorPicker.colorResult
            ) {
              const colorResult =
                response.data.customColorPicker.colorResult[0];
              this.dateFormat = colorResult.Date_Format;
              this.orgName =
                response.data.customColorPicker.colorResult[0].Org_Name;
            }
          });
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onChangeFiles(value) {
      let fileContent = value[2];

      if (fileContent && fileContent.name) {
        let d = new Date();
        let dFormat =
          [d.getFullYear(), d.getMonth() + 1, d.getDate()].join("-") +
          "." +
          [d.getHours(), d.getMinutes(), d.getSeconds()].join(":");
        let formattedFileName =
          "resume" + "?" + dFormat + "?" + fileContent.name;
        // let fileSize = fileContent.size.toString();

        if (typeof FileReader === "function") {
          const reader = new FileReader();

          reader.onload = (event) => {
            let fileInBase64Format = event.target.result;
            fileInBase64Format = fileInBase64Format.split("base64,")[1]; // Extract Base64 content

            this.onFileChange(
              {
                fileContent: fileContent,
                base64: fileInBase64Format,
                formattedName: formattedFileName,
              },
              false
            );
          };

          reader.readAsDataURL(fileContent); // Read file content as Base64
        }
      }
    },
    onFileChange(fileObj, updateResume = true) {
      this.fileContent =
        fileObj && fileObj.fileContent ? fileObj.fileContent : null;
      this.fileInBase64Format = fileObj && fileObj.base64 ? fileObj.base64 : "";
      if (this.fileContent && this.fileInBase64Format) {
        this.editedDetails["Resume"] = fileObj?.formattedName
          ? fileObj.formattedName
          : "";
        this.editedDetails["Resume_File_Size"] = this.fileContent.size
          ? this.fileContent.size.toString()
          : "";
        this.isFileChanged = true;
        if (updateResume) this.getDetailsFromResume();
        else this.uploadFileContents("updateResume");
        this.openResumeModel = false;
      } else {
        this.removeFiles();
      }
    },
  },
};
</script>
<style scoped>
.google-auto-complete-address-field {
  width: 100% !important;
  border: none !important;
}
.google-auto-complete-address-field:focus {
  outline: none !important;
}
.v-btn--variant-elevated {
  background: rgb(var(--v-theme-primary));
  color: white;
  box-shadow: 0.3px 3px 3px grey;
}
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}

:deep(.ql-toolbar.ql-snow) {
  display: none !important;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
  border: none;
}
:deep(.ql-editor) {
  font-family: Roboto, sans-serif !important;
  white-space: nowrap !important;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
:deep(.ql-editor > p) {
  white-space: normal !important;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
