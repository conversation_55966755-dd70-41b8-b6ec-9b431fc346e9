<template>
  <div id="job-postings">
    <v-card
      v-if="linkedInStatus && linkedInStatus.toLowerCase() == 'active'"
      :style="isMobileView ? '' : 'width:500px'"
      class="rounded-lg mb-10 py-3"
    >
      <div class="mx-7 d-flex justify-space-between" style="height: 50px">
        <div class="d-flex" style="width: 200px">
          <img
            :src="linkedInLogo"
            style="width: 50px; height: auto"
            class=""
            :alt="name"
          />
          <v-card-title>LinkedIn</v-card-title>
        </div>
        <JobPostPublishModal
          :jobPostId="jobPostId"
          name="LinkedIn"
          :jobPostData="jobPostData"
        >
        </JobPostPublishModal>
      </div>
      <div style="background-color: rgb(247 247 247)" class="pa-1 my-2">
        <div
          bg-color="grey-lighten-2"
          class="ma-4 text-justify text-subtitle-1"
        >
          Directly publish the jobs from recruitment module to LinkedIn with
          this integration. This integration or publishing to LinkedIn is free.
        </div>
      </div>
    </v-card>
    <v-card
      v-if="jobStreetStatus && jobStreetStatus.toLowerCase() == 'active'"
      :style="isMobileView ? '' : 'width:500px'"
      class="rounded-lg mb-10 py-3"
    >
      <div class="mx-7 d-flex justify-space-between" style="height: 50px">
        <div class="d-flex" style="width: 200px">
          <img
            :src="jobStreetLogo"
            style="width: 50px; height: auto"
            class=""
            :alt="name"
          />
          <v-card-title>JobStreet</v-card-title>
        </div>
        <JobPostPublishModal
          :jobPostId="jobPostId"
          name="JobStreet"
          :jobPostData="jobPostData"
        >
        </JobPostPublishModal>
      </div>
      <div style="background-color: rgb(247 247 247)" class="pa-1 my-2">
        <div
          bg-color="grey-lighten-2"
          class="ma-4 text-justify text-subtitle-1"
        >
          Directly publish the jobs from recruitment module to Seek/JobStreet
          with this integration. This integration requires a registered account
          with Hirer ID activated.
        </div>
      </div>
    </v-card>
    <v-card
      v-if="indeedStatus && indeedStatus.toLowerCase() == 'active'"
      :style="isMobileView ? '' : 'width:500px'"
      class="rounded-lg mb-10 py-3"
    >
      <div class="mx-7 d-flex justify-space-between" style="height: 50px">
        <div class="d-flex" style="width: 200px">
          <img
            :src="indeedLogo"
            style="width: 50px; height: auto"
            class=""
            :alt="name"
          />
          <v-card-title>Indeed</v-card-title>
        </div>
        <JobPostPublishModal
          :jobPostId="jobPostId"
          name="Indeed"
          :jobPostData="jobPostData"
          :cityList="cityList"
          :stateList="stateList"
        >
        </JobPostPublishModal>
      </div>
      <div style="background-color: rgb(247 247 247)" class="pa-1 my-2">
        <div
          bg-color="grey-lighten-2"
          class="ma-4 text-justify text-subtitle-1"
        >
          Directly publish the jobs from recruitment module to Indeed with this
          integration. This integration or publishing to Indeed requires you to
          have an active account in Indeed.
        </div>
      </div>
    </v-card>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const JobPostPublishModal = defineAsyncComponent(() =>
  import("./recruitment/JobPostPublishModal.vue")
);
import linkedIn from "../../../assets/images/common/LinkedIn-Logo.png";
import jobStreet from "../../../assets/images/common/JobStreet-Logo.png";
import Indeed from "../../../assets/images/common/Indeed-Logo.webp";
import Irruka from "../../../assets/images/common/Irukka-Logo.png";
import { GET_INTEGRATION_STATUS } from "@/graphql/settings/irukka-integration/irukkaRegistrationQueries.js";
export default {
  data: () => {
    return {
      linkedInLogo: linkedIn,
      jobStreetLogo: jobStreet,
      indeedLogo: Indeed,
      irukkaLogo: Irruka,
      linkedInStatus: null,
      indeedStatus: null,
      jobStreetStatus: null,
      irukkaStatus: null,
      getIntegrationStatus: [],
    };
  },
  components: {
    JobPostPublishModal,
  },
  props: {
    jobPostId: {
      type: Number,
      required: true,
    },
    jobPostData: {
      type: Object,
      required: true,
    },
    cityList: {
      type: Array,
      required: false,
      default: () => {
        return [];
      },
    },
    stateList: {
      type: Array,
      required: false,
      default: () => {
        return [];
      },
    },
  },
  mounted() {
    this.fetchIntegrationStatus();
  },
  methods: {
    getAllIntegrationStatus() {
      this.linkedInStatus = this.getStatusByType("linkedin");
      this.jobStreetStatus = this.getStatusByType("seek");
      this.indeedStatus = this.getStatusByType("indeed");
      this.irukkaStatus = this.getStatusByType("irukka");
    },
    getStatusByType(integrationType) {
      // Find the object with the specified Integration_Type
      const integration = this.getIntegrationStatus.find(
        (item) =>
          item.Integration_Type.toLowerCase() === integrationType.toLowerCase()
      );

      // If the integration is found, return its Integration_Status
      if (integration) {
        return integration.Integration_Status;
      } else {
        return null;
      }
    },
    async fetchIntegrationStatus() {
      let vm = this;
      await vm.$apollo
        .query({
          query: GET_INTEGRATION_STATUS,
          variables: {
            form_Id: 15,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.jobBoardIntegrationStatus &&
            response.data.jobBoardIntegrationStatus.getStatus &&
            response.data.jobBoardIntegrationStatus.getStatus.length > 0
          ) {
            this.getIntegrationStatus =
              response.data.jobBoardIntegrationStatus.getStatus;
            this.getAllIntegrationStatus();
          }
        })
        .catch((err) => {
          vm.handleRetrieveIntegrationStatusError(err);
        });
    },
    handleRetrieveIntegrationStatusError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "integration status",
        isListError: false,
      });
    },
  },
};
</script>
<style scoped>
#job-postings {
  padding: 50px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
</style>
