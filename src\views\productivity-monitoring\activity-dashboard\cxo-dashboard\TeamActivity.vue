<template>
  <div>
    <v-row class="d-flex justify-end mt-3">
      <v-col
        md="12"
        cols="12"
        xs="12"
        sm="12"
        class="d-flex ml-md-4 align-center"
        :class="{
          'flex-column': isMobileView,
          'justify-center': windowWidth < 1264,
          'justify-end': windowWidth >= 1264,
        }"
        style="flex-wrap: wrap"
      >
        <v-btn
          class="bg-white my-2 ml-2"
          :style="'width: max-content'"
          :size="isMobileView ? 'small' : 'default'"
          rounded="lg"
          @click="$refs.datePicker.fp.open()"
        >
          <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
          <span class="text-caption px-1"
            >{{ $t("productivityMonitoring.workedDate") }}:</span
          >
          <flat-pickr
            ref="datePicker"
            v-model="appliedDateRange"
            :config="flatPickerOptions"
            :placeholder="$t('productivityMonitoring.selectDateRange')"
            class="ml-2 mt-1 date-range-picker-custom-bg"
            style="outline: 0px; color: var(--v-primary-base); width: 170px"
            @onChange="onChangeDateRange"
          ></flat-pickr>
        </v-btn>
        <v-menu
          v-model="customGroupMenu"
          :close-on-content-click="true"
          transition="scale-transition"
          offset-y
          min-width="290px"
        >
          <template v-slot:activator="{ props: activatorProps }">
            <v-btn
              class="white my-2 ml-2"
              rounded="lg"
              dense
              v-bind="activatorProps"
            >
              <span class="text-caption px-1"
                >{{ $t("productivityMonitoring.customGroupLabel") }}:</span
              >
              <span class="text-primary font-weight-bold"> </span>
              {{ selectedCustomGroup ? selectedCustomGroup : "-" }}
              <v-icon color="white" class="ml-1" size="17">{{
                customGroupMenu ? "fas fa-caret-up" : "fas fa-caret-down"
              }}</v-icon>
            </v-btn>
          </template>
          <div
            style="
              min-height: 100px;
              max-height: 300px;
              overflow-y: scroll;
              background-color: white;
            "
            class="white pa-2"
          >
            <div v-if="customGroupList && customGroupList.length > 0">
              <div
                v-for="customGroup in customGroupList"
                :key="customGroup.WorkSchedule_Id"
                @click="onSelectCustomGroup(customGroup)"
              >
                <v-hover>
                  <template v-slot:default="{ isHovering, props }">
                    <div
                      v-bind="props"
                      class="pa-2 my-2 rounded-lg cursor-pointer"
                      :class="{
                        'bg-hover':
                          isHovering &&
                          selectedCustomGroup !== customGroup.Custom_Group_Name,
                        'bg-primary text-white':
                          selectedCustomGroup === customGroup.Custom_Group_Name,
                        'bg-grey-lighten-4':
                          !isHovering &&
                          selectedCustomGroup !== customGroup.Custom_Group_Name,
                      }"
                    >
                      <div class="text-body-2">
                        {{ customGroup.Custom_Group_Name }}
                      </div>
                      <div
                        style="font-size: 12px"
                        class="pt-1"
                        :class="{
                          'text-white':
                            selectedCustomGroup ===
                            customGroup.Custom_Group_Name,
                          'text-primary text-lighten-3':
                            selectedCustomGroup !==
                            customGroup.Custom_Group_Name,
                        }"
                      ></div>
                    </div>
                  </template>
                </v-hover>
              </div>
            </div>
            <div
              v-else
              style="height: 100px"
              class="text-grey rounded-lg d-flex justify-center align-center"
            >
              {{ $t("productivityMonitoring.noDataAvailable") }}
            </div>
          </div>
        </v-menu>
        <v-menu
          id="activitytracker_my_activity_date_picker"
          v-model="workScheduleMenu"
          :close-on-content-click="true"
          transition="scale-transition"
          offset-y
          min-width="290px"
        >
          <template v-slot:activator="{ props: activatorProps }">
            <v-btn
              class="bg-white my-2 ml-2"
              rounded="lg"
              dense
              v-bind="activatorProps"
            >
              <v-icon color="primary" class="mr-1" size="17"
                >far fa-clock</v-icon
              >
              <span class="text-caption primary px-1"
                >{{ $t("productivityMonitoring.workSchedule") }}:</span
              >
              <span class="text-primary font-weight-bold">
                {{ employeeWorkSchedule ? employeeWorkSchedule : "-" }}
              </span>
              <v-icon color="primary" class="ml-1" size="17">{{
                workScheduleMenu ? "fas fa-caret-up" : "fas fa-caret-down"
              }}</v-icon>
            </v-btn>
          </template>
          <div
            style="
              min-height: 100px;
              max-height: 300px;
              overflow-y: scroll;
              background-color: white;
            "
            class="white pa-2"
          >
            <div v-if="workScheduleList && workScheduleList.length > 0">
              <div
                v-for="workSchedule in workScheduleList"
                :key="workSchedule.WorkSchedule_Id"
                @click="onSelectWorkSchedule(workSchedule)"
              >
                <v-hover>
                  <template v-slot:default="{ isHovering, props }">
                    <div
                      v-bind="props"
                      class="pa-2 my-2 rounded-lg cursor-pointer"
                      :class="{
                        'bg-hover':
                          isHovering &&
                          employeeWorkSchedule !==
                            workSchedule.WorkSchedule_Name,
                        'bg-primary text-white':
                          employeeWorkSchedule ===
                          workSchedule.WorkSchedule_Name,
                        'bg-grey-lighten-4 text-primary':
                          !isHovering &&
                          employeeWorkSchedule !==
                            workSchedule.WorkSchedule_Name,
                      }"
                    >
                      <div class="text-body-2">
                        {{ workSchedule.WorkSchedule_Name }}
                      </div>
                      <div
                        style="font-size: 12px"
                        class="pt-1"
                        :class="{
                          'text-white':
                            employeeWorkSchedule ===
                            workSchedule.WorkSchedule_Name,
                          'text-primary text-lighten-3':
                            employeeWorkSchedule !==
                            workSchedule.WorkSchedule_Name,
                        }"
                      >
                        {{ workSchedule.Time_Zone }}
                      </div>
                    </div>
                  </template>
                </v-hover>
              </div>
            </div>
            <div
              v-else
              style="height: 100px"
              class="text-grey rounded-lg d-flex justify-center align-center"
            >
              {{ $t("productivityMonitoring.noDataAvailable") }}
            </div>
          </div>
        </v-menu>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" md="6">
        <div v-if="chartsLoading" style="height: 100%" class="mt-2">
          <v-row>
            <v-skeleton-loader type="article" width="100%"></v-skeleton-loader>
          </v-row>
          <v-row>
            <v-skeleton-loader
              type="article, actions"
              width="100%"
            ></v-skeleton-loader>
          </v-row>
        </div>
        <v-card v-else height="400" class="pa-3 rounded-lg">
          <div
            class="d-flex justify-space-between"
            :class="windowWidth < 550 ? 'flex-column' : ''"
          >
            <div>
              <p class="text-h6 font-weight-bold mr-5">
                {{ $t("productivityMonitoring.productivityByLocation") }}
              </p>
            </div>

            <div style="width: 200px" class="mr-8">
              <CustomSelect
                :items="chartTypeList"
                :item-selected="chartType"
                density="compact"
                @selected-item="onSelectItem($event, 'chartType')"
              ></CustomSelect>
            </div>
          </div>
          <apex-chart
            v-if="productivitySeries[0].data.length > 0"
            type="bar"
            height="300"
            :options="productivityOptions"
            :series="productivitySeries"
          ></apex-chart>
        </v-card>
      </v-col>
      <v-col cols="12" md="6">
        <div v-if="chartsLoading" style="height: 100%" class="mt-2">
          <v-row>
            <v-skeleton-loader type="article" width="100%"></v-skeleton-loader>
          </v-row>
          <v-row>
            <v-skeleton-loader
              type="article, actions"
              width="100%"
            ></v-skeleton-loader>
          </v-row>
        </div>
        <v-card v-else height="400" class="pa-3 rounded-lg">
          <div
            class="d-flex justify-space-between"
            :class="windowWidth < 550 ? 'flex-column' : ''"
          >
            <div>
              <p class="text-h6 font-weight-bold mr-5">
                {{ $t("productivityMonitoring.avgStartEndTimeByLocation") }}
              </p>
            </div>
          </div>
          <apexChart
            v-if="
              avgTimeSeries &&
              avgTimeSeries.length > 0 &&
              avgTimeSeries[0].data &&
              avgTimeSeries[0].data.length > 0
            "
            type="rangeBar"
            height="300"
            :options="avgTimeOptions"
            :series="toRaw(avgTimeSeries)"
          ></apexChart>
        </v-card>
      </v-col>
      <v-col cols="12" md="6">
        <div v-if="chartsLoading" style="height: 100%" class="mt-2">
          <v-row>
            <v-skeleton-loader type="article" width="100%"></v-skeleton-loader>
          </v-row>
          <v-row>
            <v-skeleton-loader
              type="article, actions"
              width="100%"
            ></v-skeleton-loader>
          </v-row>
        </div>
        <v-card v-else height="500" class="pa-3 rounded-lg">
          <div
            class="d-flex justify-space-between"
            :class="windowWidth < 550 ? 'flex-column' : ''"
          >
            <div>
              <p class="text-h6 font-weight-bold mr-5">
                {{ $t("productivityMonitoring.locationBreakdown") }}
              </p>
            </div>
          </div>
          <apexChart
            v-if="locationBreakdownSeries.length > 0 && showPieChart"
            type="pie"
            height="400"
            :options="locationBreakdownOptions"
            :series="locationBreakdownSeries"
            class="mt-8"
          ></apexChart>
          <div v-if="!showPieChart" class="text-h6 text-center">
            {{ $t("productivityMonitoring.noDataAvailable") }}
          </div>
        </v-card>
      </v-col>
      <v-col cols="12" md="6">
        <div v-if="locationLoading" style="height: 100%" class="mt-2">
          <v-row>
            <v-skeleton-loader type="article" width="100%"></v-skeleton-loader>
          </v-row>
          <v-row>
            <v-skeleton-loader
              type="article, actions"
              width="100%"
            ></v-skeleton-loader>
          </v-row>
        </div>
        <v-card v-else height="500" class="pa-3 rounded-lg">
          <div
            class="d-flex justify-space-between"
            :class="windowWidth < 550 ? 'flex-column' : ''"
          >
            <div>
              <p class="text-h6 font-weight-bold mr-5">
                {{ $t("productivityMonitoring.locationBreakdownByWeek") }}
              </p>
            </div>
          </div>
          <apexChart
            v-if="locationByWeekSeries.length > 0"
            type="bar"
            height="400"
            :options="locationByWeekOptions"
            :series="locationByWeekSeries"
            class="mt-8"
          >
          </apexChart>
        </v-card>
      </v-col>
      <v-col></v-col>
    </v-row>
  </div>
</template>
<script>
import moment from "moment";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import VueApexCharts from "vue3-apexcharts";
import { LIST_WORK_SCHEDULE } from "@/graphql/productivity-monitoring/activityDashboardQueries";
import {
  RETRIEVE_LOCATION_DATA_LIST,
  RETRIEVE_WEEK_WISE_LIST,
} from "@/graphql/productivity-monitoring/locationInsightsQueries";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { toRaw } from "vue";
export default {
  name: "TeamActivity",
  components: { flatPickr, CustomSelect, ApexChart: VueApexCharts },
  data() {
    return {
      appliedDateRange: null,
      workScheduleMenu: false,
      customGroupMenu: false,
      employeeWorkSchedule: "",
      selectedCustomGroup: "All",
      startDate: "",
      endDate: "",
      currentDateRange: "",
      isExceed31Days: false,
      customGroupList: [],
      workScheduleList: [],
      employeeWorkScheduleId: 0,
      customGroupId: 0,
      chartsLoading: false,
      locationLoading: false,
      chartTypeList: [
        this.$t("productivityMonitoring.systemUpTimeLabel"),
        this.$t("productivityMonitoring.activeTimeLabel"),
        this.$t("productivityMonitoring.computerActivityLabel"),
        this.$t("productivityMonitoring.productiveTimeLabel"),
      ],
      chartType: this.$t("productivityMonitoring.activeTimeLabel"),
      chartData: null,
      locationBreakdownOptions: {
        chart: {
          type: "pie",
          height: 300,
          width: 300,
        },
        noData: {
          text: this.$t("productivityMonitoring.noDataAvailable"),
          align: "center",
          verticalAlign: "middle",
          offsetX: 0,
          offsetY: 0,
          style: {
            fontSize: "14px",
          },
        },
        tooltip: {
          custom: function ({ seriesIndex, w }) {
            const label = w.globals.labels[seriesIndex];
            const color = w.globals.colors[seriesIndex];
            const percentage =
              w.globals.seriesPercent[seriesIndex][0].toFixed(2);
            return `<div style="padding: 5px; background: ${color}; font-size: 12px">
                      <strong>${label}: ${percentage}%</strong>
                    </div>`;
          },
        },
        labels: [
          this.$t("productivityMonitoring.officeRemote"),
          this.$t("productivityMonitoring.office"),
          this.$t("productivityMonitoring.remote"),
          this.$t("productivityMonitoring.offline"),
        ],
        responsive: [
          {
            breakpoint: 480,
            options: {
              chart: {
                width: 200,
              },
              legend: {
                position: "bottom",
              },
            },
          },
        ],
      },
      locationBreakdownSeries: [],
      productivityOptions: {
        chart: {
          type: "bar",
          height: 350,
          toolbar: {
            show: false,
          },
          zoom: {
            enabled: false,
          },
        },
        xaxis: {
          categories: [
            this.$t("productivityMonitoring.officeRemote"),
            this.$t("productivityMonitoring.office"),
            this.$t("productivityMonitoring.remote"),
          ],
        },
        noData: {
          text: this.$t("productivityMonitoring.noDataAvailable"),
          align: "center",
          verticalAlign: "middle",
          offsetX: 0,
          offsetY: 0,
          style: {
            fontSize: "14px",
          },
        },
        yaxis: {
          title: {
            text: this.$t("productivityMonitoring.avgTimeWorkedPerDay"),
            style: {
              fontFamily: "Roboto, sans-sarif",
              fontSize: "12px",
              color: "black",
            },
          },
        },
        dataLabels: {
          enabled: true,
          formatter: (val) => {
            if (val) {
              val = val.toFixed(2).toString();
              return `${val.split(".")[0]}h ${val.split(".")[1]}m`;
            }
            return val;
          },
        },
        tooltip: {
          enabled: true, // Enable the tooltip
          y: {
            formatter: function (val) {
              if (val) {
                val = val.toFixed(2).toString();
                return `${val.split(".")[0]}h ${val.split(".")[1]}m`;
              }
              return val;
            },
          },
        },
      },
      productivitySeries: [
        {
          name: this.$t("productivityMonitoring.activeTime"),
          data: [],
        },
      ],
      avgTimeOptions: {
        chart: {
          height: 300,
          type: "rangeBar",
          toolbar: {
            show: false,
          },
          zoom: {
            enabled: false,
          },
        },
        plotOptions: {
          bar: {
            horizontal: true,
            barHeight: "30%",
            borderRadius: 10,
          },
        },
        xaxis: {
          min: 0,
          type: "datetime",
          categories: [
            this.$t("productivityMonitoring.officeRemote"),
            this.$t("productivityMonitoring.office"),
            this.$t("productivityMonitoring.remote"),
          ],
          stepSize: 2,
          title: {
            text: this.$t("productivityMonitoring.timeLabel"),
            style: {
              fontFamily: "Roboto, sans-sarif",
              fontSize: "12px",
              color: "black",
            },
          },
          labels: {
            formatter: (val) => {
              if (val >= 0 && val < 12) {
                return val + "AM";
              } else {
                return val + "PM";
              }
            },
          },
        },
        responsive: [
          {
            breakpoint: 480,
            options: {
              chart: {
                width: 200,
              },
              legend: {
                position: "bottom",
              },
            },
          },
        ],
        tooltip: {
          enabled: true, // Enable tooltips
          custom: ({ seriesIndex, dataPointIndex, w }) => {
            const data =
              w.globals.seriesRange[seriesIndex][dataPointIndex].y[0];
            const start = data["y1"] + " AM"; // Custom formatting for start date
            const end = data["y2"] + " PM"; // Custom formatting for end date
            return `<div style="padding: 10px;">
                      <strong>${w.globals.labels[dataPointIndex]}</strong><br/>
                      ${this.$t(
                        "productivityMonitoring.startLabel"
                      )}: ${start}<br/>
                      ${this.$t("productivityMonitoring.endLabel")}: ${end}
                    </div>`;
          },
        },
      },
      avgTimeSeries: [
        {
          name: this.$t("productivityMonitoring.avgStartEndTime"),
          data: [
            {
              x: this.$t("productivityMonitoring.officeRemote"),
              y: [],
            },
            {
              x: this.$t("productivityMonitoring.office"),
              y: [],
            },
            {
              x: this.$t("productivityMonitoring.remote"),
              y: [],
            },
          ],
        },
      ],
      locationByWeekSeries: [],
      locationByWeekOptions: {
        chart: {
          type: "bar",
          height: 350,
          stacked: true,
          name: "Location By Week",
          toolbar: {
            show: false,
          },
          zoom: {
            enabled: false,
          },
        },
        responsive: [
          {
            breakpoint: 480,
            options: {
              legend: {
                position: "bottom",
                offsetX: -10,
                offsetY: 0,
              },
            },
          },
        ],
        plotOptions: {
          bar: {
            horizontal: false,
            borderRadius: 0,
            borderRadiusApplication: "end", // 'around', 'end'
            borderRadiusWhenStacked: "last", // 'all', 'last'
            dataLabels: {
              total: {
                enabled: true,
                formatter: (val) => {
                  return val.toFixed(2) + "%";
                },
                style: {
                  fontSize: "13px",
                  fontWeight: 900,
                },
              },
            },
          },
        },
        xaxis: {
          type: "category",
          categories: [],
          title: {
            text: this.$t("productivityMonitoring.weeksLabel"),
            style: {
              fontFamily: "Roboto, sans-sarif",
              fontSize: "12px",
              color: "black",
            },
          },
        },
        legend: {
          position: "right",
          offsetY: 40,
        },
        fill: {
          opacity: 1,
        },
        yaxis: {
          title: {
            text: this.$t("productivityMonitoring.percentageLabel"),
          },
        },
        tooltip: {
          enabled: true, // Enable the tooltip
          y: {
            formatter: function (val) {
              if (val) {
                val = val.toFixed(2).toString();
                return `${val}%`;
              }
              return val;
            },
          },
        },
      },
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      // Get the current date
      const currentDate = moment().toDate();
      return {
        mode: "range",
        dateFormat: orgDateFormat,
        maxDate: moment().format(this.$store.state.orgDetails.orgDateFormat),
        // Disable the current date
        disable: [currentDate],
      };
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    showPieChart() {
      if (
        this.locationBreakdownSeries &&
        this.locationBreakdownSeries.length > 0 &&
        this.locationBreakdownSeries[0] == 0 &&
        this.locationBreakdownSeries[1] == 0 &&
        this.locationBreakdownSeries[2] == 0 &&
        this.locationBreakdownSeries[3] == 0
      ) {
        return false;
      }
      return true;
    },
  },
  mounted() {
    this.getCurrentDateRange();
    this.retrieveCustomGroups();
    this.fetchWorkScheduleList();
    this.fetchEmpWorkScheduleDetails();
    this.fetchChartDetails();
    this.getWeekWiseList();
  },
  errorCaptured(err) {
    console.error("An error occurred:", err);
  },
  watch: {
    appliedDateRange() {
      if (this.isExceed31Days) {
        this.appliedDateRange = this.currentDateRange;
        this.isExceed31Days = false;
      }
    },
  },
  methods: {
    toRaw,
    onChangeDateRange(selectedDates, dateStr) {
      this.isExceed31Days = false;
      if (selectedDates.length > 1) {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        const startMoment = moment(selectedDates[0], orgDateFormat);
        const endMoment =
          selectedDates.length > 1
            ? moment(selectedDates[1], orgDateFormat)
            : moment(selectedDates[0], orgDateFormat);
        const diffInDays = endMoment.diff(startMoment, "days") + 1; // Adding 1 to include both the start and end dates
        if (diffInDays <= 31) {
          // Parse the dates from the given format
          let parsedStartDate = moment(selectedDates[0], "DD/MM/YYYY");
          let parsedEndDate = moment(
            selectedDates.length > 1 ? selectedDates[1] : selectedDates[0],
            "DD/MM/YYYY"
          );

          // Format the dates into "YYYY-MM-DD" format
          this.startDate = parsedStartDate.format("YYYY-MM-DD");
          this.endDate = parsedEndDate.format("YYYY-MM-DD");
          this.fetchChartDetails();
          this.getWeekWiseList();
        } else {
          this.isExceed31Days = true;
        }
      } else {
        this.$emit("on-change-date-range", dateStr);
      }
      if (this.isExceed31Days) {
        this.appliedDateRange = this.currentDateRange;
        let snackbarData = {
          isOpen: true,
          message: this.$t(
            "productivityMonitoring.selectDateRangeLessThan31Days"
          ),
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    getCurrentDateRange() {
      // Get current month's 1st date
      const firstDayOfMonth = moment().startOf("month").format("DD/MM/YYYY");
      // Get yesterday's date
      const yesterdayDate = moment().subtract(1, "day").format("DD/MM/YYYY");
      // Parse the dates from the given format
      let parsedStartDate = moment(firstDayOfMonth, "DD/MM/YYYY");
      let parsedEndDate = moment(yesterdayDate, "DD/MM/YYYY");
      const differenceInDays = parsedEndDate.diff(parsedStartDate, "days");
      if (differenceInDays > 0) {
        // Format the dates into "YYYY-MM-DD" format
        this.startDate = parsedStartDate.format("YYYY-MM-DD");
        this.endDate = parsedEndDate.format("YYYY-MM-DD");
        this.currentDateRange = firstDayOfMonth + " to " + yesterdayDate;
      } else {
        this.startDate = parsedEndDate.format("YYYY-MM-DD");
        this.endDate = parsedEndDate.format("YYYY-MM-DD");
        this.currentDateRange = yesterdayDate + " to " + yesterdayDate;
      }
      this.appliedDateRange = this.currentDateRange;
    },
    retrieveCustomGroups() {
      this.$store
        .dispatch("listCustomGroupBasedOnFormName", {
          formName: "productivityMonitoring",
        })
        .then((groupList) => {
          if (groupList && groupList.length) {
            const customGroups = groupList;
            let CGList = [
              {
                Custom_Group_Name: "All",
                Custom_Group_Id: 0,
              },
            ];
            this.customGroupList = CGList.concat(customGroups);
          } else {
            this.customGroupList = [];
          }
        })
        .catch((err) => {
          console.error("Error", err);
          this.customGroupList = [];
        });
    },
    fetchWorkScheduleList() {
      let vm = this;
      vm.$apollo
        .query({
          query: LIST_WORK_SCHEDULE,
          client: "apolloClientC",
          variables: {
            formName: "cxo-dashboard",
          },
        })
        .then((response) => {
          const { errorCode, workSchedule } = response.data.listWorkSchedule;
          if (!errorCode && workSchedule) {
            let WSList = [
              {
                WorkSchedule_Name: "All",
                WorkSchedule_Id: 0,
                Time_Zone: "",
              },
            ];
            vm.workScheduleList = WSList.concat(workSchedule);
          } else {
            vm.handleWorkScheduleListErr();
          }
        })
        .catch((err) => {
          console.error("Error", err);
          vm.handleWorkScheduleListErr();
        });
    },

    handleWorkScheduleListErr() {
      this.workScheduleList = [];
    },

    onSelectCustomGroup(customGroup) {
      this.customGroupId = customGroup.Custom_Group_Id;
      this.selectedCustomGroup = customGroup.Custom_Group_Name;
      this.fetchChartDetails();
      this.getWeekWiseList();
    },

    onSelectWorkSchedule(workSchedule) {
      this.employeeWorkScheduleId = workSchedule.WorkSchedule_Id;
      this.employeeWorkSchedule = workSchedule.WorkSchedule_Name;
      this.fetchChartDetails();
      this.getWeekWiseList();
      if (workSchedule.WorkSchedule_Name === "All") {
        this.employeeTimezone = this.loginEmployeeWSDetails.timeZone;
      } else {
        this.employeeTimezone = workSchedule.TimeZone_Id;
      }
    },

    fetchEmpWorkScheduleDetails() {
      let vm = this;
      try {
        this.$store
          .dispatch("getEmpWorkScheduleDetails", {
            employeeId: vm.loginEmployeeId,
          })
          .then((employeeWorkScheduleDetails) => {
            const { workSchedule, workScheduleId, timeZone } =
              employeeWorkScheduleDetails;
            vm.employeeWorkScheduleId = workScheduleId;
            vm.employeeWorkSchedule = workSchedule;
            vm.employeeTimezone = timeZone;
            vm.loginEmployeeWSDetails = employeeWorkScheduleDetails;
          })
          .catch((err) => {
            console.error("Error", err);
            vm.handleEmpWorkScheduleError();
          });
      } catch {
        vm.handleEmpWorkScheduleError();
      }
    },

    // set default value((1) when we get error while retrieving employee's work-schedule details
    handleEmpWorkScheduleError() {
      this.employeeWorkScheduleId = 1;
      this.employeeTimezone = "";
    },
    onSelectItem(value, field) {
      this[field] = value;
      this.productivitySeries[0].name = value;
      const systemUpTimeLabel = this.$t(
        "productivityMonitoring.systemUpTimeLabel"
      );
      const activeTimeLabel = this.$t("productivityMonitoring.activeTimeLabel");
      const computerActivityLabel = this.$t(
        "productivityMonitoring.computerActivityLabel"
      );
      const productiveTimeLabel = this.$t(
        "productivityMonitoring.productiveTimeLabel"
      );

      if (value == systemUpTimeLabel) {
        this.productivitySeries[0].data[0] = this.chartData
          ?.averageOfficeRemoteSystemUpTime
          ? this.convertToHrs(this.chartData.averageOfficeRemoteSystemUpTime)
          : 0;
        this.productivitySeries[0].data[1] = this.chartData
          ?.averageOfficeSystemUpTime
          ? this.convertToHrs(this.chartData.averageOfficeSystemUpTime)
          : 0;
        this.productivitySeries[0].data[2] = this.chartData
          ?.averageRemoteSystemUpTime
          ? this.convertToHrs(this.chartData.averageRemoteSystemUpTime)
          : 0;
      } else if (value == activeTimeLabel) {
        this.productivitySeries[0].data[0] = this.chartData
          .averageOfficeRemoteActiveTime
          ? this.convertToHrs(this.chartData.averageOfficeRemoteActiveTime)
          : 0;
        this.productivitySeries[0].data[1] = this.chartData
          .averageOfficeActiveTime
          ? this.convertToHrs(this.chartData.averageOfficeActiveTime)
          : 0;
        this.productivitySeries[0].data[2] = this.chartData
          .averageRemoteActiveTime
          ? this.convertToHrs(this.chartData.averageRemoteActiveTime)
          : 0;
      } else if (value == computerActivityLabel) {
        this.productivitySeries[0].data[0] = this.chartData
          .averageOfficeRemoteComputerActivityTime
          ? this.convertToHrs(
              this.chartData.averageOfficeRemoteComputerActivityTime
            )
          : 0;
        this.productivitySeries[0].data[1] = this.chartData
          .averageOfficeComputerActivityTime
          ? this.convertToHrs(this.chartData.averageOfficeComputerActivityTime)
          : 0;
        this.productivitySeries[0].data[2] = this.chartData
          .averageRemoteComputerActivityTime
          ? this.convertToHrs(this.chartData.averageRemoteComputerActivityTime)
          : 0;
      } else if (value == productiveTimeLabel) {
        this.productivitySeries[0].data[0] = this.chartData
          .averageOfficeRemoteProductiveAppsAndUrls
          ? this.convertToHrs(
              this.chartData.averageOfficeRemoteProductiveAppsAndUrls
            )
          : 0;
        this.productivitySeries[0].data[1] = this.chartData
          .averageOfficeProductiveAppsAndUrls
          ? this.convertToHrs(this.chartData.averageOfficeProductiveAppsAndUrls)
          : 0;
        this.productivitySeries[0].data[2] = this.chartData
          .averageRemoteProductiveAppsAndUrls
          ? this.convertToHrs(this.chartData.averageRemoteProductiveAppsAndUrls)
          : 0;
      }
    },
    fetchChartDetails() {
      let vm = this;
      vm.chartsLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_LOCATION_DATA_LIST,
          client: "apolloClientK",
          variables: {
            startDate: vm.startDate,
            endDate: vm.endDate,
            workScheduleId: vm.employeeWorkScheduleId,
            customGroupId: vm.customGroupId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          let data = response.data;
          let locationSeries = [];
          let productiveSeries = [];
          let avgChart = [];
          if (
            data &&
            data.getLocationWiseActivtyData &&
            data.getLocationWiseActivtyData.locationWiseActivityDetails
          ) {
            vm.chartData =
              data.getLocationWiseActivtyData.locationWiseActivityDetails;
            locationSeries[0] = vm.chartData.percentageOfficeRemoteRecords
              ? +vm.chartData.percentageOfficeRemoteRecords.toFixed(2)
              : 0;
            locationSeries[1] = vm.chartData.percentageOfficeRecords
              ? +vm.chartData.percentageOfficeRecords.toFixed(2)
              : 0;
            locationSeries[2] = vm.chartData.percentageRemoteRecords
              ? +vm.chartData.percentageRemoteRecords.toFixed(2)
              : 0;
            locationSeries[3] = vm.chartData.percentageOffilineRecords
              ? +vm.chartData.percentageOffilineRecords.toFixed(2)
              : 0;
            productiveSeries[0] = vm.chartData.averageOfficeRemoteActiveTime
              ? vm.convertToHrs(vm.chartData.averageOfficeRemoteActiveTime)
              : 0;
            productiveSeries[1] = vm.chartData.averageOfficeActiveTime
              ? vm.convertToHrs(vm.chartData.averageOfficeActiveTime)
              : 0;
            productiveSeries[2] = vm.chartData.averageRemoteActiveTime
              ? vm.convertToHrs(vm.chartData.averageRemoteActiveTime)
              : 0;
            avgChart[0] = {
              x: "Office/Remote",
              y: [
                vm.chartData.averageOfficeRemoteStartTime
                  ? parseInt(
                      vm.convertToHrs(vm.chartData.averageOfficeRemoteStartTime)
                    )
                  : 0,
                vm.chartData.averageOfficeRemoteEndTime
                  ? parseInt(
                      vm.convertToHrs(vm.chartData.averageOfficeRemoteEndTime)
                    )
                  : 0,
              ],
            };
            avgChart[1] = {
              x: "Office",
              y: [
                vm.chartData.averageOfficeStartTime
                  ? parseInt(
                      vm.convertToHrs(vm.chartData.averageOfficeStartTime)
                    )
                  : 0,
                vm.chartData.averageOfficeEndTime
                  ? parseInt(vm.convertToHrs(vm.chartData.averageOfficeEndTime))
                  : 0,
              ],
            };
            avgChart[2] = {
              x: "Remote",
              y: [
                vm.chartData.averageRemoteStartTime
                  ? parseInt(
                      vm.convertToHrs(vm.chartData.averageRemoteStartTime)
                    )
                  : 0,
                vm.chartData.averageRemoteEndTime
                  ? parseInt(vm.convertToHrs(vm.chartData.averageRemoteEndTime))
                  : 0,
              ],
            };
          }
          vm.productivitySeries[0].data = productiveSeries;
          vm.locationBreakdownSeries = locationSeries;
          vm.avgTimeSeries = [
            { name: "Avg Start and End Time", data: avgChart },
          ];
          vm.chartsLoading = false;
        })
        .catch((err) => {
          console.log("Error", err);
          vm.chartsLoading = false;
        });
    },
    convertToHrs(val) {
      let hours = Math.floor(val / 60);
      let minutes = val % 60;
      minutes = minutes < 10 ? "0" + minutes : minutes;
      return parseFloat(`${hours}.${minutes}`).toFixed(2);
    },
    getWeekWiseList() {
      let vm = this;
      vm.locationLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_WEEK_WISE_LIST,
          client: "apolloClientK",
          variables: {
            startDate: this.startDate,
            endDate: this.endDate,
            workScheduleId: this.employeeWorkScheduleId,
            customGroupId: this.customGroupId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.getWeekWiseWorkLocation &&
            data.getWeekWiseWorkLocation.weekWiseWorkLocationDetails &&
            data.getWeekWiseWorkLocation.weekWiseWorkLocationDetails.length > 0
          ) {
            let weekData = JSON.parse(
              data.getWeekWiseWorkLocation.weekWiseWorkLocationDetails
            );
            let categories = [],
              series1 = [],
              series2 = [],
              series3 = [];
            weekData.map((item) => {
              categories.push(Object.keys(item)[0]);
              series1.push(
                item[
                  Object.keys(item)[0]
                ].percentageOfficeRecordsWeekBased.toFixed(2)
              );
              series2.push(
                item[
                  Object.keys(item)[0]
                ].percentageRemoteRecordsWeekBased.toFixed(2)
              );
              series3.push(
                item[
                  Object.keys(item)[0]
                ].percentageOfficeRemoteRecordsWeekBased.toFixed(2)
              );
            });
            this.locationByWeekOptions = {
              ...this.locationByWeekOptions,
              ...{
                xaxis: {
                  categories: categories,
                },
              },
            };
            this.locationByWeekSeries = [
              { name: "Office", data: series1 },
              { name: "Remote", data: series2 },
              { name: "Office/Remote", data: series3 },
            ];
          }
          vm.locationLoading = false;
        })
        .catch((err) => {
          console.error("error", err);
          vm.locationLoading = false;
        });
    },
  },
};
</script>
<style scoped>
.topPosition {
  position: absolute;
  top: 0;
  right: 0;
}
::v-deep .v-switch__track {
  height: 8px;
  width: 15px;
}
::v-deep .v-switch__thumb {
  height: 13px;
  width: 13px;
}
</style>
