<template>
  <div>
    <div v-if="mainTabs?.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row v-if="originalList?.length" justify="center">
            <v-col
              cols="12"
              md="9"
              class="d-flex justify-end"
              v-if="!showAddEditForm && !showViewForm"
            >
              <EmployeeDefaultFilterMenu
                class="d-flex justify-end mr-8"
                :isFilter="false"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="custom-fields-container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view && isAdmin">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="originalList?.length === 0"
            key="no-data-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList?.length === 0"
            :image-name="originalList?.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row style="background: white" class="rounded-lg pa-5 mb-4">
                  <v-col cols="12">
                    <NotesCard
                      notes="The Custom Fields system empowers administrators to tailor forms with diverse field types to meet specific organizational needs. This flexibility allows organizations to customize forms for different purposes, improving data collection efficiency and ensuring that the right information is captured for each scenario. With customizable visibility and required field settings, the system adapts to various use cases without compromising on security or user experience."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                    <NotesCard
                      notes="With the ability to create custom fields for each form, organizations can ensure that forms are aligned with specific requirements, whether it’s for onboarding, compliance, or any other operational need. This approach supports organizations in managing forms for different services, and departments, allowing them to easily modify field labels, options, and settings based on local regulations and requirements. The system ensures that only admins have the authority to make changes, providing a controlled environment that safeguards the integrity of the data while allowing for tailored experiences across different contexts."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="formAccess?.add"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="openAddForm()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      Add
                    </v-btn>
                    <v-btn
                      v-if="originalList?.length === 0"
                      color="transparent"
                      rounded="lg"
                      variant="flat"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0"
            key="no-results-screen"
            main-title="There are no candidates matched for the selected filters/searches."
            image-name="common/no-records"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="windowWidth <= 960 ? 'small' : 'default'"
                      @click="resetFilter('grid')"
                    >
                      <span class="primary">Reset Filter/Search </span>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <div v-else>
            <div>
              <div
                v-if="originalList?.length"
                class="d-flex flex-wrap align-center my-3"
                :class="isMobileView ? 'flex-column' : ''"
                style="justify-content: space-between"
              >
                <div
                  class="d-flex align-center"
                  :class="isMobileView ? 'justify-center' : ''"
                >
                  <CustomSelect
                    v-model="selectedForm"
                    :items="listForms"
                    label="Forms"
                    :is-loading="dropdownLoading"
                    :isAutoComplete="true"
                    clearable
                    item-title="customFormName"
                    item-value="formId"
                    min-width="200px"
                    density="comfortable"
                    :itemSelected="selectedForm"
                    @selected-item="selectedForm = $event"
                    @update:modelValue="filteredItemList()"
                  />
                </div>

                <div
                  class="d-flex align-center"
                  :class="isMobileView ? 'justify-center' : 'justify-end'"
                >
                  <v-btn
                    v-if="formAccess?.add"
                    prepend-icon="fas fa-plus"
                    color="primary"
                    variant="elevated"
                    rounded="lg"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="openAddForm()"
                  >
                    <template v-slot:prepend>
                      <v-icon></v-icon>
                    </template>
                    Add
                  </v-btn>
                  <v-btn
                    rounded="lg"
                    color="transparent"
                    variant="flat"
                    class="mt-1"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchList('Refetch List')"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                  <v-menu v-model="openMoreMenu" transition="scale-transition">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        variant="plain"
                        class="mt-1 ml-n1 mr-n5"
                        v-bind="props"
                      >
                        <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                        <v-icon v-else>fas fa-caret-up</v-icon>
                      </v-btn>
                    </template>
                    <v-list>
                      <v-list-item
                        v-for="action in moreActions"
                        :key="action"
                        @click="onMoreAction(action)"
                      >
                        <v-hover>
                          <template v-slot:default="{ isHovering, props }">
                            <v-list-item-title
                              v-bind="props"
                              class="pa-3"
                              :class="{
                                'pink-lighten-5': isHovering,
                              }"
                              ><v-icon size="15" class="pr-2">{{
                                action.icon
                              }}</v-icon
                              >{{ action.key }}</v-list-item-title
                            >
                          </template>
                        </v-hover>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
              </div>

              <v-row>
                <v-col v-if="originalList?.length" class="mb-12">
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView ? ' v-data-table__mobile-table-row' : ''
                        "
                      >
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Field
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.Custom_Field_Id ===
                                  item.Custom_Field_Id
                              "
                              class="data-table-side-border d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.Custom_Field_Name"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.Custom_Field_Name?.length > 50
                                      ? props
                                      : ''
                                  "
                                >
                                  {{ checkNullValue(item.Custom_Field_Name) }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Field Type
                          </div>
                          <v-tooltip
                            :text="item.Custom_Field_Type"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.Custom_Field_Type?.length > 100
                                    ? props
                                    : ''
                                "
                                :style="
                                  !isMobileView
                                    ? 'max-width: 500px; '
                                    : 'max-width: 200px; '
                                "
                              >
                                {{ checkNullValue(item.Custom_Field_Type) }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <td
                          v-if="!selectedForm"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Forms
                          </div>
                          <div class="d-flex align-center">
                            <section
                              class="text-subtitle-1 font-weight-regular text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 500px;'
                                  : 'max-width: 200px;'
                              "
                            >
                              {{ checkNullValue(item.FormIds[0]?.Form_Name) }}
                            </section>
                            <div v-if="item.FormIds?.length > 1" class="ml-n2">
                              <v-tooltip
                                :text="getAllFormNames(item.FormIds)"
                                location="top"
                                max-width="300"
                              >
                                <template v-slot:activator="{ props }">
                                  <span
                                    v-if="item.FormIds?.length"
                                    variant="text"
                                    small
                                    v-bind="props"
                                    class="text-primary ml-4"
                                  >
                                    +{{ item.FormIds?.length - 1 }}
                                  </span>
                                </template>
                              </v-tooltip>
                            </div>
                          </div>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'width: max-content'
                          "
                          :cols="2"
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Who Can See
                          </div>
                          <div class="d-flex align-center">
                            <section
                              class="text-subtitle-1 font-weight-regular text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 500px;'
                                  : 'max-width: 200px;'
                              "
                            >
                              {{ checkNullValue(item.Roles_Id[0]?.Roles_Name) }}
                            </section>
                            <div v-if="item.Roles_Id?.length > 1" class="ml-n2">
                              <v-tooltip
                                :text="getAllRoleNames(item.Roles_Id)"
                                location="top"
                                max-width="400"
                              >
                                <template v-slot:activator="{ props }">
                                  <span
                                    v-if="item.Roles_Id?.length"
                                    variant="text"
                                    small
                                    v-bind="props"
                                    class="text-primary ml-4"
                                  >
                                    +{{ item.Roles_Id?.length - 1 }}
                                  </span>
                                </template>
                              </v-tooltip>
                            </div>
                          </div>
                        </td>
                        <td
                          v-if="selectedForm"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'width: max-content'
                          "
                          :cols="2"
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Required
                          </div>
                          <v-tooltip
                            :text="item.Mandatory"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.Mandatory?.length > 100 ? props : ''
                                "
                                :style="
                                  !isMobileView
                                    ? 'max-width: 500px; '
                                    : 'max-width: 200px; '
                                "
                              >
                                {{
                                  checkNullValue(
                                    checkMandatoryValue(item.FormIds)
                                  )
                                }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-center align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold d-flex justify-center align-center"
                            style="width: 100%"
                          >
                            Actions
                          </div>
                          <section
                            class="d-flex justify-start align-center"
                            style="width: 100%"
                          >
                            <ActionMenu
                              v-if="itemActions(item)?.length > 0"
                              :accessRights="checkAccess()"
                              @selected-action="onActions($event, item)"
                              :actions="itemActions(item)"
                              iconColor="grey"
                            ></ActionMenu>
                            <div v-else>
                              <p>-</p>
                            </div>
                          </section>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
    <AppLoading v-if="listLoading" />
    <ViewCustomFields
      v-if="showViewForm"
      :selected-item="selectedItem"
      :form-access="formAccess"
      :landed-form-name="landedFormName"
      :list-data="originalList"
      @open-edit-form="onOpenEditForm()"
      @close-view-form="onCloseOverlay()"
    />
    <AddEditCustomFields
      v-if="showAddEditForm"
      :selected-item="selectedItem"
      :list-forms="listForms"
      :list-mapping-keys="listMappingKeys"
      :landed-form-name="landedFormName"
      :list-data="originalList"
      :is-edit="isEdit"
      @custom-fields-updated="refetchList()"
      @close-form="onCloseOverlay()"
    />
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard.vue")
);
import { checkNullValue, convertUTCToLocal } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
const AddEditCustomFields = defineAsyncComponent(() =>
  import("./AddEditCustomFields.vue")
);
const ViewCustomFields = defineAsyncComponent(() =>
  import("./ViewCustomFields.vue")
);
import { RETRIEVE_DYNAMIC_FORM_FIELDS } from "@/graphql/settings/general/customFieldsQuries.js";
export default {
  name: "CustomFields",
  data() {
    return {
      isEdit: false,
      selectedItem: null,
      showViewForm: false,
      showAddEditForm: false,
      listLoading: false,
      listForms: [],
      listMappingKeys: [],
      dropdownLoading: false,
      itemList: [],
      originalList: [],
      searchList: [],
      openMoreMenu: false,
      currentTabItem: "tab-1",
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      selectedForm: null,
    };
  },
  components: {
    ActionMenu,
    EmployeeDefaultFilterMenu,
    NotesCard,
    ViewCustomFields,
    AddEditCustomFields,
    CustomSelect,
  },
  mixins: [FileExportMixin],
  computed: {
    landedFormName() {
      let customFieldForm = this.accessRights("309");
      if (
        customFieldForm?.customFormName &&
        customFieldForm.customFormName !== ""
      ) {
        return customFieldForm.customFormName;
      } else return "Custom Fields";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    multipleAccessRights() {
      return this.$store.getters.formIdsBasedAccessRights;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    mainTabs() {
      let tabs = ["General", `${this.landedFormName}`];
      if (this.emailTemplateFormAccess && this.emailTemplateFormAccess.view) {
        tabs.push("Email Templates");
      }
      if (this.salaryTemplateAccess && this.salaryTemplateAccess.view) {
        tabs.push("Salary Template");
      }
      return tabs;
    },
    salaryTemplateAccess() {
      let formAccess = this.accessRights("206");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    formAccess() {
      let formAccessRights = this.accessRights("309");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    emailTemplateFormAccess() {
      let formAccess = this.accessRights("310");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length) {
        msgText = `There are no ${this.landedFormName.toLowerCase()} for the selected filters/searches.`;
      }
      return msgText;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    moreActions() {
      let moreActions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return moreActions;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    tableHeaders() {
      const headers = [
        {
          title: "Field",
          align: "start",
          key: "Custom_Field_Name",
        },
        {
          title: "Field Type",
          key: "Custom_Field_Type",
        },
      ];

      if (!this.selectedForm) {
        headers.push({
          title: "Forms",
          key: "FormIds",
        });
      }

      headers.push({
        title: "Who Can See",
        key: "Roles_Id",
      });
      if (this.selectedForm) {
        headers.push({
          title: "Required",
          key: "Mandatory",
        });
      }

      headers.push({
        title: "Actions",
        key: "actions",
        sortable: false,
      });

      return headers;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  created() {
    if (this.isAdmin) {
      this.fetchList();
      this.getDropdownDetails();
    }
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_DYNAMIC_FORM_FIELDS,
          client: "apolloClientI",
          variables: {
            formId: null,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveDynamicFormFields &&
            response.data.retrieveDynamicFormFields.dynamicFields &&
            !response.data.retrieveDynamicFormFields.errorCode
          ) {
            const departList = JSON.parse(
              response.data.retrieveDynamicFormFields.dynamicFields
            );
            vm.itemList = departList;
            vm.searchList = departList;
            vm.originalList = departList;
            vm.onApplySearch();
            vm.listLoading = false;
          } else {
            vm.handleListError(
              response.data.retrieveDynamicFormFields.errorCode
            );
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    getDropdownDetails() {
      let vm = this;
      vm.dropdownLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;

      vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            formId: 309,
            key: ["Custom_Field_Forms", "Integration_Mapping_Key"],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            tempData.forEach((item) => {
              if (item.tableKey?.toLowerCase() === "custom_field_forms") {
                const tempFormIds = item.data.map((item) => item.Form_Id);
                vm.listForms = vm.multipleAccessRights(tempFormIds) || [];
              } else if (
                item.tableKey?.toLowerCase() === "integration_mapping_key"
              ) {
                vm.listMappingKeys = item.data;
              }
            });
          } else {
            let err = res.data.retrieveDropdownDetails.errorCode;
            vm.handleGetDropdownDetails(err);
          }
          vm.dropdownLoading = false;
        })
        .catch((err) => {
          vm.dropdownLoading = false;
          vm.handleGetDropdownDetails(err);
        });
    },
    handleGetDropdownDetails(err) {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: this.landedFormName,
        isListError: false,
      });
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.openMoreMenu = false;
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedForm = null;
      this.selectedItem = null;
      this.isEdit = false;
      this.resetFilter();
      this.fetchList();
    },
    checkMandatoryValue(formIds) {
      // Ensure formIds and selectedForm are valid
      if (!formIds || !this.selectedForm) return null;

      // Find the matching form
      const matchingForm = formIds.find(
        (form) => form.Form_Id === this.selectedForm
      );

      // Return the Mandatory value or a default if not found
      return matchingForm ? matchingForm.Mandatory : null;
    },
    filteredItemList() {
      this.onApplySearch();
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      // If no form is selected, return the full item list
      if (!this.selectedForm) {
        this.itemList = this.originalList;
        this.searchList = this.originalList;
      } else {
        // Filter the item list based on the selected form ID
        this.searchList = this.originalList.filter((item) =>
          item.FormIds.some((form) => form.Form_Id === this.selectedForm)
        );
        this.itemList = this.searchList;
      }
    },
    getAllFormNames(forms) {
      return forms.map((form) => form.Form_Name).join(", ");
    },
    getAllRoleNames(roles) {
      return roles.map((role) => role.Roles_Name).join(", ");
    },
    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
    },
    openAddForm() {
      if (this.listForms?.length === 0) {
        var snackbarData = {
          isOpen: true,
          type: "info",
          message:
            "No form is currently available for Add/Edit. Please contact the system administrator for assistance.",
        };
        this.showAlert(snackbarData);
      } else this.showAddEditForm = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onOpenEditForm() {
      if (this.listForms?.length === 0) {
        var snackbarData = {
          isOpen: true,
          type: "info",
          message:
            "No form is currently available for Add/Edit. Please contact the system administrator for assistance.",
        };
        this.showAlert(snackbarData);
      } else {
        this.isEdit = true;
        this.showAddEditForm = true;
      }
    },
    onCloseOverlay() {
      this.selectedItem = null;
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.isEdit = false;
      this.resetFilter();
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        if (tab.toLowerCase() === "general") {
          window.location.href = this.baseUrl + "settings/general";
        }
        if (tab.toLowerCase() === "salary template") {
          this.$router.push("/settings/general/salary-template");
        }
        if (tab.toLowerCase() === "email templates") {
          this.$router.push("/settings/general/email-templates");
        }
      }
    },
    itemActions() {
      if (this.formAccess?.update) return ["Edit"];
      else return [];
    },
    checkAccess() {
      let havingAccess = {};
      havingAccess["update"] =
        this.formAccess && this.formAccess?.update ? 1 : 0;
      return havingAccess;
    },
    onActions(action, item) {
      this.selectedItem = item;
      if (action?.toLowerCase() === "edit") {
        if (this.listForms?.length === 0) {
          var snackbarData = {
            isOpen: true,
            type: "info",
            message:
              "No form is currently available for Add/Edit. Please contact the system administrator for assistance.",
          };
          this.showAlert(snackbarData);
        } else {
          this.isEdit = true;
          this.showAddEditForm = true;
        }
      }
    },
    onMoreAction(actionType) {
      if (actionType.key === "Export") {
        this.exportReportFile(actionType);
      }
      this.openMoreMenu = false;
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.searchList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.searchList;
        searchItems = searchItems.filter((item) => {
          let matches = false;
          matches =
            item.Custom_Field_Name?.toLowerCase().includes(searchValue) ||
            item.Custom_Field_Type?.toLowerCase().includes(searchValue) ||
            item.Roles_Id?.some((role) =>
              role.Roles_Name.toLowerCase().includes(searchValue)
            );
          if (!this.selectedForm) {
            matches =
              matches ||
              item.FormIds?.some((form) =>
                form.Form_Name?.toLowerCase().includes(searchValue)
              );
          } else {
            matches =
              matches ||
              item.FormIds?.some(
                (form) =>
                  form.Form_Id === this.selectedForm &&
                  form.Mandatory?.toLowerCase().includes(searchValue)
              );
          }

          return matches;
        });
        this.itemList = searchItems;
      }
    },
    resetFilter() {
      if (!this.itemList?.length) {
        this.selectedForm = null;
        this.searchList = this.originalList;
        this.itemList = this.originalList;
      } else this.itemList = this.searchList;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
    },
    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.originalList));

      exportData = exportData.map((el) => {
        const roles =
          el.Roles_Id?.map((role) => role.Roles_Name).join("\n") || "";
        const forms = el.FormIds
          ? el.FormIds.map(
              (form) =>
                `Form:${form.Form_Name}\nMandatory: ${
                  form.Mandatory
                }\nIntegration Mapping Key: ${
                  form.Integration_Mapping_Key || "-"
                }`
            ).join("\n\n")
          : "";
        const dropdownValues = el.Dropdown_Values
          ? JSON.parse(el.Dropdown_Values).join("\n")
          : "";

        return {
          ...el,
          Roles_Id: roles,
          FormIds: forms,
          Dropdown_Values: dropdownValues,
          Url_Link: el.Url_Link || "", // Add the URL link column
          Added_On: el.Added_On ? this.convertUTCToLocal(el.Added_On) : "",
          Updated_On: el.Updated_On
            ? this.convertUTCToLocal(el.Updated_On)
            : "",
        };
      });

      // Export options
      let exportOptions = {
        fileExportData: exportData,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: [
          { header: "Field Name", key: "Custom_Field_Name" },
          { header: "Field Type", key: "Custom_Field_Type" },
          { header: "Minimum Characters", key: "Min_Validation" },
          { header: "Maximum Characters", key: "Max_Validation" },
          { header: "Validation Rule", key: "Validation_Name" },
          { header: "Dropdown Values", key: "Dropdown_Values" },
          { header: "URL Link", key: "Url_Link" },
          { header: "Who Can See", key: "Roles_Id" },
          { header: "Forms", key: "FormIds" },
          { header: "Created On", key: "Added_On" },
          { header: "Created By", key: "Added_By" },
          { header: "Updated On", key: "Updated_On" },
          { header: "Updated By", key: "Updated_By" },
        ],
      };

      this.exportExcelFile(exportOptions);
    },
  },
};
</script>

<style>
.custom-fields-container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .custom-fields-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
