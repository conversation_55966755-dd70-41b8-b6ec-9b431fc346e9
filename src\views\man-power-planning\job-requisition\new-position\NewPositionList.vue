<template>
  <div v-if="formAccess && formAccess.view">
    <div v-if="listLoading || isLoading || listAPILoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>

    <AppFetchErrorScreen
      v-else-if="isErrorInList"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      image-name="common/human-error-image"
      button-text="Retry"
      @button-click="refetchList()"
    >
    </AppFetchErrorScreen>
    <AppFetchErrorScreen
      v-else-if="originalList.length == 0"
      key="no-data-screen"
    >
      <template #contentSlot>
        <div style="max-width: 80%">
          <v-row class="rounded-lg pa-5 mb-4" style="background: white">
            <v-col cols="12">
              <NotesCard
                notes="The New Position & Additional Headcount feature enables department heads and managers to request the creation of new positions within their teams, aligning with organizational growth and evolving business needs."
                backgroundColor="transparent"
                class="mb-4"
              ></NotesCard>
              <NotesCard
                notes="This functionality allows for structured, strategic manpower planning by ensuring that every position request is justified, evaluated, and approved through an established workflow."
                backgroundColor="transparent"
                class="mb-4"
              ></NotesCard>
            </v-col>
            <v-col cols="12">
              <div
                class="d-flex flex-row align-center flex-wrap justify-center"
              >
                <CustomSelect
                  v-model="selectedPosition"
                  color="primary"
                  :items="groupList"
                  class="mt-5 mr-3"
                  clearable
                  :isAutoComplete="true"
                  label="Position Group"
                  placeholder="Position Group"
                  item-title="Pos_full_Name"
                  :itemSelected="selectedPosition"
                  item-value="Pos_Code"
                  density="compact"
                  style="max-width: 220px !important; width: 150px !important"
                  :disabled="!(isAdmin || isRecruiter)"
                  :isLoading="listLoading"
                  @update:model-value="updateGroup()"
                >
                </CustomSelect>
                <CustomSelect
                  v-model="selectedDivision"
                  color="primary"
                  :items="divisionList"
                  class="mt-5 mr-3"
                  clearable
                  :isAutoComplete="true"
                  label="Division"
                  placeholder="Division"
                  item-title="Pos_full_Name"
                  :itemSelected="selectedDivision"
                  item-value="Pos_Code"
                  density="compact"
                  style="max-width: 220px !important; width: 150px !important"
                  :disabled="!((isAdmin || isRecruiter) && selectedPosition)"
                  :isLoading="listLoading"
                  @update:model-value="updateDivision()"
                >
                </CustomSelect>

                <CustomSelect
                  v-model="selectedDepartment"
                  color="primary"
                  :items="departmentList"
                  class="mt-5 mr-3"
                  clearable
                  :isAutoComplete="true"
                  label="Department"
                  placeholder="Department"
                  item-title="Pos_full_Name"
                  :itemSelected="selectedDepartment"
                  item-value="Pos_Code"
                  density="compact"
                  :disabled="!selectedDivision"
                  :isLoading="listLoading"
                  style="max-width: 220px !important; width: 150px !important"
                  @update:model-value="updateDepartment()"
                />
                <CustomSelect
                  v-model="selectedSection"
                  color="primary"
                  :items="sectionList"
                  class="mt-5 mr-3"
                  clearable
                  :isAutoComplete="true"
                  label="Section"
                  placeholder="Section"
                  item-title="Pos_full_Name"
                  :itemSelected="selectedSection"
                  item-value="Pos_Code"
                  density="compact"
                  :disabled="!selectedDepartment"
                  :isLoading="listLoading"
                  style="max-width: 220px !important; width: 150px !important"
                  @update:model-value="updateSection()"
                />
              </div>
            </v-col>
            <v-col
              cols="12"
              :class="
                windowWidth >= 1264
                  ? 'd-flex align-center justify-center'
                  : 'flex-wrap d-flex align-center justify-center'
              "
              class="mb-4"
            >
              <div v-if="formAccess && formAccess.add">
                <v-tooltip
                  text="Please choose Position Group first"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-card
                      v-bind="disableAddButton ? props : ''"
                      variant="text"
                    >
                      <v-btn
                        @click="addNewPosition()"
                        class="px-6 mr-2 primary"
                        variant="elevated"
                        size="default"
                        :disabled="disableAddButton"
                      >
                        <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                        <span>
                          Add New Position & Additional Headcount</span
                        ></v-btn
                      >
                    </v-card>
                  </template>
                </v-tooltip>
              </div>
              <v-tooltip location="bottom" v-else>
                <template v-slot:activator="{ props }">
                  <v-chip
                    class="mr-3"
                    v-bind="props"
                    rounded="lg"
                    color="grey"
                    size="large"
                  >
                    Add New Position & Additional Headcount</v-chip
                  >
                </template>
                <div style="max-width: 350px !important">
                  Your don't have access to perform this action
                </div>
              </v-tooltip>
              <v-btn
                color="transparent"
                variant="flat"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click="refetchList()"
              >
                <v-icon class="pr-1 grey">fas fa-redo-alt</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
    <AppFetchErrorScreen
      v-else-if="newPositionList.length === 0 && !listLoading"
      key="no-results-screen"
      main-title="There are no New Position & Additional Headcount matched for the selected filters/searches."
      image-name="common/no-records"
    >
      <template #contentSlot>
        <div style="max-width: 80%">
          <v-row class="rounded-lg pa-5 mb-4">
            <v-col cols="12" class="d-flex align-center justify-center mb-4">
              <v-btn
                color="primary"
                variant="elevated"
                class="ml-4 mt-1"
                rounded="lg"
                :size="windowWidth <= 960 ? 'small' : 'default'"
                @click="resetList()"
              >
                Reset Filter/Search
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
    <div v-else>
      <div
        v-if="newPositionList.length > 0"
        class="d-flex flex-wrap align-center my-3"
        :class="isMobileView ? 'flex-column' : ''"
        style="justify-content: space-between"
      >
        <div
          class="d-flex align-center flex-wrap"
          :class="isMobileView ? 'justify-center' : ''"
        >
          <CustomSelect
            v-model="selectedPosition"
            color="primary"
            :items="groupList"
            class="mt-5 mr-3"
            clearable
            :isAutoComplete="true"
            label="Position Group"
            placeholder="Position Group"
            item-title="Pos_full_Name"
            :itemSelected="selectedPosition"
            item-value="Pos_Code"
            density="compact"
            min-width="150px"
            max-width="200px"
            :disabled="!(isAdmin || isRecruiter)"
            :isLoading="listLoading"
            @update:model-value="updateGroup()"
          />
          <CustomSelect
            v-model="selectedDivision"
            color="primary"
            :items="divisionList"
            class="mt-5 mr-3"
            clearable
            :isAutoComplete="true"
            label="Division"
            placeholder="Division"
            item-title="Pos_full_Name"
            :itemSelected="selectedDivision"
            item-value="Pos_Code"
            density="compact"
            min-width="150px"
            max-width="200px"
            :disabled="!((isAdmin || isRecruiter) && selectedPosition)"
            :isLoading="listLoading"
            @update:model-value="updateDivision()"
          />
          <CustomSelect
            v-model="selectedDepartment"
            color="primary"
            :items="departmentList"
            class="mt-5 mr-3"
            clearable
            :isAutoComplete="true"
            label="Department"
            placeholder="Department"
            item-title="Pos_full_Name"
            :itemSelected="selectedDepartment"
            item-value="Pos_Code"
            density="compact"
            min-width="150px"
            max-width="200px"
            :disabled="!selectedDivision"
            :isLoading="listLoading"
            @update:model-value="updateDepartment()"
          />
          <CustomSelect
            v-model="selectedSection"
            color="primary"
            :items="sectionList"
            class="mt-5 mr-3"
            clearable
            :isAutoComplete="true"
            label="Section"
            placeholder="Section"
            item-title="Pos_full_Name"
            :itemSelected="selectedSection"
            item-value="Pos_Code"
            density="compact"
            min-width="150px"
            max-width="200px"
            :disabled="!selectedDepartment"
            :isLoading="listLoading"
            @update:model-value="updateSection()"
          />
        </div>
        <div
          class="d-flex align-center"
          :class="isMobileView ? 'justify-center' : 'justify-end'"
        >
          <div v-if="formAccess && formAccess.add">
            <v-btn
              @click="addNewPosition()"
              class="px-6 mr-2 primary"
              variant="elevated"
              size="default"
            >
              <v-icon size="15" class="pr-1 primary">fas fa-plus</v-icon>
              <span class="primary">
                Add New Position & Additional Headcount</span
              ></v-btn
            >
          </div>
          <v-tooltip location="bottom" v-else>
            <template v-slot:activator="{ props }">
              <v-chip
                class="mr-3"
                v-bind="props"
                rounded="lg"
                color="grey"
                size="large"
              >
                Add New Position & Additional Headcount</v-chip
              >
            </template>
            <div style="max-width: 350px !important">
              Your don't have access to perform this action
            </div>
          </v-tooltip>
          <v-btn
            color="transparent"
            variant="flat"
            rounded="lg"
            :size="isMobileView ? 'small' : 'default'"
            @click="refetchList()"
          >
            <v-icon class="pr-1 grey">fas fa-redo-alt</v-icon>
          </v-btn>
          <v-menu class="mb-1" transition="scale-transition">
            <template v-slot:activator="{ props }">
              <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                <v-icon>fas fa-ellipsis-v</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item
                v-for="action in moreActions"
                :key="action.key"
                @click="onMoreAction(action.key)"
              >
                <v-hover>
                  <template v-slot:default="{ isHovering, props }">
                    <v-list-item-title
                      v-bind="props"
                      class="pa-3"
                      :class="{
                        'pink-lighten-5': isHovering,
                      }"
                      ><v-icon size="15" class="pr-2">{{ action.icon }}</v-icon
                      >{{ action.key }}</v-list-item-title
                    >
                  </template>
                </v-hover>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>
      <v-data-table
        :headers="tableHeaders"
        :items="newPositionList"
        fixed-header
        :items-per-page="50"
        :items-per-page-options="[
          { value: 50, title: '50' },
          { value: 100, title: '100' },
          { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
        ]"
        :height="
          $store.getters.getTableHeightBasedOnScreenSize(
            290,
            newPositionList,
            true
          )
        "
        style="box-shadow: none !important"
        class="elevation-1"
      >
        <template v-slot:item="{ item }">
          <tr
            @click="openViewForm(item)"
            class="data-table-tr bg-white cursor-pointer"
            :class="isMobileView ? ' v-data-table__mobile-table-row mt-2' : ''"
          >
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Position
              </div>
              <section class="justify-start align-center">
                <v-tooltip
                  :text="item?.Position_Title"
                  location="bottom"
                  max-width="200"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      v-bind="props"
                      class="text-body-2 text-truncate text-start text-primary"
                      :style="'max-width: 100px'"
                    >
                      {{ checkNullValue(item.Position_Title) }}
                    </div>
                  </template>
                </v-tooltip>
                <div
                  class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                  :style="
                    !isMobileView ? 'max-width: 100px; ' : 'max-width: 200px; '
                  "
                >
                  {{ checkNullValue(item.Pos_Code) }}
                </div>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <v-tooltip
                :text="item?.Group_Name"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Group_Name ? props : {}"
                    class="text-body-2 text-truncate text-start text-primary"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Group_Name) }}
                  </div>
                </template>
              </v-tooltip>
              <v-tooltip
                :text="item?.Group_Code"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Group_Code ? props : {}"
                    class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Group_Code) }}
                  </div>
                </template></v-tooltip
              >
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <v-tooltip
                :text="item?.Division_Name"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Division_Name ? props : {}"
                    class="text-body-2 text-truncate text-start text-primary"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Division_Name) }}
                  </div>
                </template>
              </v-tooltip>
              <v-tooltip
                :text="item?.Division_Code"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Division_Code ? props : {}"
                    class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Division_Code) }}
                  </div>
                </template></v-tooltip
              >
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <v-tooltip
                :text="item?.Department_Name"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Department_Name ? props : {}"
                    class="text-body-2 text-truncate text-start text-primary"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Department_Name) }}
                  </div>
                </template>
              </v-tooltip>
              <v-tooltip
                :text="item?.Department_Code"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Department_Code ? props : {}"
                    class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Department_Code) }}
                  </div>
                </template></v-tooltip
              >
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <v-tooltip
                :text="item?.Section_Name"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Section_Name ? props : {}"
                    class="text-body-2 text-truncate text-start text-primary"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Section_Name) }}
                  </div>
                </template>
              </v-tooltip>
              <v-tooltip
                :text="item?.Section_Code"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Section_Code ? props : {}"
                    class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Section_Code) }}
                  </div>
                </template></v-tooltip
              >
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Position Level
              </div>
              <section style="height: 3em" class="d-flex align-center">
                <div
                  class="text-subtitle-1 font-weight-regular text-truncate"
                  :style="'max-width: 100px'"
                >
                  {{ checkNullValue(item.Position_Level) }}
                </div>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Cost Center
              </div>
              <section style="height: 3em" class="d-flex align-center">
                <div
                  class="text-subtitle-1 font-weight-regular text-truncate"
                  :style="'max-width: 100px'"
                >
                  {{ checkNullValue(item.Cost_Center) }}
                </div>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Employee Type
              </div>
              <section style="height: 3em" class="d-flex align-center">
                <div
                  class="text-subtitle-1 font-weight-regular text-truncate"
                  :style="'max-width: 100px'"
                >
                  {{ checkNullValue(item.Employee_Type_Name) }}
                </div>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                No of Positions
              </div>
              <section style="height: 3em" class="d-flex align-center">
                <div
                  class="text-subtitle-1 font-weight-regular text-truncate"
                  :style="'max-width: 100px'"
                >
                  {{ checkNullValue(item.No_Of_Position) }}
                </div>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Status
              </div>
              <section style="height: 3em" class="d-flex align-center">
                <div
                  class="text-subtitle-1 font-weight-regular text-truncate"
                  :style="'max-width: 150px'"
                >
                  <span
                    id="w-80"
                    :class="getStatusClass(item.Status)"
                    class="text-body-2 font-weight-regular d-flex justify-center align-center"
                    >{{ item.Status }}</span
                  >
                </div>
              </section>
            </td>
            <td
              class="text-body-2"
              :class="
                isMobileView ? 'd-flex justify-space-between align-center' : ''
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Actions
              </div>
              <div
                class="d-flex align-center justify-center"
                v-if="
                  listAction(item.Status).length > 0 &&
                  (loginEmployeeName?.toLowerCase() ===
                    item?.Added_By?.toLowerCase() ||
                    isAdmin)
                "
              >
                <ActionMenu
                  :actions="listAction(item.Status)"
                  iconColor="grey"
                  @selected-action="onListAction($event, item)"
                  :access-rights="havingAccess"
                ></ActionMenu>
              </div>
              <div v-else class="d-flex align-center justify-center">-</div>
            </td>
          </tr>
        </template></v-data-table
      >
    </div>
  </div>
  <AppAccessDenied v-else></AppAccessDenied>
  <AppLoading v-if="isLoading"></AppLoading>
  <AddEditNewPosition
    v-if="showNewPositionForm"
    :formAccess="formAccess"
    :selectedPositionParentId="selectedPositionParentId"
    :show-add-form="showNewPositionForm"
    :selected-new-position="selectedNewPositionFormData"
    :selected-position-data="selectedPositionData"
    @on-close-add-form="onCloseNewPosition()"
    @open-workflow-form="onOpenWorkflowForm($event)"
    @edit-form-submit="editNewPosition($event)"
    @open-job-assessment-form="onOpenJobAssessmentForm($event)"
    @goto-next-duties="gotoNextDuties($event)"
  />
  <!-- this job assessment is under construction -->
  <JobAssessmentQuestionnaire
    :formAccess="formAccess"
    :show-job-assessment-form="showJobAssessmentForm"
    :selected-position-data="selectedPositionData"
    @submitJobAssessment="submitJobAssessment()"
    @closeJobAssessmentForm="closeJobAssessmentForm()"
    @refresh-form-data="retrieveIndividualPositionDetails()"
    @goto-next-education="onGotoNextEducation()"
  />
  <EducationExperience
    :formAccess="formAccess"
    :showEducationalForm="showEducationExperienceForm"
    :selected-position-data="selectedPositionData"
    @submitEducationalForm="submitEducationalForm()"
    @closeEducationalForm="closeEducationForm()"
    @refresh-form-data="retrieveIndividualPositionDetails()"
    @goto-duties="onGotoDuties()"
    @goto-next-license-form="onGotoNextLicense()"
  />
  <LicenseComments
    :formAccess="formAccess"
    :showLicenseForm="showLicenseCommentsForm"
    :selected-position-data="selectedPositionData"
    @submitLicenseForm="submitLicenseForm()"
    @closeLicenseForm="closeLicenseForm()"
    @refresh-form-data="retrieveIndividualPositionDetails()"
    @enable-workflow-form-data="enableWorkflowFormData($event)"
    @goto-education-form="onGotoEducation()"
    @edit-form-submit="editJAQForm()"
  />

  <v-overlay v-model="openCommentForm" class="d-flex justify-end">
    <v-card height="100vh" width="30vw" class="">
      <div
        class="d-flex align-center text-h6 text-medium-emphasis pa-2 bg-primary"
        style="width: 100%"
      >
        <v-spacer></v-spacer>
        <v-btn
          icon="fas fa-times"
          variant="text"
          @click="closeCommentWindow()"
        ></v-btn>
      </div>
      <v-form class="pa-5" ref="reviewForm">
        <v-textarea
          v-model="comment"
          :rules="[
            selectedAction == 'TO In Review'
              ? true
              : required('Comment', comment),
            multilingualNameNumericValidation('Comment', comment),
            selectedAction == 'TO In Review'
              ? true
              : minLengthValidation('Comment', comment, 3),
            maxLengthValidation('Comment', comment, 100),
          ]"
          label="Comment"
          :clearable="true"
          auto-grow
          variant="solo"
        >
          <template v-slot:label>
            Comment
            <span v-if="selectedAction !== 'TO In Review'" class="text-red"
              >*</span
            >
          </template>
        </v-textarea>
      </v-form>
      <v-card
        elevation="16"
        width="100%"
        class="footer position-absolute bottom-0 right-0"
      >
        <v-btn
          class="mr-5 secondary"
          variant="text"
          elevation="2"
          rounded="lg"
          @click="closeCommentWindow()"
        >
          Cancel
        </v-btn>
        <v-btn
          class="secondary"
          variant="elevated"
          rounded="lg"
          @click="validateForm()"
        >
          Preview Mail
        </v-btn>
      </v-card>
    </v-card>
  </v-overlay>
  <v-dialog
    v-model="showPreForm"
    class="rounded-lg overlay-custom-center"
    :style="isMobileView ? '' : 'width:50vw !important'"
  >
    <v-card class="px-8 rounded-lg">
      <v-icon
        color="primary"
        class="font-weight-bold mt-4 ml-auto"
        @click="showPreForm = false"
        >far fa-times
      </v-icon>
      <v-form ref="workflowForm">
        <v-card-title class="d-flex justify-center"
          >Choose your Workflow</v-card-title
        >
        <CustomSelect
          v-model="selectedWorkflow"
          :items="dropdownWorkflow"
          item-title="Workflow_Name"
          item-value="Workflow_Id"
          :isAutoComplete="true"
          label="Choose the approval workflow *"
          variant="underlined"
          :isLoading="workflowLoading"
          clearable
          :itemSelected="selectedWorkflow"
          :rules="[required('Workflow', selectedWorkflow)]"
          @selected-item="selectedWorkflow = $event"
        ></CustomSelect>
        <div class="d-flex justify-center pb-8">
          <v-btn
            class="px-8 primary"
            variant="elevated"
            :disabled="workflowLoading"
            @click="validateWorkflowForm"
            ><span class="primary">Start</span></v-btn
          >
        </div>
      </v-form>
    </v-card>
  </v-dialog>
  <CustomEmail
    ref="customEmail"
    v-if="openCustomEmail"
    :formId="16"
    :is-overlay="true"
    :templateEmail="templateEmails"
    :templateData="templateData"
    :typeOfTemplate="templateType"
    typeOfSchedule="noncalendar"
    @custom-email-cancel="closeCustomEmail()"
    @custom-email-sent="emailSuccess()"
  ></CustomEmail>
  <NewPositionView
    :showViewDetails="showViewDetails"
    :selectedPositionData="selectedPositionData"
    @close-view-details="closeViewDetails()"
    @edit-position-details="editPositionDetails()"
  />
</template>
<script>
import NotesCard from "@/components/helper-components/NotesCard.vue";
import { LIST_WORKFLOW } from "@/graphql/settings/irukka-integration/jobPostFormQueries";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import {
  NEW_POSITION_LIST,
  UPDATE_POSITION_STATUS,
} from "@/graphql/mpp/manPowerPlanningQueries";
import { checkNullValue } from "@/helper";
import AddEditNewPosition from "./AddEditNewPosition.vue";
import JobAssessmentQuestionnaire from "./new-position-forms/JobAssessmentQuestionnaire.vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import FileExportMixin from "@/mixins/FileExportMixin";
import {
  ADD_EDIT_NEW_POSITION_REQUEST,
  RETRIEVE_NEW_POSITION_DETAILS,
  ORG_STRUCTURE_BASED_ON_GROUP,
} from "@/graphql/mpp/newPositionQueries";
import EducationExperience from "./new-position-forms/EducationExperience.vue";
import LicenseComments from "./new-position-forms/LicenseComments.vue";
import CustomEmail from "@/views/common/customEmail/CustomEmail.vue";
import moment from "moment";
import NewPositionView from "./NewPositionView.vue";

export default {
  name: "NewPositionList",
  emits: [
    "apply-selected-group",
    "enable-filter",
    "reset-filter-value",
    "reset-division-filter",
  ],
  props: {
    selectedFilterGroup: {
      type: [String, null],
      required: true,
    },
    tempSelectedOriginalPositionId: {
      type: [String, null],
      required: true,
    },
    groupList: {
      type: Array,
      required: true,
      default: () => [],
    },
    tempSelectedFilter: {
      type: Object,
      required: true,
    },
  },
  mixins: [validationRules, FileExportMixin],
  data() {
    return {
      listLoading: false,
      listAPILoading: false,
      isLoading: false,
      isErrorInList: false,
      showNewPositionForm: false,
      showOptionModal: false,
      workflowLoading: false,
      showPreForm: false,
      openMoreMenu: false,
      selectedWorkflow: null,
      errorContent: "",
      newPositionList: [],
      originalList: [],
      dropdownWorkflow: [],
      newPositionFormData: {},
      selectedNewPositionFormData: {},
      //job assessment form
      showJobAssessmentForm: false,

      // education experience form
      showEducationExperienceForm: false,

      showLicenseCommentsForm: false,
      selectedPositionRequestId: null,
      selectedPositionData: {},
      tempPositionData: [],
      selectedPositionId: "",
      selectedPositionParentId: null,
      selectedPosition: "",
      openCommentForm: false,
      comment: "",
      openCustomEmail: false,
      selectedAction: "",
      selectedItem: "",
      templateType: "",
      templateData: {},
      templateEmails: [],
      showViewDetails: false,
      selectedDivision: "",
      selectedDepartment: "",
      selectedSection: "",
      divisionList: [],
      departmentList: [],
      sectionList: [],
      newPositionLimitToCallAPI: 10000,
      totalApiCount: 0,
      apiCallCount: 0,
    };
  },
  components: {
    NotesCard,
    CustomSelect,
    AddEditNewPosition,
    JobAssessmentQuestionnaire,
    ActionMenu,
    EducationExperience,
    LicenseComments,
    CustomEmail,
    NewPositionView,
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    loginEmployeeName() {
      return this.$store.state.orgDetails.userDetails.employeeFullName;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    formAccess() {
      let formAccess = this.accessRights("290");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    tableHeaders() {
      let headers = [
        {
          title: "Position",
          align: "start",
          key: "Position_Title",
        },
        { title: "Group", align: "start", key: "Group_Code" },
        { title: "Division", align: "start", key: "Division_Code" },
        { title: "Department", align: "start", key: "Department_Code" },
        { title: "Section", align: "start", key: "Section_Code" },
        { title: "Position Level", align: "start", key: "Position_Level" },
        { title: "Cost Center", align: "start", key: "Cost_Center" },
        { title: "Employee Type", align: "start", key: "Employee_Type_Name" },
        { title: "No of Positions", align: "start", key: "No_Of_Position" },
        { title: "Status", align: "start", key: "Status" },
        { title: "Actions", align: "start", sortable: false, key: "" },
      ];
      return headers;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    TOFormAccess() {
      let formAccess = this.accessRights("296");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    listAction() {
      return (status) => {
        let actions = [];
        if (
          status.toLowerCase() == "waiting for approval" ||
          status.toLowerCase() == "draft"
        ) {
          actions.push("Edit");
        }
        if (
          this.TOFormAccess &&
          this.TOFormAccess.update &&
          status.toLowerCase() == "approved"
        ) {
          actions.push("TO In Review");
        }
        if (
          this.TOFormAccess &&
          this.TOFormAccess.update &&
          status.toLowerCase() == "to in review"
        ) {
          actions.push("TO Changes Accept", "TO Changes Reject");
        }
        return actions;
      };
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    isRecruiter() {
      return this.$store.state.isRecruiter;
    },
    havingAccess() {
      let access = this.formAccess;
      if (this.formAccess && this.formAccess.update) {
        access["edit"] = this.formAccess.update;
      }
      if (this.TOFormAccess && this.TOFormAccess.update) {
        access["to in review"] = this.TOFormAccess.update;
        access["to changes accept"] = this.TOFormAccess.update;
        access["to changes reject"] = this.TOFormAccess.update;
      }
      return access;
    },
    orgDateFormat() {
      return this.$store.state.orgDetails.orgDateFormat;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    disableAddButton() {
      if (!this.selectedPosition) {
        return true;
      } else {
        return false;
      }
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
    tempSelectedFilter(val) {
      this.selectedPosition = val.group.toString();
      this.selectedPositionId = val.group.toString();
      this.selectedDivision = val.division.toString();
      this.selectedDepartment = val.division.toString();
      this.selectedSection = val.division.toString();
      this.retrieveNewPositionList();
    },
  },
  mounted() {
    if (this.formAccess && this.formAccess.view) {
      if (this.$route.query?.positionRequestId) {
        this.selectedPositionRequestId = parseInt(
          this.$route.query.positionRequestId
        );
        this.selectedNewPositionFormData = {};
        this.retrieveIndividualPositionDetails(true);
      }
      this.retrieveNewPositionList("mounted");
      this.retrieveCountGroupPosition("group");
    }
    this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
  },
  methods: {
    checkNullValue,
    onApplySearch(val) {
      if (!val) {
        this.newPositionList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.newPositionList = searchItems;
      }
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    getStatusClass(status) {
      if (status === "Open") {
        return "text-amber-darken-4";
      } else if (status === "Closed") {
        return "text-amber";
      } else if (status === "Draft") {
        return "text-purple-darken-4";
      } else if (status === "Scheduled For Interview") {
        return "text-green";
      } else if (status === "Approved") {
        return "text-brown-darken-4";
      } else if (status === "Rejected") {
        return "text-red";
      } else {
        return "text-blue";
      }
    },
    refetchList() {
      this.retrieveNewPositionList();
    },
    resetList() {
      this.$emit("reset-filter-value");
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onCloseNewPosition() {
      this.showNewPositionForm = false;
    },
    withoutWorkflow() {
      this.showNewPositionForm = false;
      this.showOptionModal = false;
    },
    addNewPosition() {
      // this.selectedNewPositionFormData = {};
      this.selectedPositionData = {};
      this.tempPositionData = {};
      this.showNewPositionForm = true;
    },
    openWorkflowConfirm() {
      this.showOptionModal = true;
    },
    //job assessment form start here
    onOpenJobAssessmentForm(payload) {
      this.newPositionFormData = payload;
      this.tempPositionData = payload;
      if (this.selectedPositionData) {
        if (this.selectedPositionData.License_Certificate) {
          payload.licenseCertificate =
            this.selectedPositionData.License_Certificate;
        }
        if (this.selectedPositionData.Organization_Structure_Id) {
          payload.organizationStructureId =
            this.selectedPositionData.Organization_Structure_Id;
        }
        if (
          this.selectedPositionData.Workflow_Id &&
          this.selectedPositionData.Event_Id
        ) {
          payload.workflowId = this.selectedPositionData.Workflow_Id;
          payload.eventId = this.selectedPositionData.Event_Id;
        }
      }
      this.isLoading = true;
      this.$apollo
        .mutate({
          mutation: ADD_EDIT_NEW_POSITION_REQUEST,
          client: "apolloClientAH",
          fetchPolicy: "no-cache",
          variables: payload,
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.addUpdateNewPosition &&
            res.data.addUpdateNewPosition.message &&
            res.data.addUpdateNewPosition.positionRequestId
          ) {
            this.showNewPositionForm = false;
            this.showJobAssessmentForm = true;
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: res.data.addUpdateNewPosition.message,
            };
            this.selectedPositionRequestId =
              res.data.addUpdateNewPosition.positionRequestId;
            this.showNewPositionForm = false;
            this.showJobAssessmentForm = true;
            this.showAlert(snackbarData);
            this.retrieveIndividualPositionDetails();
          } else {
            this.handleRetrieveWorkflowDetailsErrors();
          }
        })
        .catch((err) => {
          this.handleRetrieveWorkflowDetailsErrors(err);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    onOpenWorkflowForm(val) {
      this.newPositionFormData = val;
      this.retrieveWorkflowDetails(false);
    },
    enableWorkflowFormData() {
      this.retrieveWorkflowDetails(false);
    },
    editNewPosition(val) {
      this.newPositionFormData = val;
      this.retrieveWorkflowDetails(true);
    },
    editJAQForm() {
      this.retrieveWorkflowDetails(true);
    },
    onActions(item) {
      this.tempPositionData = {};
      this.selectedPositionRequestId = item.Position_Request_Id;
      this.retrieveIndividualPositionDetails();
      this.showNewPositionForm = true;
    },
    onListAction(action, item) {
      this.selectedItem = item;
      if (action == "Edit") {
        this.tempPositionData = {};
        this.selectedPositionRequestId = item.Position_Request_Id;
        this.retrieveIndividualPositionDetails();
        this.selectedNewPositionFormData = item;
        this.showNewPositionForm = true;
      } else if (action == "TO In Review") {
        this.templateType = "TOReviewMPP";
        this.selectedAction = action;
        let division = "";
        if (item.Division_Name) {
          division = item.Division_Name;
        } else if (item.Department_Name) {
          division = item.Department_Name;
        } else if (item.Section_Name) {
          division = item.Section_Name;
        }
        let templateData = {
          Job_Post_Name: item.Position_Title ? item.Position_Title : "",
          Title: item.Pos_Code ? "Additional Position" : "New Position Request",
          Recruiter_Name: item.Added_By ? item.Added_By : "",
          Date: item.Added_On
            ? moment(item.Added_On).format(this.orgDateFormat)
            : "",
          Division_Name: division,
        };
        if (item.Added_By_Email) {
          this.templateEmails.push(item.Added_By_Email);
        }
        this.templateData = templateData;
        this.openCommentForm = true;
      } else if (action == "TO Changes Accept") {
        this.templateType = "TOChangesAcceptMPP";
        this.selectedAction = "to changes approved";
        let division = "";
        if (item.Division_Name) {
          division = item.Division_Name;
        } else if (item.Department_Name) {
          division = item.Department_Name;
        } else if (item.Section_Name) {
          division = item.Section_Name;
        }
        let templateData = {
          Job_Post_Name: item.Position_Title ? item.Position_Title : "",
          Title: item.Pos_Code ? "Additional Position" : "New Position Request",
          Recruiter_Name: item.Added_By ? item.Added_By : "",
          Date: item.Added_On
            ? moment(item.Added_On).format(this.orgDateFormat)
            : "",
          Division_Name: division,
          Job_Post_Url:
            this.baseUrl + "v3/man-power-planning/job-requisition?formId=290",
        };
        if (item.Added_By_Email) {
          this.templateEmails.push(item.Added_By_Email);
        }
        this.templateData = templateData;
        this.openCommentForm = true;
      } else if (action == "TO Changes Reject") {
        this.templateType = "TOChangesRejectMPP";
        this.selectedAction = "to changes rejected";
        let division = "";
        if (item.Division_Name) {
          division = item.Division_Name;
        } else if (item.Department_Name) {
          division = item.Department_Name;
        } else if (item.Section_Name) {
          division = item.Section_Name;
        }
        let templateData = {
          Job_Post_Name: item.Position_Title ? item.Position_Title : "",
          Title: item.Pos_Code ? "Additional Position" : "New Position Request",
          Recruiter_Name: item.Added_By ? item.Added_By : "",
          Date: item.Added_On
            ? moment(item.Added_On).format(this.orgDateFormat)
            : "",
          Division_Name: division,
          Job_Post_Url:
            this.baseUrl + "v3/man-power-planning/job-requisition?formId=290",
        };
        if (item.Added_By_Email) {
          this.templateEmails.push(item.Added_By_Email);
        }
        this.templateData = templateData;
        this.openCommentForm = true;
      }
    },

    retrieveIndividualPositionDetails(viewFlag = false) {
      if (this.selectedPositionRequestId) {
        this.isLoading = true;
        this.$apollo
          .query({
            query: RETRIEVE_NEW_POSITION_DETAILS,
            variables: {
              positionRequestId: this.selectedPositionRequestId,
            },
            client: "apolloClientAG",
            fetchPolicy: "no-cache",
          })
          .then((res) => {
            if (
              res &&
              res.data &&
              res.data.retrieveNewPositionDetails &&
              res.data.retrieveNewPositionDetails
                .openPositiontRequestRetrieveDetails
            ) {
              if (viewFlag) {
                this.showViewDetails = true;
              }
              this.selectedPositionData =
                res.data.retrieveNewPositionDetails.openPositiontRequestRetrieveDetails;
            } else {
              this.handleRetrieveWorkflowDetailsErrors();
              this.selectedPositionData = null;
            }
          })
          .catch((err) => {
            this.selectedPositionData = null;
            this.handleRetrieveWorkflowDetailsErrors(err);
          })
          .finally(() => {
            this.isLoading = false;
          });
      }
    },
    updateGroup() {
      this.selectedDivision = null;
      this.selectedDepartment = null;
      this.selectedSection = null;
      if (this.selectedPosition) {
        if (this.selectedPosition.toLowerCase() === "nogroup") {
          this.selectedPositionId = "";
          // this.$emit("reset-division-filter", this.selectedPosition);
        } else {
          this.selectedPositionId = this.selectedPosition;
        }
        this.retrieveCountGroupPosition("group");
      }
    },
    updateDivision() {
      this.selectedDepartment = null;
      this.selectedSection = null;
      if (this.selectedDivision) {
        this.retrieveCountGroupPosition("division");
      }
    },
    updateDepartment() {
      this.selectedSection = null;
      if (this.selectedDepartment) {
        this.retrieveCountGroupPosition("department");
      }
    },
    updateSection() {
      if (this.selectedSection) {
        this.retrieveCountGroupPosition();
      }
    },
    openViewForm(item) {
      this.selectedPositionRequestId = parseInt(item.Position_Request_Id);
      this.selectedNewPositionFormData = item;
      this.retrieveIndividualPositionDetails(true);
    },
    editPositionDetails() {
      this.showViewDetails = false;
      this.tempPositionData = {};
      this.retrieveIndividualPositionDetails();
      this.showNewPositionForm = true;
    },
    retrieveCountGroupPosition(type = "") {
      this.listLoading = true;
      let groupId = "0";
      if (this.selectedSection) {
        groupId =
          this.sectionList.find((item) => item.Pos_Code == this.selectedSection)
            ?.Originalpos_Id || "";
      } else if (
        this.selectedDepartment &&
        this.selectedDepartment.toLowerCase() != "nodepartment"
      ) {
        groupId =
          this.departmentList.find(
            (item) => item.Pos_Code == this.selectedDepartment
          )?.Originalpos_Id || "";
      } else if (
        this.selectedDivision &&
        this.selectedDivision.toLowerCase() != "nodivision"
      ) {
        groupId =
          this.divisionList.find(
            (item) => item.Pos_Code == this.selectedDivision
          )?.Originalpos_Id || "";
      } else if (!this.selectedPosition) {
        groupId = "";
      } else if (
        this.selectedPosition &&
        this.selectedPosition.toLowerCase() != "nogroup"
      ) {
        groupId =
          this.groupList.find((item) => item.Pos_Code == this.selectedPosition)
            ?.Originalpos_Id || "";
      }
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: 290,
            postionParentId: String(groupId),
            limit: this.newPositionLimitToCallAPI,
            offset: 0,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data && res.data.listDetailsBasedOnGroupCode) {
            if (res.data.listDetailsBasedOnGroupCode.positionDetails) {
              const tempData =
                res.data.listDetailsBasedOnGroupCode.positionDetails;
              if (type === "group")
                this.divisionList = tempData.divisionList || [];
              if (type === "division")
                this.departmentList = tempData.deptList || [];
              if (type === "department")
                this.sectionList = tempData.sectionList || [];

              if (
                !this.divisionList.some(
                  (item) => item.Pos_Code === "nodivision"
                )
              )
                this.divisionList.unshift({
                  Pos_Name: "No Division",
                  Pos_Code: "nodivision",
                  Pos_full_Name: "No Division",
                  Originalpos_Id: "",
                });

              if (
                !this.departmentList.some(
                  (item) => item.Pos_Code === "nodepartment"
                )
              )
                this.departmentList.unshift({
                  Pos_Name: "No Department",
                  Pos_Code: "nodepartment",
                  Pos_full_Name: "No Department",
                  Originalpos_Id: "",
                });
            } else {
              this.resetTempList();
            }
            let { totalRecords } = res.data.listDetailsBasedOnGroupCode;
            if (totalRecords > 0) {
              totalRecords = parseInt(totalRecords);
              this.apiCallCount = 1;
              this.totalApiCount = Math.ceil(
                totalRecords / this.newPositionLimitToCallAPI
              );
              for (let i = 1; i < this.totalApiCount; i++) {
                this.updateGroupPosition(i, groupId, type);
              }
              this.retrieveNewPositionList();
            }
          }
          this.listLoading = false;
        })
        .catch((err) => {
          this.handleRetrieveNewPositionErrors(err);
          this.resetTempList();
          this.listLoading = false;
        });
    },
    handleRetrieveNewPositionErrors(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "new position request details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: validationMessages[0],
          };
          this.showAlert(snackbarData);
        });
    },
    updateGroupPosition(index = 1, groupId = "", type = "") {
      this.listLoading = true;
      let apiOffset = parseInt(index) * this.newPositionLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: 290,
            postionParentId: String(groupId),
            limit: this.newPositionLimitToCallAPI,
            offset: apiOffset,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.listDetailsBasedOnGroupCode &&
            res.data.listDetailsBasedOnGroupCode.positionDetails
          ) {
            const tempData =
              res.data.listDetailsBasedOnGroupCode.positionDetails;
            if (
              tempData.divisionList &&
              tempData.divisionList.length > 0 &&
              type === "group"
            ) {
              this.divisionList = [
                ...this.divisionList,
                ...tempData.divisionList,
              ];
            }

            if (
              tempData.deptList &&
              tempData.deptList.length > 0 &&
              type === "division"
            ) {
              this.departmentList = [
                ...this.departmentList,
                ...tempData.deptList,
              ];
            }
            if (
              tempData.sectionList &&
              tempData.sectionList.length > 0 &&
              type === "department"
            ) {
              this.sectionList = [...this.sectionList, ...tempData.sectionList];
            }
            this.apiCallCount = this.apiCallCount + 1;
            if (this.totalApiCount === this.apiCallCount) {
              this.listLoading = false;
            }
          } else {
            this.resetTempList();
            this.listLoading = false;
          }
        })
        .catch((err) => {
          this.handleRetrieveNewPositionErrors(err);
          this.resetTempList();
          this.listLoading = false;
        });
    },
    resetTempList() {
      this.divisionList = [];
      this.departmentList = [];
      this.sectionList = [];
    },
    retrieveNewPositionList(callingFrom = "") {
      this.listAPILoading = true;
      let code = "";
      if (this.selectedSection) {
        code = this.selectedSection.toString();
      } else if (
        this.selectedDepartment &&
        this.selectedDepartment.toLowerCase() != "nodepartment"
      ) {
        code = this.selectedDepartment.toString();
      } else if (
        this.selectedDivision &&
        this.selectedDivision.toLowerCase() != "nodivision"
      ) {
        code = this.selectedDivision.toString();
      } else if (!this.selectedPosition) {
        code = "";
      } else if (
        this.selectedPosition &&
        this.selectedPosition.toLowerCase() != "nogroup"
      ) {
        code = this.selectedPosition.toString();
      }
      this.$apollo
        .query({
          query: NEW_POSITION_LIST,
          variables: {
            formId: 290,
            postionParentCode: code,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.listAPILoading = false;
          if (res && res.data && res.data.listNewPositionRequest) {
            const tempData = res.data.listNewPositionRequest;
            let parentId = tempData.positionParentId;
            let groupIndex = this.groupList.findIndex(
              (item) => parseInt(item.Originalpos_Id) === parseInt(parentId)
            );
            if (groupIndex > -1 && callingFrom === "mounted") {
              this.selectedPosition = this.groupList[groupIndex].Pos_Code;
            } else {
              // we are restting to no group in old form scenario, cuurently it's removed.
              // this.selectedPosition = "nogroup";
              // let index = this.divisionList.findIndex(
              //   (item) => parseInt(item.Originalpos_Id) === parseInt(parentId)
              // );
              // if (index > -1) {
              //   this.selectedDivision = this.divisionList[index].Pos_Code;
              // }
            }
            if (
              tempData &&
              tempData.openPositiontRequestDetails &&
              tempData.openPositiontRequestDetails.length > 0
            ) {
              this.newPositionList = tempData.openPositiontRequestDetails;
              this.originalList = tempData.openPositiontRequestDetails;
              if (this.searchValue && this.searchValue.length > 0) {
                this.onApplySearch(this.searchValue);
              }
              this.$emit("enable-filter", true);
            } else {
              this.$emit("enable-filter", false);
              this.newPositionList = [];
              this.originalList = [];
            }
            if (tempData && tempData.positionParentId) {
              this.selectedPositionParentId = tempData.positionParentId || "";
            } else {
              this.selectedPositionParentId =
                this.tempSelectedOriginalPositionId?.toString() || "";
            }
            if (tempData && tempData.groupCode) {
              this.selectedPositionId = tempData.groupCode?.toString() || "";
              this.$emit("apply-selected-group", tempData.groupCode);
            }
          } else {
            this.$emit("enable-filter", false);
            this.newPositionList = [];
            this.originalList = [];
            this.selectedPositionParentId =
              this.tempSelectedOriginalPositionId?.toString() || "";
          }
        })
        .catch(() => {
          this.listAPILoading = false;
          this.newPositionList = [];
          this.originalList = [];
          this.$emit("enable-filter", false);
        });
    },
    retrieveWorkflowDetails(workflowFlag) {
      let vm = this;
      this.isLoading = true;
      vm.selectedWorkflow = null;
      vm.workflowLoading = true;
      vm.dropdownWorkflow = [];
      vm.$apollo
        .query({
          query: LIST_WORKFLOW,
          variables: {
            employeeId: vm.loginEmployeeId,
            searchString: "",
            moduleId: ["11"],
            isDropDownCall: 1,
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((workflowList) => {
          this.isLoading = false;
          if (
            workflowList &&
            workflowList.data &&
            workflowList.data.listWorkflow &&
            workflowList.data.listWorkflow.Workflows &&
            workflowList.data.listWorkflow.Workflows.length > 0
          ) {
            this.dropdownWorkflow = workflowList.data.listWorkflow.Workflows;
            if (
              this.selectedPositionData &&
              this.selectedPositionData.Workflow_Id
            ) {
              this.selectedWorkflow = this.selectedPositionData.Workflow_Id;
              if (
                (this.dropdownWorkflow && this.dropdownWorkflow.length === 1) ||
                workflowFlag
              ) {
                this.submitNewPosition();
              } else {
                this.showPreForm = true;
              }
            } else {
              if (this.dropdownWorkflow && this.dropdownWorkflow.length === 1) {
                this.selectedWorkflow = this.dropdownWorkflow[0].Workflow_Id;
                this.submitNewPosition();
              } else {
                this.selectedWorkflow = this.dropdownWorkflow[0].Workflow_Id;
                this.showPreForm = true;
              }
            }
            this.workflowLoading = false;
          } else {
            this.workflowLoading = false;
          }
        })
        .catch(() => {
          this.isLoading = false;
          this.handleRetrieveWorkflowDetailsErrors();
          this.workflowLoading = false;
        });
    },
    async validateWorkflowForm() {
      const { valid } = await this.$refs.workflowForm.validate();
      if (valid) {
        this.submitNewPosition();
      }
    },
    submitNewPosition() {
      let requestPayload = {};
      if (
        this.tempPositionData &&
        this.tempPositionData.status &&
        this.tempPositionData.status === "Draft"
      ) {
        requestPayload = this.tempPositionData;
        if (
          this.selectedPositionData &&
          this.selectedPositionData.Position_Request_Id
        ) {
          requestPayload.positionRequestId =
            this.selectedPositionData.Position_Request_Id;
        }
        requestPayload.status = "Waiting For Approval";
      } else {
        requestPayload = this.newPositionFormData;
      }
      if (this.selectedPositionData) {
        if (this.selectedPositionData.License_Certificate) {
          requestPayload.licenseCertificate =
            this.selectedPositionData.License_Certificate;
        }
        if (this.selectedPositionData.Organization_Structure_Id) {
          requestPayload.organizationStructureId =
            this.selectedPositionData.Organization_Structure_Id;
        }
        if (
          this.selectedPositionData.Workflow_Id &&
          this.selectedPositionData.Event_Id
        ) {
          requestPayload.workflowId = this.selectedPositionData.Workflow_Id;
          requestPayload.eventId = this.selectedPositionData.Event_Id;
        }
      }
      this.isLoading = true;
      this.showPreForm = false;
      const workflowData = this.dropdownWorkflow.find(
        (item) => item.Workflow_Id === this.selectedWorkflow
      );
      if (workflowData && workflowData.Workflow_Id) {
        requestPayload.workflowId = workflowData?.Workflow_Id;
        requestPayload.eventId = workflowData?.Event_Id;
      }
      this.$apollo
        .mutate({
          mutation: ADD_EDIT_NEW_POSITION_REQUEST,
          client: "apolloClientAH",
          fetchPolicy: "no-cache",
          variables: requestPayload,
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.addUpdateNewPosition &&
            res.data.addUpdateNewPosition.message
          ) {
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: res.data.addUpdateNewPosition.message,
            };
            this.showNewPositionForm = false;
            this.showAlert(snackbarData);
            this.retrieveNewPositionList();
            this.showLicenseCommentsForm = false;
            this.selectedPositionData = {};
          } else {
            this.handleRetrieveWorkflowDetailsErrors();
          }
        })
        .catch((err) => {
          this.handleRetrieveWorkflowDetailsErrors(err);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    closeViewDetails() {
      this.showViewDetails = false;
    },
    // job assessment form end here
    submitJobAssessment() {
      this.showJobAssessmentForm = false;
      this.showEducationExperienceForm = true;
    },

    closeJobAssessmentForm() {
      this.showJobAssessmentForm = false;
    },
    submitEducationalForm() {
      this.closeEducationForm();
      this.showLicenseCommentsForm = true;
    },
    closeEducationForm() {
      this.showEducationExperienceForm = false;
    },
    onGotoDuties() {
      this.showJobAssessmentForm = true;
      this.closeEducationForm();
    },
    gotoNextDuties(payload) {
      this.newPositionFormData = payload;
      this.tempPositionData = payload;
      this.onCloseNewPosition();
      this.showJobAssessmentForm = true;
    },
    onGotoNextEducation() {
      this.closeJobAssessmentForm();
      this.showEducationExperienceForm = true;
    },
    onGotoNextLicense() {
      this.closeEducationForm();
      this.showLicenseCommentsForm = true;
    },
    submitLicenseForm() {
      this.showLicenseCommentsForm = false;
    },
    closeLicenseForm() {
      this.showLicenseCommentsForm = false;
    },
    onGotoEducation() {
      this.showEducationExperienceForm = true;
      this.showLicenseCommentsForm = false;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleRetrieveWorkflowDetailsErrors(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action:
            this.selectedPositionData &&
            this.selectedPositionData.Position_Request_Id
              ? "updating"
              : "adding",
          form: "new position request details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: validationMessages[0],
          };
          this.showAlert(snackbarData);
        })
        .catch(() => {});
    },
    exportReportFile() {
      let itemList = this.newPositionList.map((item) => ({
        Position_Title: item.Position_Title,
        Pos_Code: item.Pos_Code,
        Group_Code: item.Group_Code,
        Group_Name: item.Group_Name,
        Division_Name: item.Division_Name,
        Division_Code: item.Division_Code,
        Department_Name: item.Department_Name,
        Department_Code: item.Department_Code,
        Section_Name: item.Section_Name,
        Section_Code: item.Section_Code,
        Position_Level: item.Position_Level,
        Cost_Code: item.Cost_Center,
        Employee_Type: item.Employee_Type_Name,
        No_Of_Position: item.No_Of_Position,
        Status: item.Status,
      }));
      let fileName = "New Position & Additional Headcount List";
      let exportHeaders = [
        {
          header: "Position Title",
          key: "Position_Title",
        },
        {
          header: "Position Code",
          key: "Pos_Code",
        },
        {
          header: "Group Name",
          key: "Group_Name",
        },
        {
          header: "Group Code",
          key: "Group_Code",
        },
        {
          header: "Division Name",
          key: "Division_Name",
        },
        { header: "Division Code", key: "Division_Code" },
        {
          header: "Department Name",
          key: "Department_Name",
        },
        { header: "Department Code", key: "Department_Code" },
        {
          header: "Section Name",
          key: "Section_Name",
        },
        { header: "Section Code", key: "Section_Code" },
        { header: "Position Level", key: "Position_Level" },
        { header: "Cost Center", key: "Cost_Code" },
        { header: "Employee Type", key: "Employee_Type" },
        {
          header: "No of Positions",
          key: "No_Of_Position",
        },
        { header: "Status", key: "Status" },
      ];
      let exportOptions = {
        fileExportData: itemList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    closeCommentWindow() {
      this.openCommentForm = false;
      this.comment = "";
    },
    closeCustomEmail() {
      this.templateData = "";
      this.templateType = "";
      this.comment = "";
      this.selectedItem = null;
      this.openCustomEmail = false;
    },
    async validateForm() {
      let { valid } = await this.$refs.reviewForm.validate();
      if (valid) {
        this.openCommentForm = false;
        this.openCustomEmail = true;
        this.templateData = {
          ...this.templateData,
          Remark: this.comment,
        };
      }
    },
    emailSuccess() {
      this.openCustomEmail = false;
      this.updatePositionStatus();
    },
    updatePositionStatus() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_POSITION_STATUS,
          variables: {
            positionRequestId: vm.selectedItem.Position_Request_Id,
            status: vm.selectedAction,
            comments: this.comment,
          },
          client: "apolloClientAH",
        })
        .then((response) => {
          if (response && response.data && !response.data.errorCode) {
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Position status updated successfully",
            };
            this.showAlert(snackbarData);
            this.retrieveNewPositionList();
          }
          vm.isLoading = false;
          this.comment = "";
          this.templateData = {};
          this.selectedItem = null;
          this.templateType = "";
        })
        .catch((error) => {
          vm.isLoading = false;
          this.handleStatusUpdateError(error);
        });
    },
    handleStatusUpdateError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "new position & additional headcount request details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message:
              validationMessages &&
              validationMessages.length &&
              validationMessages[0],
          };
          this.showAlert(snackbarData);
        });
    },
  },
};
</script>
<style scoped>
.overlay-custom-center .v-overlay__content {
  display: contents !important;
}
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
.footer {
  height: 7%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
}
</style>
