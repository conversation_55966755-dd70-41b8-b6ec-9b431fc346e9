<template>
  <div>
    <AppTopBarTab
      :tabs-list="mainTabList"
      :tab-count="tabCountList"
      :current-tab="currentTabItem"
      :center-tab="true"
      @tab-clicked="onTabChange($event)"
      :showBottomSheet="false"
    >
      <template #topBarContent> </template>
    </AppTopBarTab>
    <v-container fluid>
      <v-window v-model="currentTabItem" id="approvals-tab" v-if="isSuperAdmin">
        <v-window-item
          :value="currentTabItem"
          class="pa-3"
          v-if="
            flowList &&
            Object.values(flowList) &&
            Object.values(flowList).length
          "
        >
          <v-card class="rounded-lg mt-15 pa-3 mb-3">
            <v-card-title class="">
              <div class="d-flex justify-space-between flex-wrap">
                <div class="d-flex">
                  <div class="text-center font-weight-medium">
                    Default Hiring Flow
                  </div>
                  <v-badge color="primary" content="Default" inline></v-badge>
                </div>
                <div>
                  <v-btn rounded="lg" color="primary" @click="onEnableEdit()">
                    Edit
                  </v-btn>
                </div>
              </div>
            </v-card-title>
          </v-card>
          <v-stepper alt-labels class="rounded-lg" color="primary">
            <v-stepper-header
              non-linear
              :class="isMobileView ? 'flex-column' : ''"
            >
              <template
                v-for="(flowItem, index) in flowList"
                :key="`${index}-step`"
              >
                <v-stepper-item
                  style="position: relative"
                  value=""
                  :class="isMobileView ? 'mb-2 w-100' : ''"
                >
                  <template v-slot:title>
                    {{ flowItem[0].Stage }}
                  </template>
                </v-stepper-item>
                <v-divider
                  :key="index"
                  v-if="
                    Object.values(flowList).length !== flowItem[0].Order &&
                    !isMobileView
                  "
                ></v-divider>
              </template>
            </v-stepper-header>
          </v-stepper>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <ConfigureHiringFlow
      @onCloseFlow="onCloseFlow()"
      :addHiringFlow="addHiringFlow"
      :flowList="flowList"
      @refresh-list="getStageList()"
    />
    <AppLoading v-if="stageLoader"></AppLoading>
  </div>
</template>
<script>
import { GET_STATUS_LIST } from "@/graphql/settings/Integration/statusHiringFlowQueries";
import ConfigureHiringFlow from "./ConfigureHiringFlow.vue";

export default {
  name: "DefaultHiringFlow",
  data() {
    return {
      tempList: [],
      currentTabItem: "tab-0",
      mainTabList: ["Hiring Flow"],
      addHiringFlow: false,
      flowList: {},
      stageLoader: false,
    };
  },
  components: {
    ConfigureHiringFlow,
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("282");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  mounted() {
    this.getStageList();
  },
  methods: {
    onEnableEdit() {
      this.addHiringFlow = true;
    },
    onCloseFlow() {
      this.addHiringFlow = false;
    },
    getStageList() {
      this.stageLoader = true;
      this.$apollo
        .query({
          query: GET_STATUS_LIST,
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
          variables: {
            formId: 282,
            conditions: [
              {
                key: "Form_Id",
                value: ["16"],
              },
            ],
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getAtsStatusList &&
            response.data.getAtsStatusList.statusList
          ) {
            let tempData = response.data.getAtsStatusList.statusList;
            const groupedByStage = tempData.reduce((acc, item) => {
              if (!acc[item.Stage]) {
                acc[item.Stage] = [];
              }
              acc[item.Stage].push(item);
              return acc;
            }, {});
            const sortedGroupedByStage = Object.fromEntries(
              Object.entries(groupedByStage).sort(
                ([, a], [, b]) => a[0].Order - b[0].Order
              )
            );
            this.flowList = sortedGroupedByStage;
          } else {
            this.flowList = {};
          }
          this.stageLoader = false;
        })
        .catch((err) => {
          this.stageLoader = false;
          this.handleRetrieveHiringFlow(err);
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleRetrieveHiringFlow(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "Hiring Flow",
        isListError: false,
      });
    },
  },
};
</script>
