<template>
  <div v-if="mainTabs.length > 0">
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row v-if="isFilterEnable" style="width: 100%">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end"
              :isDefaultFilter="false"
              :isFilter="false"
              @reset-emp-filter="resetFilter()"
              @apply-emp-filter="applyFilter()"
            />
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
    <v-container fluid class="job-requisition-container">
      <v-window v-model="currentTabItem" v-if="formAccess && formAccess.view">
        <v-window-item :value="currentTabItem">
          <RecruitmentRequestList
            v-if="currentTabItem === 'tab-0'"
            :temp-selected-filter="tempSelectedFilter"
            :divisionList="divisionList"
            :groupList="originalGroupList"
            :tempSelectedOriginalPositionId="tempSelectedOriginalPositionId"
            @apply-selected-group="applySelectedGroup($event)"
            @enable-filter="enableFilter($event)"
            @reset-filter-value="resetFilter()"
            @reset-division-filter="resetDivisionFilter($event)"
          />
          <NewPositionList
            v-if="currentTabItem === 'tab-1'"
            :groupList="originalGroupList"
            :selectedFilterGroup="tempSelectedPosition"
            :divisionList="divisionList"
            :temp-selected-filter="tempSelectedFilter"
            :tempSelectedOriginalPositionId="tempSelectedOriginalPositionId"
            @apply-selected-group="applySelectedGroup($event)"
            @enable-filter="enableFilter($event)"
            @reset-filter-value="resetFilter()"
            @reset-division-filter="resetDivisionFilter($event)"
          />
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied
    ></v-container>
  </div>
</template>
<script>
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu.vue";
import RecruitmentRequestList from "./recruitment-request/RecruitmentRequestList.vue";
import NewPositionList from "./new-position/NewPositionList.vue";
import { LIST_OF_POSITION_LIST } from "@/graphql/mpp/manPowerPlanningQueries";
import { ORG_STRUCTURE_BASED_ON_GROUP } from "@/graphql/mpp/newPositionQueries";

export default {
  name: "JobRequisition",
  data() {
    return {
      mainTabs: ["Approved & Forecasted Positions"],
      currentTabItem: "",
      isFilterEnable: false,
      listLoading: false,
      isLoading: false,
      isErrorInList: false,
      showSearch: false,
      positionListLoading: false,
      errorContent: "",
      itemList: [],
      originalList: [],
      selectedParentPosition: "",
      selectedPosition: "",
      tempSelectedPosition: null,
      tempSelectedFilter: { group: null, division: null },
      tempSelectedOriginalPositionId: null,
      positionGroup: [],
      originalGroupList: [],
      divisionList: [],
      selectedDivision: "",
      tempSelectedDivision: null,
      divisionListLoading: false,
      jobRequisitionLimitToCallAPI: 10000,
      apiCallCount: 0,
      totalApiCount: 0,
    };
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    formAccess() {
      let formAccess = this.accessRights("289");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    recruitmentRequestFormAccess() {
      let formAccess = this.accessRights("291");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    newPositionFormAccess() {
      let formAccess = this.accessRights("290");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
  },
  components: {
    RecruitmentRequestList,
    EmployeeDefaultFilterMenu,
    NewPositionList,
  },
  mounted() {
    if (this.formAccess && this.formAccess.view) {
      this.mainTabs = [
        "Approved & Forecasted Positions",
        "New Position & Additional Headcount",
        "Approvals",
      ];
      if (this.$route.query && this.$route.query.formId) {
        this.currentTabItem =
          this.$route.query.formId === "291" ? "tab-0" : "tab-1";
        this.retrievePositionList();
      } else {
        this.currentTabItem = "tab-0";
        this.retrievePositionList();
      }
      this.retrieveCountGroupPosition();
    } else {
      this.currentTabItem = "tab-0";
    }
  },
  methods: {
    onTabChange(value) {
      if (this.mainTabs.indexOf(value) == 2) {
        if (this.currentTabItem === "tab-0") {
          this.$router.push("/approvals/approval-management?form_id=291");
        } else if (this.currentTabItem === "tab-1") {
          this.$router.push("/approvals/approval-management?form_id=290");
        }
      } else {
        this.currentTabItem = "tab-" + this.mainTabs.indexOf(value);
        this.retrievePositionList();
        this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
        this.selectedPosition = null;
        this.tempSelectedPosition = null;
        this.tempSelectedFilter = { group: null, division: null };
        this.tempSelectedDivision = null;
        this.selectedDivision = null;
        this.tempSelectedOriginalPositionId = null;
        this.retrieveCountGroupPosition();
      }
    },
    applySelectedGroup(val) {
      const result = this.positionGroup.find((item) => item.Pos_Code === val);
      if (result && result.Pos_Code) this.selectedPosition = result.Pos_Code;
      else this.selectedDivision = val;
    },
    enableFilter(val) {
      this.isFilterEnable = val;
    },
    resetDivisionFilter(val) {
      this.selectedDivision = "";
      this.selectedPosition = val;
      if (val) {
        this.retrieveCountGroupPosition();
      }
    },
    resetFilter() {
      this.selectedPosition = "";
      this.tempSelectedPosition = "";
      this.tempSelectedFilter = { group: "", division: "" };
      this.tempSelectedDivision = "";
      this.selectedDivision = "";
      this.tempSelectedOriginalPositionId = "";
    },
    applyFilter() {
      this.tempSelectedPosition = this.selectedPosition || "";
      this.tempSelectedFilter = {
        group: this.selectedPosition || "",
        division: this.selectedDivision || "",
      };
      this.tempSelectedDivision = this.selectedDivision || "";
      this.tempSelectedOriginalPositionId =
        this.positionGroup
          .find((item) => item.Pos_Code === this.selectedPosition)
          ?.Originalpos_Id?.toString() || "";
    },
    retrievePositionList() {
      if (this.formAccess && this.formAccess.view) {
        if (
          this.currentTabItem === "tab-0"
            ? this.recruitmentRequestFormAccess &&
              this.recruitmentRequestFormAccess.view
            : this.newPositionFormAccess && this.newPositionFormAccess.view
        ) {
          this.positionListLoading = true;
          this.$apollo
            .query({
              query: LIST_OF_POSITION_LIST,
              client: "apolloClientA",
              fetchPolicy: "no-cache",
              variables: {
                Form_Id: this.currentTabItem === "tab-0" ? 291 : 290,
                conditions: [
                  {
                    key: "Org_Level",
                    operator: "=",
                    value: "GRP",
                  },
                ],
              },
            })
            .then((res) => {
              if (
                res &&
                res.data &&
                res.data.jobTitleList &&
                res.data.jobTitleList.jobTitleResult
              ) {
                const tempGroupList = res.data.jobTitleList.jobTitleResult;
                this.positionGroup = tempGroupList;
                this.originalGroupList = [
                  {
                    Pos_Name: "No Group",
                    Pos_Code: "nogroup",
                    Pos_full_Name: "No Group",
                    Originalpos_Id: "",
                  },
                ].concat(tempGroupList);
              } else {
                this.originalGroupList = [];
                this.positionGroup = [];
              }
            })
            .catch((err) => {
              this.positionGroup = [];
              this.originalGroupList = [];
              this.handleRetrieveError(err);
            })
            .finally(() => {
              this.positionListLoading = false;
            });
        }
      }
    },
    updateGroup() {
      if (
        this.selectedPosition &&
        this.selectedPosition.toLowerCase() === "nogroup"
      ) {
        this.selectedDivision = null;
        this.retrieveCountGroupPosition();
      }
    },
    retrieveCountGroupPosition() {
      const positionData = this.positionGroup.find(
        (item) => item.Pos_Code === this.selectedPosition
      );
      const divisionData = this.divisionList.find(
        (item) => item.Pos_Code === this.selectedDivision
      );
      this.divisionListLoading = true;
      this.selectedDivision = null;
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: this.currentTabItem === "tab-0" ? 291 : 290,
            postionParentId:
              divisionData && divisionData.Originalpos_Id
                ? divisionData.Originalpos_Id.toString() || ""
                : positionData?.Originalpos_Id?.toString() || "",
            limit: this.jobRequisitionLimitToCallAPI,
            offset: 0,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data && res.data.listDetailsBasedOnGroupCode) {
            if (res.data.listDetailsBasedOnGroupCode.positionDetails) {
              const tempData =
                res.data.listDetailsBasedOnGroupCode.positionDetails;
              this.divisionList = tempData.divisionList || [];
            } else {
              this.divisionList = [];
            }
            let { totalRecords } = res.data.listDetailsBasedOnGroupCode;
            if (totalRecords > 0) {
              totalRecords = parseInt(totalRecords);
              this.apiCallCount = 1;
              this.totalApiCount = Math.ceil(
                totalRecords / this.jobRequisitionLimitToCallAPI
              );
              for (let i = 1; i < this.totalApiCount; i++) {
                this.updateGroupPosition(i);
              }
            }
            this.divisionListLoading = false;
          } else {
            this.divisionListLoading = false;
          }
        })
        .catch((err) => {
          this.handleRetrieveError(err);
          this.divisionList = [];
          this.divisionListLoading = false;
        });
    },
    updateGroupPosition(index = 1) {
      this.divisionListLoading = true;
      const positionData = this.positionGroup.find(
        (item) => item.Pos_Code === this.selectedPosition
      );
      const divisionData = this.divisionList.find(
        (item) => item.Pos_Code === this.selectedDivision
      );
      let apiOffset = parseInt(index) * this.jobRequisitionLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      this.isFormDirty = true;
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: this.currentTabItem === "tab-0" ? 291 : 290,
            postionParentId:
              divisionData && divisionData.Originalpos_Id
                ? divisionData.Originalpos_Id.toString() || ""
                : positionData?.Originalpos_Id?.toString() || "",
            limit: this.jobRequisitionLimitToCallAPI,
            offset: apiOffset,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.listDetailsBasedOnGroupCode &&
            res.data.listDetailsBasedOnGroupCode.positionDetails
          ) {
            const tempData =
              res.data.listDetailsBasedOnGroupCode.positionDetails;
            if (tempData.divisionList && tempData.divisionList.length > 0) {
              this.divisionList = [
                ...this.divisionList,
                ...tempData.divisionList,
              ];
            }
            this.apiCallCount = this.apiCallCount + 1;
            if (this.totalApiCount === this.apiCallCount) {
              this.divisionListLoading = false;
            }
          } else {
            this.divisionListLoading = false;
          }
        })
        .catch((err) => {
          this.handleRetrieveError(err);
          this.divisionList = [];
          this.divisionListLoading = false;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleRetrieveError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "fetching",
          form: "Man power planning",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: validationMessages[0],
          };
          this.showAlert(snackbarData);
        });
    },
  },
};
</script>
<style scoped>
.job-requisition-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .job-requisition-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
