/**
 * Universal Email Content Fixer
 * Fixes common email formatting issues dynamically
 */

function fixEmailContent(htmlContent) {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return htmlContent;
  }

  let fixedContent = htmlContent;

  // 1. Remove problematic rich text editor elements
  fixedContent = removeRichTextEditorArtifacts(fixedContent);

  // 2. Fix list formatting issues
  fixedContent = fixListFormatting(fixedContent);

  // 3. Add proper email styling
  fixedContent = addEmailStyling(fixedContent);

  // 4. Fix general formatting issues
  fixedContent = fixGeneralFormatting(fixedContent);

  return fixedContent;
}

/**
 * Remove rich text editor artifacts that cause display issues
 */
function removeRichTextEditorArtifacts(content) {
  return content
    // Remove Quill editor UI elements
    .replace(/<span class="ql-ui"[^>]*><\/span>/g, '')
    .replace(/<span class="ql-ui"[^>]*>.*?<\/span>/g, '')
    
    // Remove contenteditable attributes
    .replace(/contenteditable="[^"]*"/g, '')
    
    // Remove empty spans and divs
    .replace(/<span[^>]*><\/span>/g, '')
    .replace(/<div[^>]*><\/div>/g, '')
    
    // Clean up multiple consecutive spaces
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Fix list formatting issues
 */
function fixListFormatting(content) {
  // Fix ordered lists with center alignment
  content = content.replace(
    /<ol([^>]*)>/g, 
    (match, attributes) => {
      // Remove text-align: center from ol attributes
      let cleanAttributes = attributes.replace(/style="[^"]*text-align:\s*center[^"]*"/g, '');
      return `<ol${cleanAttributes} style="text-align: left; margin: 15px 0; padding-left: 30px;">`;
    }
  );

  // Fix unordered lists with center alignment
  content = content.replace(
    /<ul([^>]*)>/g, 
    (match, attributes) => {
      let cleanAttributes = attributes.replace(/style="[^"]*text-align:\s*center[^"]*"/g, '');
      return `<ul${cleanAttributes} style="text-align: left; margin: 15px 0; padding-left: 30px;">`;
    }
  );

  // Fix list items with center alignment
  content = content.replace(
    /<li([^>]*style="[^"]*text-align:\s*center[^"]*"[^>]*)>/g,
    (match, attributes) => {
      let cleanAttributes = attributes.replace(/text-align:\s*center;?/g, '');
      return `<li${cleanAttributes} style="margin: 8px 0; padding: 4px 0; text-align: left;">`;
    }
  );

  // Add proper styling to list items that don't have style attributes
  content = content.replace(
    /<li(?![^>]*style=)([^>]*)>/g,
    '<li$1 style="margin: 8px 0; padding: 4px 0; text-align: left;">'
  );

  return content;
}

/**
 * Add proper email styling
 */
function addEmailStyling(content) {
  // Check if content already has a container
  const hasContainer = content.includes('email-container') || content.includes('max-width');
  
  if (!hasContainer) {
    // Wrap content in email container
    content = `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; line-height: 1.6; padding: 20px;">
        ${content}
      </div>
    `;
  }

  // Add CSS styles for better email rendering
  const emailStyles = `
    <style>
      .email-container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; }
      .email-container p { margin: 10px 0; }
      .email-container ol, .email-container ul { text-align: left; margin: 15px 0; padding-left: 30px; }
      .email-container li { margin: 8px 0; padding: 4px 0; text-align: left; }
      .email-container a { color: #0066cc; text-decoration: none; }
      .email-container a:hover { text-decoration: underline; }
      .email-container strong { font-weight: bold; }
      .email-container img { max-width: 100%; height: auto; }
      
      /* Fix for Outlook */
      table { border-collapse: collapse; }
      .email-container table { width: 100%; }
      
      /* Mobile responsive */
      @media only screen and (max-width: 600px) {
        .email-container { padding: 10px !important; }
        .email-container ol, .email-container ul { padding-left: 20px !important; }
      }
    </style>
  `;

  // Add styles at the beginning if not already present
  if (!content.includes('<style>')) {
    content = emailStyles + content;
  }

  return content;
}

/**
 * Fix general formatting issues
 */
function fixGeneralFormatting(content) {
  return content
    // Fix multiple consecutive <br> tags
    .replace(/(<br\s*\/?>){3,}/g, '<br><br>')
    
    // Fix empty paragraphs
    .replace(/<p[^>]*>\s*<\/p>/g, '')
    
    // Ensure proper spacing around paragraphs
    .replace(/<p([^>]*)>/g, '<p$1 style="margin: 10px 0;">')
    
    // Fix images without proper styling
    .replace(/<img([^>]*?)(?!.*style=)([^>]*)>/g, '<img$1 style="max-width: 100%; height: auto;"$2>')
    
    // Fix links without proper styling
    .replace(/<a([^>]*?)(?!.*style=)([^>]*)>/g, '<a$1 style="color: #0066cc; text-decoration: none;"$2>')
    
    // Clean up extra whitespace
    .replace(/>\s+</g, '><')
    .trim();
}

/**
 * Vue.js mixin version for easy integration
 */
const EmailContentFixerMixin = {
  methods: {
    fixEmailContent(htmlContent) {
      return fixEmailContent(htmlContent);
    }
  }
};

/**
 * React hook version
 */
function useEmailContentFixer() {
  const fixContent = (htmlContent) => {
    return fixEmailContent(htmlContent);
  };

  return { fixContent };
}

// Export for different module systems
if (typeof module !== 'undefined' && module.exports) {
  // Node.js
  module.exports = {
    fixEmailContent,
    EmailContentFixerMixin,
    useEmailContentFixer
  };
} else if (typeof window !== 'undefined') {
  // Browser
  window.EmailContentFixer = {
    fixEmailContent,
    EmailContentFixerMixin,
    useEmailContentFixer
  };
}

// Example usage:
/*
// Basic usage
const fixedContent = fixEmailContent(originalHtmlContent);

// Vue.js usage
export default {
  mixins: [EmailContentFixerMixin],
  methods: {
    sendEmail() {
      const fixedContent = this.fixEmailContent(this.emailContent);
      // Send email with fixedContent
    }
  }
}

// React usage
function EmailComponent() {
  const { fixContent } = useEmailContentFixer();
  
  const handleSendEmail = () => {
    const fixedContent = fixContent(emailContent);
    // Send email with fixedContent
  };
}
*/
