<template>
  <div class="rounded-lg ma-1" color="#FDFEFF">
    <div v-if="!showEditForm">
      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="blue"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="text-h6 text-grey-darken-1 font-weight-bold"
            >Personal Details</span
          >
        </div>
        <div v-if="enableEdit">
          <v-btn @click="openEditDialog" color="primary" variant="text">
            <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
          </v-btn>
        </div>
      </div>

      <v-row class="pa-4 ma-2 card-blue-background">
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Employee Id</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.User_Defined_EmpId) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Biometric Integration Id
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.External_EmpId) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Salutation</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Salutation) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">First Name</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Emp_First_Name) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Middle Name</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Emp_Middle_Name) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Last Name</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Emp_Last_Name) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Suffix</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Appellation) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Gender</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Gender) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList[348]?.Field_Visiblity?.toLowerCase() === 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[348].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Gender_Expression) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList[347]?.Field_Visiblity?.toLowerCase() === 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[347].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Gender_Identity) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList[207].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[207].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Pronoun) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList[208].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[208].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Gender_Orientations) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[399]?.Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.maritalStatusName) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Date of Birth</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ formatDate(personalDetails.DOB) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList[401]?.Field_Visiblity?.toLowerCase() === 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[401]?.Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Blood_Group) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Nationality</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Nationality) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList['212'] && labelList['212'].Field_Visiblity === 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[212].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Aadhaar_Card_Number) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList['213'] && labelList['213'].Field_Visiblity === 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[213].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.PAN) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Personal Email</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Personal_Email) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList['214'] && labelList['214'].Field_Visiblity === 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[214].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.UAN) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList['188'] && labelList['188'].Field_Visiblity === 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[188].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Tax_Code) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Manager</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ parseInt(personalDetails.Is_Manager) ? "Yes" : "No" }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Recruiter</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ personalDetails.Is_Recruiter }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Allow User Sign In</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ parseInt(personalDetails.Allow_User_Signin) ? "Yes" : "No" }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="labelList[295].Field_Visiblity == 'Yes'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Place Of Birth</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Place_Of_Birth) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[232].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Emp_Pref_First_Name) }}
          </p>
        </v-col>

        <v-col
          v-if="labelList[380]?.Field_Visiblity?.toLowerCase() === 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[380]?.Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              personalDetails.Languages && personalDetails.Languages.length > 0
                ? formLanguagesNames(personalDetails.Languages)
                : "-"
            }}
          </p>
        </v-col>

        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Hobbies</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Hobbies) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList[242]?.Field_Visiblity.toLowerCase() == 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[242].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Caste) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList[276]?.Field_Visiblity.toLowerCase() == 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[276].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Ethnic_Race) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="labelList[296].Field_Visiblity == 'Yes'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Religion</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Religion) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList[382]?.Field_Visiblity?.toLowerCase() === 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[382]?.Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ parseInt(personalDetails.Physically_Challenged) ? "Yes" : "No" }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Military Service</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ parseInt(personalDetails.Military_Service) ? "Yes" : "No" }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Smoker</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ parseInt(personalDetails.Smoker) ? "Yes" : "No" }}
          </p>
        </v-col>
        <v-col v-if="parseInt(personalDetails.Smoker)" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Smoker As Of</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ formatDate(personalDetails.Smokerasof) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList['223'] && labelList['223'].Field_Visiblity === 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["223"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.Statutory_Insurance_Number) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList['224'] && labelList['224'].Field_Visiblity === 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["224"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(personalDetails.PRAN_No) }}
          </p>
        </v-col>
      </v-row>
      <div
        class="d-flex align-center justify-space-between mb-2"
        v-if="labelList[375]?.Field_Visiblity?.toLowerCase() === 'yes'"
      >
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="danger"
            :size="18"
            class="mx-2"
          ></v-progress-circular>
          <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold"
            >Language(s) Known</span
          >
        </div>
      </div>
      <v-row v-if="labelList[375]?.Field_Visiblity?.toLowerCase() === 'yes'">
        <v-slide-group
          class="pa-4"
          selected-class="bg-primary"
          prev-icon="fas fa-chevron-circle-left"
          next-icon="fas fa-chevron-circle-right"
          show-arrows
        >
          <v-slide-group-item>
            <v-card
              v-for="(lang, index) in personalDetails.Languages"
              :key="index"
              class="card-item d-flex pa-4 rounded-lg mr-4 mb-1"
              color="grey-lighten-5"
              :style="
                !isMobileView
                  ? `width:450px; border-left: 7px solid ${generateRandomColor()}; height:auto; `
                  : `overflow:visible;border-left: 7px solid ${generateRandomColor()}`
              "
            >
              <v-row>
                <v-col
                  v-if="
                    labelList[375]?.Field_Visiblity?.toLowerCase() === 'yes'
                  "
                  cols="12"
                  class="pa-0 pl-4 d-flex"
                >
                  <v-tooltip :text="lang?.Language_Name" location="bottom">
                    <template v-slot:activator="{ props }">
                      <div
                        class="text-primary font-weight-bold text-subtitle-1 text-truncate"
                        :style="
                          isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                        "
                        v-bind="lang?.Language_Name ? props : ''"
                      >
                        {{ checkNullValue(lang?.Language_Name) }}
                      </div>
                    </template>
                  </v-tooltip>
                </v-col>
                <v-col
                  v-if="
                    labelList[369]?.Field_Visiblity?.toLowerCase() === 'yes'
                  "
                  cols="12"
                  sm="6"
                  class="pa-0 pl-4"
                >
                  <div
                    class="mt-2 mr-2 d-flex flex-column justify-start text-body-1"
                    style="max-content"
                  >
                    <b class="mr-2 text-grey justify-start">
                      {{ labelList[369]?.Field_Alias }}
                    </b>
                    <span class="pb-1 pt-1">{{
                      lang?.Lang_Spoken ? "Yes" : "No"
                    }}</span>
                  </div>
                </v-col>
                <v-col
                  v-if="
                    labelList[370]?.Field_Visiblity?.toLowerCase() === 'yes'
                  "
                  cols="12"
                  sm="6"
                  class="pa-0 pl-4"
                >
                  <div
                    class="mt-2 mr-2 d-flex flex-column justify-start text-body-1"
                    style="max-content"
                  >
                    <b class="mr-2 text-grey justify-start">
                      {{ labelList[370]?.Field_Alias }}
                    </b>
                    <span class="pb-1 pt-1">{{
                      lang?.Lang_Read_Write ? "Yes" : "No"
                    }}</span>
                  </div>
                </v-col>
                <v-col
                  v-if="
                    labelList[371]?.Field_Visiblity?.toLowerCase() === 'yes'
                  "
                  cols="12"
                  sm="6"
                  class="pa-0 pl-4"
                >
                  <div
                    class="mt-2 mr-2 d-flex flex-column justify-start text-body-1"
                    style="max-content"
                  >
                    <b class="mr-2 text-grey justify-start">
                      {{ labelList[371]?.Field_Alias }}
                    </b>
                    <v-tooltip
                      :text="lang?.Lang_Proficiency"
                      location="bottom"
                      max-width="400"
                    >
                      <template v-slot:activator="{ props }">
                        <span class="pb-1 pt-1" v-bind="props">
                          <div
                            :style="
                              isMobileView
                                ? 'max-width: 200px'
                                : 'max-width:140px'
                            "
                            class="text-truncate"
                          >
                            {{ checkNullValue(lang?.Lang_Proficiency) }}
                          </div></span
                        >
                      </template>
                    </v-tooltip>
                  </div>
                </v-col>
              </v-row>
            </v-card>
          </v-slide-group-item></v-slide-group
        >
      </v-row>
    </div>
    <div v-else>
      <EditPersonalDetails
        ref="editPersonalDetails"
        :personalDetails="personalDetails"
        :actionType="actionType"
        :selectedEmpStatus="selectedEmpStatus"
        :callingFrom="callingFrom"
        :selectedEmpId="selectedEmpId"
        @edit-updated="editUpdated($event)"
        @close-edit-form="closeEditForm"
        @close-add-form="$emit('close-add-form')"
      >
      </EditPersonalDetails>
    </div>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
// components
const EditPersonalDetails = defineAsyncComponent(() =>
  import("./EditPersonalDetails.vue")
);
import { checkNullValue, generateRandomColor } from "@/helper";
import moment from "moment";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "PersonalDetails",
  components: {
    EditPersonalDetails,
  },
  props: {
    personalDetailsData: {
      type: [Array, Object],
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
    actionType: {
      type: String,
      default: "",
    },
    selectedEmpStatus: {
      type: String,
      default: "Active",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
  },
  emits: [
    "refetch-personal-details",
    "edit-opened",
    "edit-closed",
    "close-add-form",
  ],
  data() {
    return {
      showEditForm: false,
      personalDetails: {
        Employee_Id: 0,
        User_Defined_EmpId: "",
        Salutation: null,
        Emp_First_Name: "",
        Emp_Middle_Name: "",
        Emp_Last_Name: "",
        Emp_Pref_First_Name: "",
        Gender: null,
        DOB: null,
        Place_Of_Birth: "",
        Marital_Status: null,
        Blood_Group: null,
        Military_Service: 0,
        Statutory_Insurance_Number: null,
        PRAN_No: null,
        Nationality: "",
        Personal_Email: "",
        Religion: "",
        Caste: "",
        Ethnic_Race: "",
        Is_Manager: 0,
        Physically_Challenged: 0,
        Smoker: 0,
        Smokerasof: null,
        Aadhaar_Card_Number: "",
        PAN: "",
        UAN: "",
        Allow_User_Signin: 0,
        Enable_Sign_In_With_Mobile_No: 0,
        Sign_In_Mobile_Number: "",
        Work_Email: "",
        Sign_In_Mobile_No_Country_Code: "",
        External_EmpId: "",
        Languages: [],
        Hobbies: "",
        Form_Status: "",
        Photo_Path: "",
      },
    };
  },

  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return (
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  watch: {
    showEditForm(val) {
      let editFormOpened =
        this.$store.state.employeeProfile.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.personalDetailsData && this.personalDetailsData.length > 0) {
      this.personalDetails = this.personalDetailsData[0];
    }
    if (this.actionType === "add" && !this.selectedEmpId) {
      this.showEditForm = true;
    }
  },

  methods: {
    checkNullValue,
    generateRandomColor,
    formLanguagesNames(languages) {
      return languages.map((el) => el.Language_Name).join(", ");
    },
    editUpdated(empId) {
      mixpanel.track("EmpProfile-personalDetails-edit-updated");
      this.showEditForm = false;
      this.$emit("refetch-personal-details", empId);
    },
    openEditDialog() {
      mixpanel.track("EmpProfile-personalDetails-edit-opened");
      this.showEditForm = true;
      this.$emit("edit-opened");
    },
    closeEditForm() {
      mixpanel.track("EmpProfile-personalDetails-edit-closed");
      this.showEditForm = false;
      this.$emit("edit-closed");
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
