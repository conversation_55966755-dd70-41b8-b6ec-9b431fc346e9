<template>
  <v-overlay
    :model-value="true"
    @click:outside="
      isFormDirty ? (openConfirmationPopup = true) : onCloseOverlay()
    "
    class="d-flex justify-end"
    persistent="true"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        :style="windowWidth >= 1264 ? '' : 'width: 100vw'"
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center fixed-title"
        >
          <div class="text-h6 text-medium ps-2">
            {{ isEdit ? "Edit" : "Apply" }} {{ landedFormName }}
          </div>
          <v-btn
            icon
            class="clsBtn"
            variant="text"
            @click="openConfirmationPopup = true"
          >
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text
          class="card mb-3 px-0"
          style="overflow-y: auto; padding-bottom: 50px"
        >
          <div class="px-5 py-6">
            <v-form ref="addEditFormValidator">
              <v-row class="px-6 pt-6">
                <!-- Employee Name -->
                <v-col cols="12" sm="6" class="px-md-6 mt-2">
                  <CustomSelect
                    ref="employeeName"
                    v-model="employeeId"
                    :items="listEmployees"
                    itemTitle="text"
                    itemValue="value"
                    :loading="listLoading"
                    :rules="[required('Employee', employeeId)]"
                    :disabled="
                      callingFrom?.toLowerCase() === 'selfservice' || isEdit
                    "
                    label="Employee Name"
                    :is-required="true"
                    clearable
                    variant="solo"
                    :isAutoComplete="true"
                    :itemSelected="employeeId"
                    @selected-item="onChangeFieldType($event, 'Employee_Id')"
                  />
                </v-col>

                <!-- Request For -->
                <v-col cols="12" sm="6" class="px-md-6 mt-2">
                  <CustomSelect
                    ref="requestFor"
                    v-model="requestFor"
                    :items="settingsList"
                    itemTitle="text"
                    itemValue="value"
                    :loading="isLoading"
                    :rules="[required('Request For', requestFor)]"
                    label="Request For"
                    :is-required="true"
                    clearable
                    variant="solo"
                    :isAutoComplete="true"
                    :itemSelected="requestFor"
                    @selected-item="onChangeFieldType($event, 'Request_For')"
                  />
                </v-col>

                <!-- Start Date -->
                <v-col cols="12" sm="6" class="px-md-6 mt-2">
                  <v-menu
                    v-model="startDateMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                  >
                    <template v-slot:activator="{ props }">
                      <v-text-field
                        ref="startDate"
                        v-model="formattedStartDate"
                        prepend-inner-icon="fas fa-calendar"
                        :rules="[required('Start Date', formattedStartDate)]"
                        readonly
                        v-bind="props"
                        variant="solo"
                      >
                        <template v-slot:label>
                          Start Date <span style="color: red">*</span>
                        </template>
                      </v-text-field>
                    </template>
                    <v-date-picker
                      v-model="startDate"
                      :min="minStartDate"
                      @update:model-value="
                        onChangeFieldType($event, 'Start_Date')
                      "
                    />
                  </v-menu>
                </v-col>
                <!-- Start Time -->
                <v-col cols="12" sm="6" class="px-md-6 mt-2">
                  <v-menu
                    v-model="startTimeMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                  >
                    <template v-slot:activator="{ props }">
                      <v-text-field
                        ref="startTime"
                        v-model="formattedStartTime"
                        prepend-inner-icon="fas fa-clock"
                        :rules="[required('Start Time', formattedStartTime)]"
                        readonly
                        :disabled="!startDate"
                        v-bind="props"
                        variant="solo"
                      >
                        <template v-slot:label>
                          Start Time
                          <span style="color: red">*</span>
                        </template>
                      </v-text-field>
                    </template>
                    <v-time-picker
                      v-model="startTime"
                      format="24hr"
                      @update:model-value="
                        onChangeFieldType($event, 'Start_Time')
                      "
                    />
                  </v-menu>
                </v-col>

                <!-- End Date -->
                <v-col cols="12" sm="6" class="px-md-6 mt-2">
                  <v-menu
                    v-model="endDateMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                  >
                    <template v-slot:activator="{ props }">
                      <v-text-field
                        ref="endDate"
                        v-model="formattedEndDate"
                        prepend-inner-icon="fas fa-calendar"
                        :rules="[required('End Date', formattedEndDate)]"
                        readonly
                        :disabled="!startDate"
                        v-bind="props"
                        variant="solo"
                      >
                        <template v-slot:label>
                          End Date
                          <span style="color: red">*</span>
                        </template>
                      </v-text-field>
                    </template>
                    <v-date-picker
                      v-model="endDate"
                      :min="startDate || minStartDate"
                      @update:model-value="
                        onChangeFieldType($event, 'End_Date')
                      "
                    />
                  </v-menu>
                </v-col>

                <!-- End Time -->
                <v-col cols="12" sm="6" class="px-md-6 mt-2">
                  <v-menu
                    v-model="endTimeMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                  >
                    <template v-slot:activator="{ props }">
                      <v-text-field
                        ref="endTime"
                        v-model="formattedEndTime"
                        prepend-inner-icon="fas fa-clock"
                        :rules="[required('End Time', formattedEndTime)]"
                        readonly
                        :disabled="!endDate"
                        v-bind="props"
                        variant="solo"
                      >
                        <template v-slot:label>
                          End Time
                          <span style="color: red">*</span>
                        </template>
                      </v-text-field>
                    </template>
                    <v-time-picker
                      v-model="endTime"
                      format="24hr"
                      @update:model-value="
                        onChangeFieldType($event, 'End_Time')
                      "
                    />
                  </v-menu>
                </v-col>

                <!-- Short Time Off Rules and Balance - Only shown when end time is filled -->
                <template v-if="endDateTime || endTime">
                  <!-- Short Time Off Rules -->
                  <v-col cols="12" sm="6" class="px-md-6">
                    <div class="text-subtitle-1 text-grey-darken-1">
                      Short Time Off Rules
                    </div>
                    <div class="text-subtitle-1 font-weight-regular">
                      <section class="text-body-2">
                        <div v-if="shortTimeOffRules">
                          {{ checkNullValue(shortTimeOffRules) }}
                        </div>
                      </section>
                    </div>
                  </v-col>

                  <!-- Short Time Off Balance -->
                  <v-col cols="12" sm="6" class="px-md-6 mt-2">
                    <div class="text-subtitle-1 text-grey-darken-1">
                      Short Time Off Balance
                    </div>
                    <div class="text-subtitle-1 font-weight-regular">
                      <section class="text-body-2">
                        <div v-if="shortTimeOffBalance !== null">
                          {{ checkNullValue(shortTimeOffBalance) }}
                        </div>
                      </section>
                    </div>
                  </v-col>
                </template>

                <!-- Total Hours -->
                <v-col cols="12" sm="6" class="px-md-6 mt-2">
                  <div class="text-subtitle-1 text-grey-darken-1">
                    Total Hours
                  </div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      <div
                        v-if="
                          formattedTotalHours && formattedTotalHours !== '00:00'
                        "
                      >
                        {{ checkNullValue(formattedTotalHoursText) }}
                      </div>
                      <div class="ml-1" v-else>-</div>
                    </section>
                  </div>
                </v-col>

                <!-- Reason -->
                <v-col cols="12" sm="6" class="px-md-6 mt-2">
                  <v-textarea
                    ref="reason"
                    v-model="reason"
                    variant="solo"
                    auto-grow
                    label="Reason"
                    rows="1"
                    :is-required="true"
                    :rules="[
                      required('Reason', reason),
                      reason
                        ? validateWithRulesAndReturnMessages(
                            reason,
                            'description',
                            'Reason'
                          )
                        : true,
                    ]"
                    @update:model-value="onChangeFieldType($event, 'Reason')"
                  >
                    <template v-slot:label>
                      Reason
                      <span style="color: red">*</span>
                    </template>
                  </v-textarea>
                </v-col>

                <!-- Alternate Person -->
                <v-col cols="12" sm="6" class="px-md-6 mt-2">
                  <CustomSelect
                    ref="Alternate_Person"
                    v-model="alternatePerson"
                    :items="listAlternativeDetails"
                    itemTitle="Employee_Name"
                    itemValue="Manager_Id"
                    :loading="nameChangeLoading"
                    label="Alternate Person"
                    variant="solo"
                    clearable
                    :is-required="false"
                    :isAutoComplete="true"
                    :itemSelected="alternatePerson"
                    :key="'alternate-person-' + (alternatePerson || 'none')"
                    @selected-item="
                      onChangeFieldType($event, 'Alternate_Person')
                    "
                    @update:model-value="
                      onChangeFieldType($event, 'Alternate_Person')
                    "
                  />
                </v-col>
              </v-row>
            </v-form>
          </div>
        </v-card-text>
      </v-card>
      <v-card class="overlay-footer bottom-0 position-sticky w-100">
        <div class="d-flex justify-end pa-4">
          <v-btn
            rounded="lg"
            class="mr-6"
            variant="outlined"
            @click="openConfirmationPopup = true"
          >
            Cancel
          </v-btn>
          <v-btn
            rounded="lg"
            class="mr-1 primary"
            variant="elevated"
            :disabled="isLoading || editedShortTimeOffData.disabledSubmitButton"
            @click="validateCustomFields()"
          >
            Submit
          </v-btn>
        </div></v-card
      >
      <AppLoading v-if="nameChangeLoading"></AppLoading>
    </template>
  </v-overlay>
  <AppLoading v-if="isLoading" />
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="onCloseOverlay()"
  />
</template>

<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import { checkNullValue } from "@/helper";
import moment from "moment";

export default {
  name: "AddEditShortTimeOff",
  data() {
    return {
      minDateToApply: null,
      listLoading: false,
      employeeId: null,
      requestFor: null,
      startDate: null,
      startTime: null,
      startDateTime: null, // Combined start date and time field
      endDate: null,
      endTime: null,
      endDateTime: null, // Combined end date and time field
      totalHours: 0,
      reason: null,
      alternatePerson: null,
      documentUpload: [],
      disabledSubmitButton: false,
      // Formatted fields
      formattedStartDate: "",
      formattedStartTime: "",
      formattedEndDate: "",
      formattedEndTime: "",
      formattedTotalHours: "00:00",

      // Other existing data properties
      startDateMenu: false,
      startTimeMenu: false,
      endDateMenu: false,
      endTimeMenu: false,
      listEmployees: [],
      listAlternativeDetails: [],
      showAddEditForm: true,
      isLoading: false,
      errorsMessages: [],
      isFormDirty: false,
      openConfirmationPopup: false,
      shortTimeOffBalance: null,
      shortTimeOffRules: null,
      // Loaders and lists
      employeesLoading: false,
      nameChangeLoading: false,
      settingsList: [],
      // Form data object
      editedShortTimeOffData: {
        Employee_Id: null,
        Request_For: null,
        Start_Date: null,
        Start_Time: null,
        Start_Date_Time: null, // Combined start date and time field
        End_Date: null,
        End_Time: null,
        End_Date_Time: null, // Combined end date and time field
        Total_Hours: 0,
        Reason: null,
        Alternate_Person: null,
        formattedStartDate: "",
        formattedEndDate: "",
        disabledSubmitButton: false,
      },
      // Menu controls
    };
  },
  components: {
    CustomSelect,
  },
  mixins: [validationRules],
  emits: ["close-form", "edit-updated", "form-updated"],
  props: {
    selectedItem: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    callingFrom: {
      type: String,
      required: true,
    },
    formId: {
      type: Number,
      required: true,
    },
    formAccess: {
      type: Object,
      required: true,
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  computed: {
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    todayDate() {
      return this.formatDate(moment().format("YYYY-MM-DD"));
    },
    minStartDate() {
      // Use minDateToApply if it exists and is a valid date
      if (
        this.minDateToApply &&
        moment(this.minDateToApply, "YYYY-MM-DD", true).isValid()
      ) {
        return moment(this.minDateToApply).format("YYYY-MM-DD");
      }
      // If employeeId is set, find the employee in listEmployees and use their Date_Of_Join
      if (
        this.employeeId &&
        this.listEmployees &&
        this.listEmployees.length > 0
      ) {
        const selectedEmployee = this.listEmployees.find(
          (emp) => emp.value === this.employeeId
        );
        if (selectedEmployee && selectedEmployee.Date_Of_Join) {
          return moment(selectedEmployee.Date_Of_Join).format("YYYY-MM-DD");
        }
      }
      // Fallback to current date if no Date_Of_Join is found
      return moment().format("YYYY-MM-DD");
    },
    formatDate() {
      return (date) => {
        if (moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    formattedTotalHoursText() {
      // Parse the HH:MM format
      const [hours, minutes] = this.formattedTotalHours.split(":").map(Number);

      // Format as "XX Hours YY Minutes"
      return `${hours.toString().padStart(2, "0")} Hrs ${minutes
        .toString()
        .padStart(2, "0")} Mins`;
    },
  },
  watch: {
    "editedShortTimeOffData.Start_Date"(val) {
      if (val) {
        this.editedShortTimeOffData.formattedStartDate = this.formatDate(val);
        this.isFormDirty = true;

        // Auto-sync End Date if it's not set
        if (!this.editedShortTimeOffData.End_Date) {
          this.editedShortTimeOffData.End_Date = val;
          this.endDate = val;
          this.formattedEndDate = this.formatDate(val);
        }

        // Recalculate total hours if we have all required values
        if (
          this.editedShortTimeOffData.Start_Time &&
          this.editedShortTimeOffData.End_Date &&
          this.editedShortTimeOffData.End_Time
        ) {
          this.calculateTotalHours();
        }
      }
    },
    "editedShortTimeOffData.End_Date"(val) {
      if (val) {
        this.editedShortTimeOffData.formattedEndDate = this.formatDate(val);
        this.isFormDirty = true;

        // Recalculate total hours if we have all required values
        if (
          this.editedShortTimeOffData.Start_Date &&
          this.editedShortTimeOffData.Start_Time &&
          this.editedShortTimeOffData.End_Time
        ) {
          this.calculateTotalHours();
        }
      }
    },
    "editedShortTimeOffData.Start_Time"(val) {
      if (val) {
        this.formattedStartTime = val;
        this.isFormDirty = true;

        // Recalculate total hours if we have all required values
        if (
          this.editedShortTimeOffData.Start_Date &&
          this.editedShortTimeOffData.End_Date &&
          this.editedShortTimeOffData.End_Time
        ) {
          this.calculateTotalHours();
        }
      }
    },
    "editedShortTimeOffData.End_Time"(val) {
      if (val) {
        this.formattedEndTime = val;
        this.isFormDirty = true;

        // Recalculate total hours if we have all required values
        if (
          this.editedShortTimeOffData.Start_Date &&
          this.editedShortTimeOffData.Start_Time &&
          this.editedShortTimeOffData.End_Date
        ) {
          this.calculateTotalHours();
        }
      }
    },
    // Add watchers for date and time pickers to close menus after selection
    startDate(val) {
      if (val) {
        this.startDateMenu = false;
        this.formattedStartDate = this.formatDate(val);
      }
    },
    startTime(val) {
      if (val) {
        this.startTimeMenu = false;
        this.formattedStartTime = val;
      }
    },
    endDate(val) {
      if (val) {
        this.endDateMenu = false;
        this.formattedEndDate = this.formatDate(val);
      }
    },
    endTime(val) {
      if (val) {
        this.endTimeMenu = false;
        this.formattedEndTime = val;
      }
    },
    // Watch for reason changes and sync with editedShortTimeOffData
    reason: {
      handler(val) {
        this.editedShortTimeOffData.Reason = val;
      },
      immediate: true,
    },
    // Watch for alternatePerson changes and sync with editedShortTimeOffData
    alternatePerson: {
      handler(val) {
        this.editedShortTimeOffData.Alternate_Person = val;
      },
      immediate: true,
    },
  },
  mounted() {
    if (this.isEdit && this.selectedItem) {
      // In edit mode, prefill the form with the selected item's values
      this.prefillDetails();
    } else {
      // In add mode, set the employee ID to the logged-in employee
      this.editedShortTimeOffData.Employee_Id = String(this.loginEmployeeId);
      this.employeeId = String(this.loginEmployeeId);

      // Load alternate person list for the current employee in add mode
      this.$nextTick(() => {
        this.setDefaultRequestFor();
        this.updateAlternatePerson();
      });
    }

    // Get employee list and update alternate person options
    this.listEmployeeDetails();

    // Short time off balance will be fetched when start time is filled
  },
  methods: {
    checkNullValue,
    onCloseOverlay() {
      this.$emit("close-form");
    },
    setDefaultRequestFor() {
      if (this.settingsList && this.settingsList.length > 0) {
        // Use the settings list to determine the default value
        if (this.settingsList.includes("Permission")) {
          this.requestFor = "Permission";
        } else if (this.settingsList.includes("On Duty")) {
          this.requestFor = "On Duty";
        } else if (this.settingsList.length > 0) {
          // Use the first option from the settings list
          this.requestFor = this.settingsList[0];
        }
      }

      // Update the form data
      this.editedShortTimeOffData.Request_For = this.requestFor;
    },

    prefillDetails() {
      if (!this.selectedItem) return;

      // Format the total hours for display
      const totalHoursValue = parseFloat(this.selectedItem.Total_Hours) || 0;
      const totalHours = Math.floor(totalHoursValue);
      const totalMinutes = Math.round((totalHoursValue - totalHours) * 60);
      const formattedTotalHrs = `${totalHours
        .toString()
        .padStart(2, "0")}:${totalMinutes.toString().padStart(2, "0")}`;
      this.formattedTotalHours = formattedTotalHrs;
      this.totalHours = totalHoursValue;

      // Parse dates and times correctly from combined date-time fields
      let startDate, startTime, endDate, endTime;

      // Handle Start_Date_Time format ("2025-07-05 13:06:00")
      if (this.selectedItem.Start_Date_Time) {
        const startDateTime = moment(this.selectedItem.Start_Date_Time);
        startDate = startDateTime.format("YYYY-MM-DD");
        startTime = startDateTime.format("HH:mm");
      } else {
        // Fallback to separate fields if combined field is not available
        startDate = moment(this.selectedItem.Start_Date).format("YYYY-MM-DD");
        startTime = this.selectedItem.Start_Time;
      }

      // Handle End_Date_Time format ("2025-07-05 13:06:00")
      if (this.selectedItem.End_Date_Time) {
        const endDateTime = moment(this.selectedItem.End_Date_Time);
        endDate = endDateTime.format("YYYY-MM-DD");
        endTime = endDateTime.format("HH:mm");
      } else {
        // Fallback to separate fields if combined field is not available
        endDate = moment(this.selectedItem.End_Date).format("YYYY-MM-DD");
        endTime = this.selectedItem.End_Time;
      }

      // Ensure time values are in HH:MM format
      if (startTime && !startTime.includes(":")) {
        startTime = moment(startTime, "HHmm").format("HH:mm");
      }

      if (endTime && !endTime.includes(":")) {
        endTime = moment(endTime, "HHmm").format("HH:mm");
      }

      // We'll set the alternatePerson value later in the method

      // Populate the editedShortTimeOffData object
      this.editedShortTimeOffData = {
        Employee_Id: String(this.selectedItem.Employee_Id),
        Request_For: this.selectedItem.Request_For,
        Start_Date: startDate,
        Start_Time: startTime,
        Start_Date_Time:
          this.selectedItem.Start_Date_Time || `${startDate} ${startTime}`,
        End_Date: endDate,
        End_Time: endTime,
        End_Date_Time:
          this.selectedItem.End_Date_Time || `${endDate} ${endTime}`,
        Total_Hours: this.selectedItem.Total_Hours,
        Reason: this.selectedItem.Reason,
        // We'll set Alternate_Person after handling the alternatePerson value
      };

      // Set the combined startDateTime and endDateTime fields
      this.startDateTime =
        this.selectedItem.Start_Date_Time || `${startDate} ${startTime}`;
      this.endDateTime =
        this.selectedItem.End_Date_Time || `${endDate} ${endTime}`;

      // Sync with v-model variables
      this.employeeId = String(this.selectedItem.Employee_Id);
      this.requestFor = this.selectedItem.Request_For;
      this.startDate = startDate;
      this.startTime = startTime;
      this.endDate = endDate;
      this.endTime = endTime;
      // Set reason value in both places
      this.reason = this.selectedItem.Reason;
      this.editedShortTimeOffData.Reason = this.selectedItem.Reason;

      // Set the alternatePerson value
      let alternatePersonId = this.selectedItem.Alternate_Person || null;
      this.alternatePerson = alternatePersonId;
      this.editedShortTimeOffData.Alternate_Person = alternatePersonId;

      // Force the CustomSelect component to update by setting a key
      this.$nextTick(() => {
        if (this.$refs.Alternate_Person) {
          this.$refs.Alternate_Person.assignSelectedItem();
        }
      });

      // Format dates for display
      this.formattedStartDate = this.formatDate(new Date(startDate));
      this.formattedEndDate = this.formatDate(new Date(endDate));
      this.formattedStartTime = startTime;
      this.formattedEndTime = endTime;

      // Explicitly set the v-model values for the time pickers
      this.$nextTick(() => {
        // Force update the time pickers
        if (this.$refs.startTime) {
          this.$refs.startTime.value = startTime;
        }
        if (this.$refs.endTime) {
          this.$refs.endTime.value = endTime;
        }

        this.startDateMenu = false;
        this.startTimeMenu = false;
        this.endDateMenu = false;
        this.endTimeMenu = false;

        // Calculate total hours
        this.calculateTotalHours();
      });

      // Get short time off balance for the selected employee
      this.getShortTimeOffBalance();

      // Update alternate person list with a flag to preserve the selected value
      this.updateAlternatePerson(true);

      // Set form as not dirty initially
      this.isFormDirty = false;
    },

    listEmployeeDetails() {
      let vm = this;
      vm.listLoading = true;
      const apiObj = {
        url: vm.baseUrl + "default/employee-info/list-employee-details",
        type: "POST",
        dataType: "json",
        data: {
          _fId: "Short Time Off",
        },
      };

      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res && res.length) {
            vm.listEmployees = res;
            if (vm.listEmployees?.length > 0) {
              const match = vm.listEmployees.find(
                (emp) => emp.value === this.loginEmployeeId.toString()
              );
              vm.userDefinedEmpId = match ? match.User_Defined_EmpId : null;
            }
          } else {
            vm.listEmployees = [];
          }
          vm.listLoading = false;
        })
        .catch(() => {
          vm.listLoading = false;
          vm.listEmployees = [];
        });
    },
    updateAlternatePerson(preserveSelected = false) {
      // Use a local variable for 'this' to avoid scope issues
      const vm = this;
      vm.nameChangeLoading = true;

      // Safety check: ensure editedShortTimeOffData exists
      if (!vm.editedShortTimeOffData) {
        vm.editedShortTimeOffData = {};
      }

      // Check if employeeId is null or undefined
      if (!vm.employeeId) {
        vm.nameChangeLoading = false;
        vm.listAlternativeDetails = [];
        vm.alternatePerson = null;
        vm.editedShortTimeOffData.Alternate_Person = null;
        return;
      }

      // Store the current alternate person value to preserve it
      const currentAlternatePerson =
        preserveSelected && vm.alternatePerson ? vm.alternatePerson : null;

      // Prepare API request
      const apiObj = {
        url:
          vm.baseUrl +
          "default/employee-info/list-approver-details/employeeId/" +
          vm.employeeId +
          "/formName/Short Time Off",
        type: "GET",
        dataType: "json",
      };

      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          // Initialize empty arrays as default
          vm.listAlternativeDetails = [];
          vm.settingsList = [];
          this.minDateToApply = res.minimumDateToApply;

          // Check for SettingsList in the response
          // First check if it's directly in the response
          if (res && res.SettingsList && Array.isArray(res.SettingsList)) {
            // Store the settings list
            vm.settingsList = res.SettingsList;
          }
          // Then check if it's in the fieldValidation object
          else if (
            res &&
            res.fieldValidation &&
            res.fieldValidation.SettingsList &&
            Array.isArray(res.fieldValidation.SettingsList)
          ) {
            // Store the settings list
            vm.settingsList = res.fieldValidation.SettingsList;
          }

          // Update requestForOptions with the values from settingsList if available
          if (vm.settingsList && vm.settingsList.length > 0) {
            vm.requestForOptions = vm.settingsList.map((setting) => ({
              value: setting,
              text: setting,
            }));

            // Set default Request For based on available options
            if (!vm.isEdit) {
              vm.setDefaultRequestFor();
            }
          }

          // Check if response and alternatePersonDetails exist and are valid
          if (res && Array.isArray(res.alternatePersonDetails)) {
            // Map the response with null checks
            const mappedList = [];
            for (const item of res.alternatePersonDetails) {
              if (item) {
                const entry = {
                  Employee_Name: item.Employee_Name || null,
                  Manager_Id: parseInt(item.Manager_Id) || null,
                };
                // Only add entries with valid Manager_Id
                if (entry.Manager_Id !== null) {
                  mappedList.push(entry);
                }
              }
            }
            vm.listAlternativeDetails = mappedList;

            // Handle the alternate person selection
            if (preserveSelected && currentAlternatePerson) {
              try {
                // Convert to string for safe comparison
                const alternatePersonId = String(currentAlternatePerson);
                let found = false;

                // Check if the alternate person exists in the list
                for (const item of vm.listAlternativeDetails) {
                  if (
                    item &&
                    item.Manager_Id &&
                    String(item.Manager_Id) === alternatePersonId
                  ) {
                    found = true;
                    break;
                  }
                }

                // Set values based on whether the person was found
                if (found) {
                  vm.editedShortTimeOffData.Alternate_Person =
                    currentAlternatePerson;
                } else {
                  vm.editedShortTimeOffData.Alternate_Person = null;
                }
              } catch (error) {
                vm.editedShortTimeOffData.Alternate_Person = null;
              }
            }
            // If in edit mode with an alternate person, verify it exists
            else if (vm.isEdit && vm.alternatePerson) {
              try {
                const alternatePersonId = String(vm.alternatePerson);
                let found = false;

                // Check if the alternate person exists in the list
                for (const item of vm.listAlternativeDetails) {
                  if (
                    item &&
                    item.Manager_Id &&
                    String(item.Manager_Id) === alternatePersonId
                  ) {
                    found = true;
                    break;
                  }
                }

                // If not found, reset the values
                if (!found) {
                  vm.editedShortTimeOffData.Alternate_Person = null;
                }
              } catch (error) {
                vm.editedShortTimeOffData.Alternate_Person = null;
              }
            }
          } else {
            vm.editedShortTimeOffData.Alternate_Person = null;
          }
        })
        .catch(() => {
          // If preserving selected value, restore it; otherwise reset
          if (preserveSelected && currentAlternatePerson) {
            vm.editedShortTimeOffData.Alternate_Person = currentAlternatePerson;
          } else {
            vm.editedShortTimeOffData.Alternate_Person = null;
          }
          vm.listAlternativeDetails = [];

          vm.showAlert({
            isOpen: true,
            type: "warning",
            message: "Failed to load alternate person details",
          });
        })
        .finally(() => {
          vm.nameChangeLoading = false;
        });
    },

    validateDateTimeFields() {
      // Reset validation errors
      this.errorsMessages = [];

      // 1. Start Date Validation
      if (!this.editedShortTimeOffData.Start_Date) {
        this.errorsMessages.push("Start date is required");
        return false;
      }

      // 2. Auto-sync End Date if only Start Date is set
      if (
        this.editedShortTimeOffData.Start_Date &&
        !this.editedShortTimeOffData.End_Date
      ) {
        this.editedShortTimeOffData.End_Date =
          this.editedShortTimeOffData.Start_Date;
        this.endDate = this.editedShortTimeOffData.Start_Date;
        this.formattedEndDate = this.formatDate(
          this.editedShortTimeOffData.Start_Date
        );
      }

      // 3. Time validation - ensure both times are provided
      if (
        !this.editedShortTimeOffData.Start_Time ||
        !this.editedShortTimeOffData.End_Time
      ) {
        if (!this.editedShortTimeOffData.Start_Time) {
          this.errorsMessages.push("Start time is required");
        }
        if (!this.editedShortTimeOffData.End_Time) {
          this.errorsMessages.push("End time is required");
        }
        return false;
      }

      // Validate time format and fix if needed
      if (
        this.editedShortTimeOffData.Start_Time &&
        !this.isValidTimeFormat(this.editedShortTimeOffData.Start_Time)
      ) {
        // Try to fix the format
        try {
          const fixedTime = moment(this.editedShortTimeOffData.Start_Time, [
            "HH:mm",
            "HHmm",
            "H:mm",
          ]).format("HH:mm");
          if (this.isValidTimeFormat(fixedTime)) {
            this.editedShortTimeOffData.Start_Time = fixedTime;
            this.startTime = fixedTime;
            this.formattedStartTime = fixedTime;
          } else {
            this.errorsMessages.push("Start time format is invalid");
            return false;
          }
        } catch (e) {
          this.errorsMessages.push("Start time format is invalid");
          return false;
        }
      }

      if (
        this.editedShortTimeOffData.End_Time &&
        !this.isValidTimeFormat(this.editedShortTimeOffData.End_Time)
      ) {
        // Try to fix the format
        try {
          const fixedTime = moment(this.editedShortTimeOffData.End_Time, [
            "HH:mm",
            "HHmm",
            "H:mm",
          ]).format("HH:mm");
          if (this.isValidTimeFormat(fixedTime)) {
            this.editedShortTimeOffData.End_Time = fixedTime;
            this.endTime = fixedTime;
            this.formattedEndTime = fixedTime;
          } else {
            this.errorsMessages.push("End time format is invalid");
            return false;
          }
        } catch (e) {
          this.errorsMessages.push("End time format is invalid");
          return false;
        }
      }

      // 4. Create moment objects for start and end dates/times for comparison
      const startDateTime = moment(
        `${moment(this.editedShortTimeOffData.Start_Date).format(
          "YYYY-MM-DD"
        )} ${this.editedShortTimeOffData.Start_Time}`
      );

      const endDateTime = moment(
        `${moment(this.editedShortTimeOffData.End_Date).format("YYYY-MM-DD")} ${
          this.editedShortTimeOffData.End_Time
        }`
      );

      // 5. Chronological check - end date/time must be after start date/time
      if (endDateTime.isSameOrBefore(startDateTime)) {
        // Check if dates are the same
        const isSameDate = moment(
          this.editedShortTimeOffData.Start_Date
        ).isSame(moment(this.editedShortTimeOffData.End_Date), "day");

        const errorMessage = isSameDate
          ? "End time should be greater than start time"
          : "End date should be greater than start date";

        this.errorsMessages.push(errorMessage);
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: errorMessage,
        });

        return false;
      }

      // 6. 24-Hour boundary check
      const hoursDiff = endDateTime.diff(startDateTime, "hours");
      if (hoursDiff >= 24) {
        const errorMessage = "Short time off should be within 24 hours";
        this.errorsMessages.push(errorMessage);
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: errorMessage,
        });
        return false;
      }

      // 7. Calculate duration
      const duration = moment.duration(endDateTime.diff(startDateTime));

      // Format hours and minutes
      const totalHours = Math.floor(duration.asHours());
      const totalMinutes = duration.minutes();

      // Format as HH:mm for display
      const formattedTimeString = `${totalHours
        .toString()
        .padStart(2, "0")}:${totalMinutes.toString().padStart(2, "0")}`;

      // Also keep decimal value for calculations
      const formattedHoursDecimal = parseFloat(
        (totalHours + totalMinutes / 60).toFixed(2)
      );

      // 8. Update total hours values
      this.editedShortTimeOffData.Total_Hours = formattedHoursDecimal;
      this.totalHours = formattedHoursDecimal;
      this.formattedTotalHours = formattedTimeString; // Add this to data section if not already there

      // 9. Validate against max hours rule if available
      if (this.shortTimeOffRules && this.shortTimeOffRules.maxHours) {
        const maxHours = parseFloat(this.shortTimeOffRules.maxHours);
        if (formattedHoursDecimal > maxHours) {
          const errorMessage = `Short time off cannot exceed ${maxHours} hours per request`;
          this.errorsMessages.push(errorMessage);
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: errorMessage,
          });
          return false;
        }
      }

      // 10. Validate against min minutes rule if available
      if (this.shortTimeOffRules && this.shortTimeOffRules.minMinutes) {
        const minMinutes = parseFloat(this.shortTimeOffRules.minMinutes);
        const durationMinutes = duration.asMinutes();
        if (durationMinutes < minMinutes) {
          const errorMessage = `Short time off must be at least ${minMinutes} minutes`;
          this.errorsMessages.push(errorMessage);
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: errorMessage,
          });
          return false;
        }
      }

      return true;
    },

    calculateTotalHours() {
      if (this.validateDateTimeFields()) {
        // All validations passed, total hours already calculated in validateDateTimeFields
        return this.totalHours;
      } else {
        // Validation failed, reset total hours
        this.editedShortTimeOffData.Total_Hours = 0;
        this.totalHours = 0;
        this.formattedTotalHours = "00:00";
        return 0;
      }
    },

    resetFormValues(isEmployeeChange = true) {
      // Preserve the employee ID
      const employeeId = this.employeeId;

      // If employee is changed, set Request For to "Permission"
      // Otherwise, preserve the current Request For value
      const requestFor = isEmployeeChange ? "Permission" : this.requestFor;

      // Reset all form values
      this.requestFor = requestFor;
      this.startDate = null;
      this.startTime = null;
      this.startDateTime = null;
      this.endDate = null;
      this.endTime = null;
      this.endDateTime = null;
      this.totalHours = 0;
      this.reason = null;
      this.alternatePerson = null;
      this.documentUpload = [];

      // Reset formatted fields
      this.formattedStartDate = "";
      this.formattedStartTime = "";
      this.formattedEndDate = "";
      this.formattedEndTime = "";
      this.formattedTotalHours = "00:00";

      // Reset the editedShortTimeOffData object
      this.editedShortTimeOffData = {
        Employee_Id: employeeId,
        Request_For: requestFor,
        Start_Date: null,
        Start_Time: null,
        Start_Date_Time: null,
        End_Date: null,
        End_Time: null,
        End_Date_Time: null,
        Total_Hours: 0,
        Reason: null,
        Alternate_Person: null,
        formattedStartDate: "",
        formattedEndDate: "",
        disabledSubmitButton: false,
      };

      // Reset balance and rules
      this.shortTimeOffBalance = null;
      this.shortTimeOffRules = null;
    },

    onChangeFieldType(value, type) {
      this.isFormDirty = true;

      // Update the corresponding field in editedShortTimeOffData
      if (type === "Employee_Id" && value) {
        // Store the new employee ID
        this.editedShortTimeOffData.Employee_Id = value;
        this.employeeId = value;

        // Employee ID is now set

        // If not in edit mode, reset all form values when employee changes
        if (!this.isEdit) {
          this.resetFormValues(true); // true = employee change, reset everything except Employee ID
        }

        this.listEmployeeDetails();
        // When employee changes, update alternate person list
        this.updateAlternatePerson();
      } else if (type === "Request_For") {
        // Store the previous value
        const previousRequestFor = this.editedShortTimeOffData.Request_For;

        // Update the value
        this.editedShortTimeOffData.Request_For = value;
        this.requestFor = value;

        // If the value has changed and not in edit mode, reset other form values
        if (previousRequestFor !== value && !this.isEdit) {
          this.resetFormValues(false); // false = Request_For change, preserve both Employee ID and Request_For
        }
      } else if (type === "Start_Date_Time") {
        // Store the combined date-time value
        this.editedShortTimeOffData.Start_Date_Time = value;

        // Extract date and time components for backward compatibility
        if (value) {
          const dateObj = moment(value);
          const dateStr = dateObj.format("YYYY-MM-DD");
          const timeStr = dateObj.format("HH:mm");

          // Update the separate date and time fields
          this.editedShortTimeOffData.Start_Date = dateStr;
          this.editedShortTimeOffData.Start_Time = timeStr;
          this.startDate = dateStr;
          this.startTime = timeStr;
          this.formattedStartDate = this.formatDate(dateStr);
          this.formattedStartTime = timeStr;

          // Auto-sync End Date with Start Date
          this.syncEndDateWithStartDate(dateStr);

          // Call getShortTimeOffBalance when the date-time is filled
          this.getShortTimeOffBalance();
        }
      } else if (type === "Start_Date") {
        this.editedShortTimeOffData.Start_Date = value;
        this.startDate = value;
        this.formattedStartDate = this.formatDate(value);

        // Auto-sync End Date with Start Date
        this.syncEndDateWithStartDate(value);

        // Call getShortTimeOffBalance when both start date and time are filled
        if (value && this.editedShortTimeOffData.Start_Time) {
          this.getShortTimeOffBalance();
        }
      } else if (type === "End_Date_Time") {
        // Store the combined date-time value
        this.editedShortTimeOffData.End_Date_Time = value;

        // Extract date and time components for backward compatibility
        if (value) {
          const dateObj = moment(value);
          const dateStr = dateObj.format("YYYY-MM-DD");
          const timeStr = dateObj.format("HH:mm");

          // Update the separate date and time fields
          this.editedShortTimeOffData.End_Date = dateStr;
          this.editedShortTimeOffData.End_Time = timeStr;
          this.endDate = dateStr;
          this.endTime = timeStr;
          this.formattedEndDate = this.formatDate(dateStr);
          this.formattedEndTime = timeStr;

          // Recalculate total hours when end date/time changes
          this.calculateTotalHours();
        }
      } else if (type === "End_Date") {
        this.editedShortTimeOffData.End_Date = value;
        this.endDate = value;
        this.formattedEndDate = this.formatDate(value);
      } else if (type === "Start_Time") {
        // Ensure time is in HH:MM format
        let formattedTime = value;
        if (value && !value.includes(":")) {
          formattedTime = moment(value, "HHmm").format("HH:mm");
        }
        this.editedShortTimeOffData.Start_Time = formattedTime;
        this.startTime = formattedTime;
        this.formattedStartTime = formattedTime;

        // Call getShortTimeOffBalance when both start date and time are filled
        if (this.editedShortTimeOffData.Start_Date && formattedTime) {
          this.getShortTimeOffBalance();
        }
      } else if (type === "End_Time") {
        // Ensure time is in HH:MM format
        let formattedTime = value;
        if (value && !value.includes(":")) {
          formattedTime = moment(value, "HHmm").format("HH:mm");
        }
        this.editedShortTimeOffData.End_Time = formattedTime;
        this.endTime = formattedTime;
        this.formattedEndTime = formattedTime;
      } else if (type === "Reason") {
        this.reason = value;
        this.editedShortTimeOffData.Reason = value;
      } else if (type === "Alternate_Person") {
        try {
          // Set both the component data and the form data
          if (value === null || value === undefined) {
            this.alternatePerson = null;
            this.editedShortTimeOffData.Alternate_Person = null;
          } else {
            this.alternatePerson = value;
            this.editedShortTimeOffData.Alternate_Person = value;
          }
          // Force the CustomSelect component to update
          this.$nextTick(() => {
            if (this.$refs.Alternate_Person) {
              this.$refs.Alternate_Person.assignSelectedItem();
            }
          });
        } catch (error) {
          this.alternatePerson = null;
          this.editedShortTimeOffData.Alternate_Person = null;
        }
      }

      if (
        type === "Start_Date" ||
        type === "End_Date" ||
        type === "Start_Time" ||
        type === "End_Time" ||
        type === "Start_Date_Time" ||
        type === "End_Date_Time"
      ) {
        this.calculateTotalHours();
      }
    },

    async validateCustomFields() {
      const { valid } = await this.$refs.addEditFormValidator.validate();
      if (valid && this.errorsMessages.length == 0) {
        // Skip balance check in edit mode
        if (!this.isEdit) {
          // Check if balance is available for the selected month
          if (!this.shortTimeOffBalance) {
            // If balance hasn't been fetched yet, fetch it now
            await new Promise((resolve) => {
              this.getShortTimeOffBalance();
              // Wait for balance to be fetched (with a timeout)
              const checkInterval = setInterval(() => {
                if (!this.balanceLoading) {
                  clearInterval(checkInterval);
                  resolve();
                }
              }, 100);
              // Timeout after 3 seconds
              setTimeout(() => {
                clearInterval(checkInterval);
                resolve();
              }, 3000);
            });
          }

          // Now check the balance
          if (
            this.shortTimeOffBalance ===
            "0 Request(s) (Including current request)"
          ) {
            const startDate = moment(
              this.editedShortTimeOffData.Start_Date ||
                this.editedShortTimeOffData.Start_Date_Time
            ).format("MMMM YYYY");
            this.showAlert({
              isOpen: true,
              type: "warning",
              message: `No short time off balance available for ${startDate}.`,
            });
            return;
          }
        }

        this.addEditShortTimeOffRequest();
      }
    },

    async addEditShortTimeOffRequest() {
      let vm = this;
      vm.isLoading = true;

      let isUpdate = !!vm.isEdit;
      try {
        // Format total hours as HH:MM
        const totalHoursValue =
          parseFloat(vm.editedShortTimeOffData.Total_Hours) || 0;
        const totalHours = Math.floor(totalHoursValue);
        const totalMinutes = Math.round((totalHoursValue - totalHours) * 60);
        const formattedTotalHrs = `${totalHours
          .toString()
          .padStart(2, "0")}:${totalMinutes.toString().padStart(2, "0")}`;

        // Ensure Start_Date_Time and End_Date_Time are updated with the latest values
        if (
          vm.editedShortTimeOffData.Start_Date &&
          vm.editedShortTimeOffData.Start_Time
        ) {
          const formattedStartDate = moment(
            vm.editedShortTimeOffData.Start_Date
          ).format("YYYY-MM-DD");
          vm.editedShortTimeOffData.Start_Date_Time = `${formattedStartDate} ${vm.editedShortTimeOffData.Start_Time}`;
        }

        if (
          vm.editedShortTimeOffData.End_Date &&
          vm.editedShortTimeOffData.End_Time
        ) {
          const formattedEndDate = moment(
            vm.editedShortTimeOffData.End_Date
          ).format("YYYY-MM-DD");
          vm.editedShortTimeOffData.End_Date_Time = `${formattedEndDate} ${vm.editedShortTimeOffData.End_Time}`;
        }

        // Prepare API request object
        const apiObj = {
          url: `${
            vm.baseUrl
          }employees/short-time-off/update-short-time-off/shortTimeOffId/${
            isUpdate ? vm.selectedItem.Short_Time_Off_Id : 0
          }/istheme/ShortTimeOff`,
          type: "POST",
          dataType: "json",
          data: null, // Will be set below
        };

        // Format the date-time strings for the API
        const startDateTime =
          vm.editedShortTimeOffData.Start_Date &&
          vm.editedShortTimeOffData.Start_Time
            ? moment(
                `${moment(vm.editedShortTimeOffData.Start_Date).format(
                  "YYYY-MM-DD"
                )} ${vm.editedShortTimeOffData.Start_Time}`
              ).format("YYYY-MM-DD HH:mm:ss")
            : null;

        const endDateTime =
          vm.editedShortTimeOffData.End_Date &&
          vm.editedShortTimeOffData.End_Time
            ? moment(
                `${moment(vm.editedShortTimeOffData.End_Date).format(
                  "YYYY-MM-DD"
                )} ${vm.editedShortTimeOffData.End_Time}`
              ).format("YYYY-MM-DD HH:mm:ss")
            : null;

        // Prepare data in the required format
        const requestData = {
          shortTimeOffId: isUpdate ? vm.selectedItem.Short_Time_Off_Id : 0,
          employeeId: parseInt(vm.editedShortTimeOffData.Employee_Id) || null,
          forwardTo: vm.forwardedTo || "", // Use the numeric ID
          requestFor: vm.editedShortTimeOffData.Request_For || null,
          startDateTime: startDateTime,
          endDateTime: endDateTime,
          reason: vm.editedShortTimeOffData.Reason || "",
          totalHrs: formattedTotalHrs, // Use formatted HH:MM string
          contactNumber: vm.editedShortTimeOffData.Contact_No || "0000000000",
          alternatePerson: vm.editedShortTimeOffData.Alternate_Person
            ? vm.editedShortTimeOffData.Alternate_Person.toString()
            : null, // Use the alternate person ID as string
          status: isUpdate ? vm.selectedItem.Approval_Status : "Applied", // Only use Applied for new records
          comments: vm.editedShortTimeOffData.Comment || "",
        };

        // Update the API object with the correctly formatted data
        apiObj.data = requestData;

        const response = await vm.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );

        if (response?.success) {
          let snackbarData = {
            isOpen: true,
            type: "success",
            message: isUpdate
              ? "Short time off request updated successfully."
              : "Short time off request added successfully.",
          };
          vm.showAlert(snackbarData);
          // Emit both events to ensure compatibility with all parent components
          vm.$emit("form-updated");
          vm.$emit("edit-updated");

          // Close the form window after successful submission
          vm.$emit("close-form");
        } else {
          // Handle error response
          let errorMessage = response?.msg
            ? response.msg
            : "Something went wrong. Please try after some time.";

          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: errorMessage,
          };
          vm.showAlert(snackbarData);
        }
      } catch (err) {
        vm.$store.dispatch("handleApiErrors", {
          error: err,
          action: "warning",
          form: "short time off",
          isListError: false,
        });
      } finally {
        vm.isLoading = false;
      }
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    isValidTimeFormat(timeStr) {
      // Check if the time string is in HH:MM format
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
      return timeRegex.test(timeStr);
    },

    // Helper method to sync end date with start date
    syncEndDateWithStartDate(startDate) {
      if (startDate) {
        this.editedShortTimeOffData.End_Date = startDate;
        this.endDate = startDate;
        this.formattedEndDate = this.formatDate(startDate);
      }
    },

    getShortTimeOffBalance() {
      let vm = this;
      vm.balanceLoading = true;

      // Reset balance values first to ensure we don't use stale data
      vm.shortTimeOffBalance = null;
      vm.shortTimeOffRules = null;

      // Format start date and time if available
      let startDateTime = null;
      if (
        vm.editedShortTimeOffData.Start_Date &&
        vm.editedShortTimeOffData.Start_Time
      ) {
        // Format the date explicitly to YYYY-MM-DD before combining with time
        const formattedDate = moment(
          vm.editedShortTimeOffData.Start_Date
        ).format("YYYY-MM-DD");
        const formattedTime = vm.editedShortTimeOffData.Start_Time;

        // Combine date and time in the format "YYYY-MM-DD HH:mm:ss"
        startDateTime = `${formattedDate} ${formattedTime}:00`;
      } else if (vm.editedShortTimeOffData.Start_Date_Time) {
        // Use the combined date-time field if available
        startDateTime = moment(
          vm.editedShortTimeOffData.Start_Date_Time
        ).format("YYYY-MM-DD HH:mm:ss");
      }

      // Get employee ID - checking multiple possible sources
      const employeeId =
        vm.employeeId ||
        vm.editedShortTimeOffData.Employee_Id ||
        (vm.selectedItem ? vm.selectedItem.Employee_Id : null);

      if (!employeeId) {
        vm.balanceLoading = false;
        vm.showAlert({
          isOpen: true,
          type: "warning",
          message: "Employee ID is required to check balance",
        });
        return;
      }

      const apiObj = {
        url: vm.baseUrl + "employees/short-time-off/get-short-time-off-balance",
        type: "POST",
        dataType: "json",
        data: {
          shortTimeOffId:
            vm.isEdit && vm.selectedItem
              ? vm.selectedItem.Short_Time_Off_Id
              : 0,
          employeeId: parseInt(employeeId),
          totalHours: vm.formattedTotalHours || vm.totalHours || 0,
          startDateTime: startDateTime,
        },
      };

      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res) {
            // Directly use the response values
            vm.shortTimeOffBalance = res.shortTimeOffBalance || null;
            vm.shortTimeOffRules = res.shortTimeOffRules || null;
            // Update the UI with the retrieved values
          } else {
            vm.shortTimeOffBalance = null;
            vm.shortTimeOffRules = null;
          }
          vm.balanceLoading = false;
        })
        .catch(() => {
          vm.balanceLoading = false;
          vm.shortTimeOffBalance = null;
          vm.shortTimeOffRules = null;

          vm.showAlert({
            isOpen: true,
            type: "warning",
            message: "Failed to load short time off balance and rules",
          });
        });
    },
  },
};
</script>
<style scoped>
#integration-form > .v-overlay__content {
  height: 100%;
  width: 50%;
}
.fixed-title {
  position: sticky;
  top: 0;
  z-index: 10;
}
.overlay-card {
  height: 100vh;
  width: 40vw;
  overflow-y: auto;
}

.leaveBalanceDiv {
  box-shadow: rgba(17, 17, 26, 0.1) 0px 4px 16px,
    rgba(17, 17, 26, 0.1) 0px 8px 24px, rgba(17, 17, 26, 0.1) 0px 16px 56px;
}
</style>
