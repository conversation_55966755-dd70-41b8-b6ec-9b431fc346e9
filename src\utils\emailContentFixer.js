/**
 * Email Content Fixer Utility
 * Universal function to fix email formatting issues
 *
 * Usage:
 * import { fixEmailContent } from '@/utils/emailContentFixer';
 * const fixedContent = fixEmailContent(originalContent);
 */

/**
 * Main function to fix email content formatting issues
 * @param {string} htmlContent - The original HTML content
 * @returns {string} - Fixed HTML content
 */
export function fixEmailContent(htmlContent) {
  if (!htmlContent || typeof htmlContent !== "string") {
    return htmlContent;
  }

  let fixedContent = htmlContent;

  // Apply all fixes in sequence
  fixedContent = removeRichTextEditorArtifacts(fixedContent);
  fixedContent = fixListFormatting(fixedContent);
  fixedContent = addEmailStyling(fixedContent);
  fixedContent = fixGeneralFormatting(fixedContent);

  return fixedContent;
}

/**
 * Remove rich text editor artifacts that cause display issues
 */
function removeRichTextEditorArtifacts(content) {
  return (
    content
      // Remove Quill editor UI elements
      .replace(/<span class="ql-ui"[^>]*><\/span>/g, "")
      .replace(/<span class="ql-ui"[^>]*>.*?<\/span>/g, "")

      // Remove contenteditable attributes
      .replace(/contenteditable="[^"]*"/g, "")

      // Remove empty spans and divs that serve no purpose
      .replace(/<span[^>]*><\/span>/g, "")
      .replace(/<div[^>]*><\/div>/g, "")

      // Clean up multiple consecutive spaces
      .replace(/\s+/g, " ")
      .trim()
  );
}

/**
 * Fix list formatting issues - the main problem you're facing
 */
function fixListFormatting(content) {
  // Fix ordered lists with center alignment
  content = content.replace(/<ol([^>]*)>/g, (match, attributes) => {
    // Remove any text-align: center from existing styles
    let cleanAttributes = attributes.replace(
      /style="[^"]*text-align:\s*center[^"]*"/g,
      ""
    );
    // Remove existing style attribute completely to avoid conflicts
    cleanAttributes = cleanAttributes.replace(/style="[^"]*"/g, "");
    return `<ol${cleanAttributes} style="text-align: left; margin: 15px 0; padding-left: 30px; list-style-position: outside;">`;
  });

  // Fix unordered lists with center alignment
  content = content.replace(/<ul([^>]*)>/g, (match, attributes) => {
    let cleanAttributes = attributes.replace(
      /style="[^"]*text-align:\s*center[^"]*"/g,
      ""
    );
    cleanAttributes = cleanAttributes.replace(/style="[^"]*"/g, "");
    return `<ul${cleanAttributes} style="text-align: left; margin: 15px 0; padding-left: 30px; list-style-position: outside;">`;
  });

  // Fix list items with center alignment - this is crucial for your issue
  content = content.replace(/<li([^>]*)>/g, (match, attributes) => {
    // Remove any center alignment from list items
    let cleanAttributes = attributes.replace(
      /style="[^"]*text-align:\s*center[^"]*"/g,
      ""
    );
    cleanAttributes = cleanAttributes.replace(/style="[^"]*"/g, "");
    return `<li${cleanAttributes} style="margin: 8px 0; padding: 4px 0; text-align: left; line-height: 1.5;">`;
  });

  return content;
}

/**
 * Add proper email styling for better compatibility
 */
function addEmailStyling(content) {
  // Email-safe CSS styles
  const emailStyles = `
    <style type="text/css">
      /* Reset and base styles for email clients */
      .email-wrapper { 
        max-width: 600px; 
        margin: 0 auto; 
        font-family: Arial, Helvetica, sans-serif; 
        line-height: 1.6; 
        color: #333333;
        padding: 20px;
      }
      .email-wrapper p { 
        margin: 12px 0; 
        padding: 0;
      }
      .email-wrapper ol, .email-wrapper ul { 
        text-align: left !important; 
        margin: 15px 0; 
        padding-left: 30px !important;
        list-style-position: outside;
      }
      .email-wrapper li { 
        margin: 8px 0; 
        padding: 4px 0; 
        text-align: left !important; 
        line-height: 1.5;
      }
      .email-wrapper a { 
        color: #0066cc; 
        text-decoration: underline; 
      }
      .email-wrapper strong { 
        font-weight: bold; 
      }
      .email-wrapper img { 
        max-width: 100%; 
        height: auto; 
        display: block;
      }
      
      /* Outlook specific fixes */
      table { 
        border-collapse: collapse; 
        mso-table-lspace: 0pt; 
        mso-table-rspace: 0pt; 
      }
      
      /* Mobile responsive */
      @media only screen and (max-width: 600px) {
        .email-wrapper { 
          width: 100% !important; 
          padding: 10px !important; 
        }
        .email-wrapper ol, .email-wrapper ul { 
          padding-left: 20px !important; 
        }
      }
    </style>
  `;

  // Check if content already has wrapper or styles
  const hasWrapper =
    content.includes("email-wrapper") || content.includes("max-width: 600px");

  if (!hasWrapper) {
    // Wrap content in email wrapper
    content = `
      ${emailStyles}
      <div class="email-wrapper">
        ${content}
      </div>
    `;
  } else if (!content.includes("<style>")) {
    // Add styles if wrapper exists but styles don't
    content = emailStyles + content;
  }

  return content;
}

/**
 * Fix general formatting issues
 */
function fixGeneralFormatting(content) {
  return (
    content
      // Fix multiple consecutive <br> tags
      .replace(/(<br\s*\/?>){3,}/g, "<br><br>")

      // Fix empty paragraphs
      .replace(/<p[^>]*>\s*<\/p>/g, "")

      // Ensure proper spacing around paragraphs
      .replace(
        /<p(?![^>]*style=)([^>]*)>/g,
        '<p$1 style="margin: 12px 0; padding: 0;">'
      )

      // Fix images without proper styling
      .replace(
        /<img([^>]*?)(?!.*style=)([^>]*)>/g,
        '<img$1 style="max-width: 100%; height: auto; display: block;"$2>'
      )

      // Clean up extra whitespace but preserve intentional spacing
      .replace(/>\s+</g, "><")
      .trim()
  );
}

/**
 * Vue.js mixin for easy integration
 */
export const EmailContentFixerMixin = {
  methods: {
    fixEmailContent(htmlContent) {
      return fixEmailContent(htmlContent);
    },

    prepareEmailForSending(emailContent) {
      return fixEmailContent(emailContent);
    },
  },
};

/**
 * Quick fix specifically for your list numbering issue
 */
export function quickFixListNumbering(htmlContent) {
  if (!htmlContent) return htmlContent;

  return (
    htmlContent
      // Remove center alignment from lists
      .replace(
        /(<ol[^>]*?)style="[^"]*text-align:\s*center[^"]*"([^>]*>)/g,
        '$1style="text-align: left; padding-left: 30px;"$2'
      )
      .replace(
        /(<ul[^>]*?)style="[^"]*text-align:\s*center[^"]*"([^>]*>)/g,
        '$1style="text-align: left; padding-left: 30px;"$2'
      )
      .replace(
        /(<li[^>]*?)style="[^"]*text-align:\s*center[^"]*"([^>]*>)/g,
        '$1style="text-align: left;"$2'
      )
      // Remove ql-ui spans
      .replace(/<span class="ql-ui"[^>]*><\/span>/g, "")
  );
}

// Default export
export default {
  fixEmailContent,
  EmailContentFixerMixin,
  quickFixListNumbering,
};
