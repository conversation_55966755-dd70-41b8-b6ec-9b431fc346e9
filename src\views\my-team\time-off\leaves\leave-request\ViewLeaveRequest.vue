<template>
  <v-overlay
    :model-value="showViewForm"
    @click:outside="onCloseOverlay()"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        :style="
          windowWidth <= 1264
            ? 'width:100vw; height: 100vh'
            : 'width:40vw; height: 100vh'
        "
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center fixed-title"
        >
          <div class="text-h6 text-medium ps-2">View {{ landedFormName }}</div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseOverlay()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="card mb-3 px-0" style="overflow-y: auto">
          <div class="d-flex justify-end align-center">
            <v-btn
              v-if="presentEditButton"
              @click="onOpenEditForm()"
              class="mr-3 mt-3 text-primary"
              variant="text"
              rounded="lg"
            >
              <v-icon class="mr-1" size="15">fas fa-edit</v-icon>Edit
            </v-btn>
          </div>
          <div class="px-6 py-2">
            <v-row>
              <v-col
                v-if="callingFrom?.toLowerCase() === `myteam`"
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 my-3"
              >
                <div class="text-subtitle-1 text-grey-darken-1">
                  Employee Id
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.User_Defined_EmpId) }}
                  </section>
                </div>
              </v-col>
              <v-col
                v-if="callingFrom?.toLowerCase() === `myteam`"
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 my-3"
              >
                <div class="text-subtitle-1 text-grey-darken-1">Employee</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Employee_Name) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Leave</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Leave_Name) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Reason</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Reason) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Duration</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Duration) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Period</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Leave_Period) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">From</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.StartDate) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">To</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.EndDate) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Total Day(s)
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Total_Days) }}
                  </section>
                </div>
              </v-col>
              <!-- Alternate Person -->
              <v-col
                v-if="labelList[428]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 my-3"
              >
                <div class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[428].Field_Alias }}
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Alternate_Person_Name) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Late Arrival
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ selectedItem?.Late_Attendance ? "Yes" : "No" }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Late Arrival Hours
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Late_Attendance_Hours) }}
                    <p
                      class="text-body-4 text-red"
                      v-if="selectedItem.Late_Attendance_Time"
                    >
                      {{ selectedItem.Late_Attendance_Time }}
                    </p>
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Early Checkout
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Early_Checkout) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Early Checkout Hours
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Early_Checkout_Hours) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Status</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Approval_Status) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Documents</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2 text-truncate">
                    <template v-if="selectedItem?.Document_File_Path?.length">
                      <div
                        v-for="(
                          filePath, index
                        ) in selectedItem.Document_File_Path"
                        :key="index"
                      >
                        <v-btn
                          @click="retrieveFileContents(filePath)"
                          variant="text"
                          color="primary"
                          style="text-decoration-underline"
                        >
                          {{ extractFileName(filePath) }}
                        </v-btn>
                      </div>
                    </template>
                    <span v-else>-</span>
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Comment</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Comment?.join(", ")) }}
                  </section>
                </div>
              </v-col>
            </v-row>
            <v-row class="px-sm-8 px-md-10 my-4 d-flex justify-center">
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails> </v-col
            ></v-row>
          </div> </v-card-text></v-card></template
  ></v-overlay>
  <FilePreviewModal
    v-if="openDocumentModal"
    :fileName="retrievedFileName"
    folderName="Leave Documents"
    fileRetrieveType="documents"
    @close-preview-modal="(openDocumentModal = false), (retrievedFileName = '')"
  />
</template>

<script>
import { checkNullValue } from "@/helper";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import { defineAsyncComponent } from "vue";
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);
export default {
  name: "ViewLeaveRequest",
  data() {
    return {
      moreDetailsList: [],
      openMoreDetails: true,
      showViewForm: true,
      openDocumentModal: false,
      retrievedFileName: "",
    };
  },
  props: {
    selectedItem: {
      type: Object,
      default: () => {},
    },
    landedFormName: {
      type: String,
      required: true,
    },
    callingFrom: {
      type: String,
      required: true,
    },
    formAccess: {
      type: Object,
      required: true,
    },
  },
  emits: ["close-view-form", "open-edit-form"],
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    presentEditButton() {
      let {
        Late_Attendance,
        Approval_Status,
        Approver_Id,
        Restrict_Employee_To_Apply,
        Added_By,
        Employee_Id,
        Curr_Year,
        M,
        H,
      } = this.selectedItem;
      let editableStatus = ["Applied", "Approved", "Returned"];
      let showEdit =
        (this.formAccess.admin == "admin" ||
          (Restrict_Employee_To_Apply === 0 &&
            ([Added_By, Employee_Id].includes(this.loginEmployeeId) ||
              Approver_Id == this.loginEmployeeId))) &&
        editableStatus.includes(Approval_Status) &&
        Curr_Year == "Current" &&
        !(M == 1 || H == 1) &&
        this.formAccess?.update &&
        Late_Attendance == 0;
      return showEdit;
    },
  },
  components: {
    MoreDetails,
    FilePreviewModal,
  },
  mounted() {
    this.prefillMoreDetails();
  },
  methods: {
    extractFileName(filePath) {
      let fileName = filePath.split("?")[3] || ""; // Extracting file name from `?`-separated format
      return fileName?.length > 25 ? fileName.slice(0, 22) + "..." : fileName;
    },
    retrieveFileContents(doc) {
      this.retrievedFileName = doc;
      this.openDocumentModal = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    checkNullValue,
    onOpenEditForm() {
      this.showViewForm = false;
      this.$emit("open-edit-form");
    },
    onCloseOverlay() {
      this.showViewForm = false;
      this.$emit("close-view-form");
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.selectedItem.Added_On,
        addedByName = this.selectedItem.Added_By_Name,
        updatedByName = this.selectedItem.Updated_By_Name,
        updatedOn = this.selectedItem.Updated_On;

      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>

<style scoped>
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
.fixed-title {
  position: sticky;
  top: 0;
  z-index: 10;
}
</style>
